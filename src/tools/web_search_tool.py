import os
import json
import requests
import hashlib
import time
from typing import List, Dict, Any, Optional
from config.settings import Config

try:
    from exa_py import Exa
    EXA_AVAILABLE = True
except ImportError:
    EXA_AVAILABLE = False
    print("⚠️  exa_py未安装，将使用智博API。安装命令: pip install exa_py")

class WebSearchTool:
    """智能Web搜索工具，支持Exa AI和智博API，优先使用Exa AI"""

    def __init__(self, config: Config = None):
        self.config = config or Config()
        self.api_url = self.config.SEARCH_API_URL
        self.api_key = self.config.SEARCH_API_KEY
        self.cache_dir = self.config.SEARCH_CACHE_DIR
        self.search_mode = self.config.SEARCH_MODE

        # 设置Exa AI API密钥
        self.exa_api_key = "715b14b0-a64c-48ca-a799-89983090bf00"

        # 检查是否使用Exa AI
        self.use_exa = EXA_AVAILABLE and self.exa_api_key

        # Exa AI速率限制控制 - 优化为更快的间隔
        self.last_exa_request_time = 0
        self.exa_min_interval = 0.1  # 减少到100ms间隔，提高速度

        if self.use_exa:
            print("🚀 使用Exa AI搜索引擎（高质量）")
            self.exa_client = Exa(api_key=self.exa_api_key)
        else:
            print("🔍 使用智博API搜索引擎")

        os.makedirs(self.cache_dir, exist_ok=True)
    
    def _get_cache_path(self, query: str) -> str:
        """生成查询缓存路径"""
        query_hash = hashlib.md5(query.encode()).hexdigest()
        return os.path.join(self.cache_dir, f"{query_hash}.json")
    
    def _is_cached(self, query: str) -> bool:
        """检查查询是否已缓存"""
        cache_path = self._get_cache_path(query)
        return os.path.exists(cache_path)
    
    def _get_cached_results(self, query: str) -> Optional[List[Dict]]:
        """获取缓存的搜索结果"""
        if not self._is_cached(query):
            return None
        
        cache_path = self._get_cache_path(query)
        try:
            with open(cache_path, "r", encoding="utf-8") as f:
                cached_data = json.load(f)
                cache_time = cached_data.get("timestamp", 0)
                current_time = time.time()
                
                if current_time - cache_time > self.config.CACHE_EXPIRY_HOURS * 3600:
                    return None
                
                return cached_data.get("results", [])
        except (json.JSONDecodeError, IOError):
            return None
    
    def _cache_results(self, query: str, results: List[Dict]):
        """缓存搜索结果"""
        cache_path = self._get_cache_path(query)
        cache_data = {
            "timestamp": time.time(),
            "query": query,
            "results": results
        }
        
        with open(cache_path, "w", encoding="utf-8") as f:
            json.dump(cache_data, f, ensure_ascii=False, indent=2)

    def _search_exa(self, query: str, num_results: int = 10) -> List[Dict]:
        """使用Exa AI搜索，带速率限制"""
        if not self.use_exa:
            return []

        try:
            # 速率限制控制
            current_time = time.time()
            time_since_last_request = current_time - self.last_exa_request_time

            if time_since_last_request < self.exa_min_interval:
                sleep_time = self.exa_min_interval - time_since_last_request
                print(f"⏱️  Exa AI速率限制，等待 {sleep_time:.2f} 秒...")
                time.sleep(sleep_time)

            # 更新最后请求时间
            self.last_exa_request_time = time.time()

            # 使用Exa AI进行搜索
            response = self.exa_client.search(
                query=query,
                num_results=min(num_results, 10),
                include_domains=None,
                exclude_domains=None,
                start_crawl_date=None,
                end_crawl_date=None,
                start_published_date=None,
                end_published_date=None,
                use_autoprompt=True,  # 让Exa优化查询
                type="neural"  # 使用神经搜索
            )

            results = []
            for result in response.results:
                formatted_result = {
                    "title": result.title or "",
                    "url": result.url or "",
                    "snippet": result.text[:500] if result.text else "",  # 限制摘要长度
                    "displayUrl": result.url or "",
                    "siteName": "",
                    "datePublished": result.published_date or ""
                }
                results.append(formatted_result)

            return results

        except Exception as e:
            print(f"🚀 Exa AI搜索失败: {e}")
            return []

    def _search_api(self, query: str, num_results: int = 10) -> List[Dict]:
        """调用搜索API"""
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        payload = {
            "query": query,
            "summary": True,
            "count": num_results
        }

        try:
            # 添加延迟避免API限制 - 优化延迟时间
            time.sleep(0.3)  # 减少到300ms延迟，提高速度
            response = requests.post(self.api_url, headers=headers, json=payload, timeout=30)
            response.raise_for_status()

            # 解析响应
            result_data = response.json()

            # 根据博查AI的API响应格式提取结果
            if result_data.get("code") == 200 and "data" in result_data:
                data = result_data["data"]
                if "webPages" in data and "value" in data["webPages"]:
                    results = data["webPages"]["value"]

                    # 转换为标准格式
                    formatted_results = []
                    for item in results:
                        formatted_result = {
                            "title": item.get("name", ""),
                            "url": item.get("url", ""),
                            "snippet": item.get("snippet", "") or item.get("summary", ""),
                            "displayUrl": item.get("displayUrl", ""),
                            "siteName": item.get("siteName", ""),
                            "datePublished": item.get("datePublished", "")
                        }
                        formatted_results.append(formatted_result)

                    return formatted_results
                else:
                    print(f"API响应中没有找到webPages.value: {data}")
                    return []
            else:
                print(f"API响应错误，code: {result_data.get('code')}, msg: {result_data.get('msg')}")
                return []

        except requests.RequestException as e:
            print(f"搜索API调用失败: {e}")
            return []
        except json.JSONDecodeError as e:
            print(f"搜索API响应解析失败: {e}")
            return []
    
    def _perform_search(self, query: str, num_results: int = 10) -> List[Dict]:
        """执行实际搜索，优先使用Exa AI"""
        if self.use_exa:
            # 尝试Exa AI搜索
            results = self._search_exa(query, num_results)
            if results:
                return results
            else:
                print("🔄 Exa AI搜索失败，回退到智博API...")
                return self._search_api(query, num_results)
        else:
            return self._search_api(query, num_results)

    def search(self, query: str, num_results: int = 10) -> List[Dict]:
        """执行Web搜索，支持多种缓存策略"""
        if self.search_mode == "cache_only":
            return self._get_cached_results(query) or []

        elif self.search_mode == "cache_first":
            cached_results = self._get_cached_results(query)
            if cached_results:
                return cached_results

            results = self._perform_search(query, num_results)
            if results:
                self._cache_results(query, results)
            return results

        elif self.search_mode == "search_only":
            return self._perform_search(query, num_results)

        elif self.search_mode == "cache_and_search":
            cached_results = self._get_cached_results(query)
            fresh_results = self._perform_search(query, num_results)

            # 确保cached_results是列表
            if not isinstance(cached_results, list):
                cached_results = []

            # 确保fresh_results是列表
            if not isinstance(fresh_results, list):
                fresh_results = []

            if fresh_results:
                self._cache_results(query, fresh_results)

            # 合并结果，去重
            seen_urls = set()
            combined_results = []

            for result in fresh_results + cached_results:
                url = result.get("url", "")
                if url and url not in seen_urls:
                    seen_urls.add(url)
                    combined_results.append(result)

            return combined_results[:num_results]

        return self._perform_search(query, num_results)
