# src/models/data_models.py
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from enum import Enum

class ReportType(Enum):
    """报告类型枚举"""
    COMPANY = "company"
    INDUSTRY = "industry"
    MACRO = "macro"

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class DataSource(Enum):
    """数据源类型枚举"""
    WEB_SEARCH = "web_search"
    API = "api"
    DATABASE = "database"
    FILE = "file"
    RAG = "rag"

@dataclass
class SearchResult:
    """搜索结果数据模型"""
    url: str
    title: str
    snippet: str
    source: str = ""
    relevance_score: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "url": self.url,
            "title": self.title,
            "snippet": self.snippet,
            "source": self.source,
            "relevance_score": self.relevance_score,
            "timestamp": self.timestamp.isoformat()
        }

@dataclass
class ContentData:
    """内容数据模型"""
    content: str
    url: str
    title: str = ""
    content_type: str = "text"
    language: str = "zh"
    quality_score: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    extracted_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "content": self.content,
            "url": self.url,
            "title": self.title,
            "content_type": self.content_type,
            "language": self.language,
            "quality_score": self.quality_score,
            "metadata": self.metadata,
            "extracted_at": self.extracted_at.isoformat()
        }

@dataclass
class FinancialMetrics:
    """财务指标数据模型"""
    revenue: Optional[float] = None
    profit: Optional[float] = None
    total_assets: Optional[float] = None
    net_assets: Optional[float] = None
    market_cap: Optional[float] = None
    pe_ratio: Optional[float] = None
    pb_ratio: Optional[float] = None
    roe: Optional[float] = None
    roa: Optional[float] = None
    debt_ratio: Optional[float] = None
    current_ratio: Optional[float] = None
    gross_margin: Optional[float] = None
    net_margin: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {k: v for k, v in self.__dict__.items() if v is not None}

@dataclass
class CompanyInfo:
    """公司信息数据模型"""
    name: str
    stock_code: str = ""
    industry: str = ""
    sector: str = ""
    description: str = ""
    established_date: Optional[datetime] = None
    headquarters: str = ""
    website: str = ""
    employees: Optional[int] = None
    financial_metrics: Optional[FinancialMetrics] = None
    business_segments: List[str] = field(default_factory=list)
    key_products: List[str] = field(default_factory=list)
    competitors: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        data = {
            "name": self.name,
            "stock_code": self.stock_code,
            "industry": self.industry,
            "sector": self.sector,
            "description": self.description,
            "headquarters": self.headquarters,
            "website": self.website,
            "employees": self.employees,
            "business_segments": self.business_segments,
            "key_products": self.key_products,
            "competitors": self.competitors
        }
        
        if self.established_date:
            data["established_date"] = self.established_date.isoformat()
        
        if self.financial_metrics:
            data["financial_metrics"] = self.financial_metrics.to_dict()
        
        return data

@dataclass
class IndustryInfo:
    """行业信息数据模型"""
    name: str
    description: str = ""
    market_size: Optional[float] = None
    growth_rate: Optional[float] = None
    market_segments: List[str] = field(default_factory=list)
    key_players: List[str] = field(default_factory=list)
    key_trends: List[str] = field(default_factory=list)
    regulatory_environment: str = ""
    technology_trends: List[str] = field(default_factory=list)
    investment_opportunities: List[str] = field(default_factory=list)
    risks: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "description": self.description,
            "market_size": self.market_size,
            "growth_rate": self.growth_rate,
            "market_segments": self.market_segments,
            "key_players": self.key_players,
            "key_trends": self.key_trends,
            "regulatory_environment": self.regulatory_environment,
            "technology_trends": self.technology_trends,
            "investment_opportunities": self.investment_opportunities,
            "risks": self.risks
        }

@dataclass
class MacroIndicators:
    """宏观指标数据模型"""
    gdp_growth: Optional[float] = None
    inflation_rate: Optional[float] = None
    unemployment_rate: Optional[float] = None
    interest_rate: Optional[float] = None
    exchange_rate: Optional[float] = None
    fiscal_deficit: Optional[float] = None
    trade_balance: Optional[float] = None
    stock_index: Optional[float] = None
    commodity_prices: Dict[str, float] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {k: v for k, v in self.__dict__.items() if v is not None}

@dataclass
class TaskInfo:
    """任务信息数据模型"""
    task_id: str
    task_type: ReportType
    status: TaskStatus
    target: str  # 目标公司/行业/主题
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    progress: float = 0.0
    error_message: str = ""
    result_path: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        data = {
            "task_id": self.task_id,
            "task_type": self.task_type.value,
            "status": self.status.value,
            "target": self.target,
            "created_at": self.created_at.isoformat(),
            "progress": self.progress,
            "error_message": self.error_message,
            "result_path": self.result_path,
            "metadata": self.metadata
        }
        
        if self.started_at:
            data["started_at"] = self.started_at.isoformat()
        
        if self.completed_at:
            data["completed_at"] = self.completed_at.isoformat()
        
        return data

@dataclass
class ReportData:
    """报告数据模型"""
    report_type: ReportType
    target: str
    title: str
    executive_summary: str = ""
    main_content: str = ""
    key_findings: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    risks: List[str] = field(default_factory=list)
    data_sources: List[str] = field(default_factory=list)
    charts: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "report_type": self.report_type.value,
            "target": self.target,
            "title": self.title,
            "executive_summary": self.executive_summary,
            "main_content": self.main_content,
            "key_findings": self.key_findings,
            "recommendations": self.recommendations,
            "risks": self.risks,
            "data_sources": self.data_sources,
            "charts": self.charts,
            "created_at": self.created_at.isoformat(),
            "metadata": self.metadata
        }

@dataclass
class APIResponse:
    """API响应数据模型"""
    success: bool
    data: Any = None
    error_message: str = ""
    status_code: int = 200
    response_time: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "success": self.success,
            "data": self.data,
            "error_message": self.error_message,
            "status_code": self.status_code,
            "response_time": self.response_time,
            "timestamp": self.timestamp.isoformat()
        }

@dataclass
class CacheEntry:
    """缓存条目数据模型"""
    key: str
    value: Any
    created_at: datetime = field(default_factory=datetime.now)
    expires_at: Optional[datetime] = None
    access_count: int = 0
    last_accessed: datetime = field(default_factory=datetime.now)
    tags: List[str] = field(default_factory=list)
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.expires_at is None:
            return False
        return datetime.now() > self.expires_at
    
    def to_dict(self) -> Dict[str, Any]:
        data = {
            "key": self.key,
            "value": self.value,
            "created_at": self.created_at.isoformat(),
            "access_count": self.access_count,
            "last_accessed": self.last_accessed.isoformat(),
            "tags": self.tags
        }
        
        if self.expires_at:
            data["expires_at"] = self.expires_at.isoformat()
        
        return data

@dataclass
class AgentMetrics:
    """Agent性能指标数据模型"""
    agent_name: str
    task_count: int = 0
    success_count: int = 0
    failure_count: int = 0
    average_response_time: float = 0.0
    total_processing_time: float = 0.0
    memory_usage: float = 0.0
    cpu_usage: float = 0.0
    error_rate: float = 0.0
    last_activity: datetime = field(default_factory=datetime.now)
    
    def calculate_success_rate(self) -> float:
        """计算成功率"""
        if self.task_count == 0:
            return 0.0
        return (self.success_count / self.task_count) * 100
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "agent_name": self.agent_name,
            "task_count": self.task_count,
            "success_count": self.success_count,
            "failure_count": self.failure_count,
            "success_rate": self.calculate_success_rate(),
            "average_response_time": self.average_response_time,
            "total_processing_time": self.total_processing_time,
            "memory_usage": self.memory_usage,
            "cpu_usage": self.cpu_usage,
            "error_rate": self.error_rate,
            "last_activity": self.last_activity.isoformat()
        }

# 工厂函数
def create_search_result(url: str, title: str, snippet: str, **kwargs) -> SearchResult:
    """创建搜索结果对象"""
    return SearchResult(url=url, title=title, snippet=snippet, **kwargs)

def create_company_info(name: str, **kwargs) -> CompanyInfo:
    """创建公司信息对象"""
    return CompanyInfo(name=name, **kwargs)

def create_task_info(task_id: str, task_type: ReportType, target: str, **kwargs) -> TaskInfo:
    """创建任务信息对象"""
    return TaskInfo(task_id=task_id, task_type=task_type, target=target, **kwargs)

def create_report_data(report_type: ReportType, target: str, title: str, **kwargs) -> ReportData:
    """创建报告数据对象"""
    return ReportData(report_type=report_type, target=target, title=title, **kwargs)

# 验证函数
def validate_company_info(company_info: CompanyInfo) -> List[str]:
    """验证公司信息的完整性"""
    errors = []
    
    if not company_info.name:
        errors.append("公司名称不能为空")
    
    if company_info.stock_code and len(company_info.stock_code) < 4:
        errors.append("股票代码格式不正确")
    
    if company_info.website and not company_info.website.startswith(('http://', 'https://')):
        errors.append("网站地址格式不正确")
    
    if company_info.employees is not None and company_info.employees < 0:
        errors.append("员工数量不能为负数")
    
    return errors

def validate_financial_metrics(metrics: FinancialMetrics) -> List[str]:
    """验证财务指标的合理性"""
    errors = []
    
    if metrics.pe_ratio is not None and metrics.pe_ratio < 0:
        errors.append("市盈率不能为负数")
    
    if metrics.pb_ratio is not None and metrics.pb_ratio < 0:
        errors.append("市净率不能为负数")
    
    if metrics.roe is not None and (metrics.roe < -100 or metrics.roe > 100):
        errors.append("ROE超出合理范围")
    
    if metrics.debt_ratio is not None and (metrics.debt_ratio < 0 or metrics.debt_ratio > 1):
        errors.append("资产负债率应在0-1之间")
    
    return errors
