import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import pickle
import json
import os
import time
from typing import List, Dict, Any, Tuple
from config.settings import Config

class RAGSystem:
    """检索增强生成系统，支持文档存储、向量化和语义搜索"""
    
    def __init__(self, config: Config = None):
        self.config = config or Config()
        self.vector_db_path = self.config.VECTOR_DB_DIR
        os.makedirs(self.vector_db_path, exist_ok=True)
        
        # 文件路径
        self.documents_file = os.path.join(self.vector_db_path, "documents.json")
        self.vectorizer_file = os.path.join(self.vector_db_path, "vectorizer.pkl")
        self.vectors_file = os.path.join(self.vector_db_path, "vectors.npy")
        
        # 初始化组件
        self.documents = self._load_documents()
        self.vectorizer = self._load_vectorizer()
        self.document_vectors = self._load_vectors()
    
    def _load_documents(self) -> List[Dict[str, Any]]:
        """加载文档数据"""
        if os.path.exists(self.documents_file):
            try:
                with open(self.documents_file, "r", encoding="utf-8") as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError):
                return []
        return []
    
    def _save_documents(self):
        """保存文档数据"""
        with open(self.documents_file, "w", encoding="utf-8") as f:
            json.dump(self.documents, f, ensure_ascii=False, indent=2)
    
    def _load_vectorizer(self):
        """加载TF-IDF向量化器"""
        if os.path.exists(self.vectorizer_file):
            try:
                with open(self.vectorizer_file, "rb") as f:
                    return pickle.load(f)
            except (pickle.PickleError, IOError):
                pass
        
        # 创建新的向量化器
        return TfidfVectorizer(
            max_features=10000,
            stop_words='english',
            ngram_range=(1, 2),
            max_df=0.95,
            min_df=1  # 改为1，避免文档数量少时的错误
        )
    
    def _save_vectorizer(self):
        """保存向量化器"""
        with open(self.vectorizer_file, "wb") as f:
            pickle.dump(self.vectorizer, f)
    
    def _load_vectors(self):
        """加载文档向量"""
        if os.path.exists(self.vectors_file):
            try:
                return np.load(self.vectors_file)
            except IOError:
                pass
        return np.array([])
    
    def _save_vectors(self):
        """保存文档向量"""
        if self.document_vectors.size > 0:
            np.save(self.vectors_file, self.document_vectors)
    
    def add_document(self, content: str, metadata: Dict[str, Any] = None):
        """添加文档到知识库"""
        if metadata is None:
            metadata = {}
        
        # 创建文档记录
        document = {
            "id": len(self.documents),
            "content": content,
            "metadata": metadata,
            "timestamp": time.time()
        }
        
        self.documents.append(document)
        
        # 重新构建向量索引
        self._rebuild_index()
        
        # 保存数据
        self._save_documents()
        self._save_vectorizer()
        self._save_vectors()
    
    def _rebuild_index(self):
        """重建向量索引"""
        if not self.documents:
            return

        # 提取所有文档内容
        document_texts = [doc["content"] for doc in self.documents]

        try:
            # 拟合向量化器并转换文档
            self.document_vectors = self.vectorizer.fit_transform(document_texts).toarray()
        except ValueError as e:
            if "max_df corresponds to < documents than min_df" in str(e):
                # 如果文档太少，创建一个更宽松的向量化器
                print(f"文档数量太少({len(document_texts)})，使用简化的向量化器")
                self.vectorizer = TfidfVectorizer(
                    max_features=1000,
                    ngram_range=(1, 1),
                    min_df=1,
                    max_df=1.0
                )
                self.document_vectors = self.vectorizer.fit_transform(document_texts).toarray()
            else:
                raise e
    
    def search(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """语义搜索相关文档"""
        if not self.documents or self.document_vectors.size == 0:
            return []
        
        # 向量化查询
        query_vector = self.vectorizer.transform([query]).toarray()
        
        # 计算相似度
        similarities = cosine_similarity(query_vector, self.document_vectors)[0]
        
        # 获取Top-K结果
        top_indices = np.argsort(similarities)[::-1][:top_k]
        
        results = []
        for idx in top_indices:
            if similarities[idx] > 0.1:  # 相似度阈值
                doc = self.documents[idx]
                results.append({
                    "id": doc["id"],
                    "content": doc["content"],
                    "metadata": doc["metadata"],
                    "similarity": float(similarities[idx])
                })
        
        return results
    
    def get_relevant_context(self, query: str, max_length: int = 4000) -> str:
        """获取查询相关的上下文信息"""
        relevant_docs = self.search(query, top_k=10)
        
        context_parts = []
        current_length = 0
        
        for doc in relevant_docs:
            content = doc["content"]
            if current_length + len(content) > max_length:
                # 截断内容以适应长度限制
                remaining_length = max_length - current_length
                content = content[:remaining_length] + "..."
                context_parts.append(content)
                break
            
            context_parts.append(content)
            current_length += len(content)
        
        return "\n\n".join(context_parts)
