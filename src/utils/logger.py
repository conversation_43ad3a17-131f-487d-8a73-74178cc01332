# src/utils/logger.py
import logging
import os
import sys
from datetime import datetime
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from typing import Optional
from config.settings import Config

class ColoredFormatter(logging.Formatter):
    """带颜色的日志格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        log_color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        record.levelname = f"{log_color}{record.levelname}{self.COLORS['RESET']}"
        
        # 格式化消息
        return super().format(record)

class FinancialLogger:
    """
    金融研报系统专用日志器
    """
    
    def __init__(self, 
                 name: str = "FinancialReportAgent",
                 config: Config = None):
        self.name = name
        self.config = config or Config()
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志器"""
        logger = logging.getLogger(self.name)
        logger.setLevel(getattr(logging, self.config.LOG_LEVEL))
        
        # 避免重复添加处理器
        if logger.handlers:
            logger.handlers.clear()
        
        # 创建日志目录
        log_dir = "data/logs"
        os.makedirs(log_dir, exist_ok=True)
        
        # 文件处理器 - 按大小轮转
        file_handler = RotatingFileHandler(
            filename=os.path.join(log_dir, f"{self.name}.log"),
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        
        # 错误日志处理器 - 按时间轮转
        error_handler = TimedRotatingFileHandler(
            filename=os.path.join(log_dir, f"{self.name}_error.log"),
            when='D',
            interval=1,
            backupCount=30,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        
        # 格式化器
        detailed_formatter = logging.Formatter(
            fmt='%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        simple_formatter = logging.Formatter(
            fmt='%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        colored_formatter = ColoredFormatter(
            fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 设置格式化器
        file_handler.setFormatter(detailed_formatter)
        error_handler.setFormatter(detailed_formatter)
        console_handler.setFormatter(colored_formatter)
        
        # 添加处理器
        logger.addHandler(file_handler)
        logger.addHandler(error_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def debug(self, message: str, **kwargs):
        """调试日志"""
        self.logger.debug(message, extra=kwargs)
    
    def info(self, message: str, **kwargs):
        """信息日志"""
        self.logger.info(message, extra=kwargs)
    
    def warning(self, message: str, **kwargs):
        """警告日志"""
        self.logger.warning(message, extra=kwargs)
    
    def error(self, message: str, exception: Exception = None, **kwargs):
        """错误日志"""
        if exception:
            self.logger.error(f"{message} - Exception: {str(exception)}", exc_info=True, extra=kwargs)
        else:
            self.logger.error(message, extra=kwargs)
    
    def critical(self, message: str, **kwargs):
        """严重错误日志"""
        self.logger.critical(message, extra=kwargs)
    
    def log_performance(self, operation: str, duration: float, **metrics):
        """性能日志"""
        self.logger.info(
            f"Performance - {operation}: {duration:.3f}s",
            extra={"operation": operation, "duration": duration, **metrics}
        )
    
    def log_api_call(self, api_name: str, status: str, response_time: float = None, **details):
        """API调用日志"""
        message = f"API Call - {api_name}: {status}"
        if response_time:
            message += f" ({response_time:.3f}s)"
        
        self.logger.info(message, extra={"api": api_name, "status": status, **details})
    
    def log_data_processing(self, step: str, input_count: int, output_count: int, **details):
        """数据处理日志"""
        self.logger.info(
            f"Data Processing - {step}: {input_count} -> {output_count}",
            extra={"step": step, "input_count": input_count, "output_count": output_count, **details}
        )
    
    def log_agent_action(self, agent_name: str, action: str, status: str, **details):
        """Agent动作日志"""
        self.logger.info(
            f"Agent Action - {agent_name}: {action} - {status}",
            extra={"agent": agent_name, "action": action, "status": status, **details}
        )

# 全局日志器实例
_loggers = {}

def get_logger(name: str = "FinancialReportAgent") -> FinancialLogger:
    """
    获取日志器实例
    
    Args:
        name: 日志器名称
        
    Returns:
        日志器实例
    """
    if name not in _loggers:
        _loggers[name] = FinancialLogger(name)
    
    return _loggers[name]

# 便捷函数
def log_debug(message: str, **kwargs):
    """调试日志"""
    get_logger().debug(message, **kwargs)

def log_info(message: str, **kwargs):
    """信息日志"""
    get_logger().info(message, **kwargs)

def log_warning(message: str, **kwargs):
    """警告日志"""
    get_logger().warning(message, **kwargs)

def log_error(message: str, exception: Exception = None, **kwargs):
    """错误日志"""
    get_logger().error(message, exception, **kwargs)

def log_critical(message: str, **kwargs):
    """严重错误日志"""
    get_logger().critical(message, **kwargs)

# 装饰器
def log_execution_time(operation_name: str = None):
    """记录函数执行时间的装饰器"""
    def decorator(func):
        import time
        from functools import wraps
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            operation = operation_name or f"{func.__module__}.{func.__name__}"
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                get_logger().log_performance(operation, duration, status="success")
                return result
            except Exception as e:
                duration = time.time() - start_time
                get_logger().log_performance(operation, duration, status="error", error=str(e))
                raise
        
        return wrapper
    return decorator

def log_api_calls(api_name: str = None):
    """记录API调用的装饰器"""
    def decorator(func):
        import time
        from functools import wraps
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            api = api_name or f"{func.__module__}.{func.__name__}"
            
            try:
                result = await func(*args, **kwargs)
                response_time = time.time() - start_time
                get_logger().log_api_call(api, "success", response_time)
                return result
            except Exception as e:
                response_time = time.time() - start_time
                get_logger().log_api_call(api, "error", response_time, error=str(e))
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            api = api_name or f"{func.__module__}.{func.__name__}"
            
            try:
                result = func(*args, **kwargs)
                response_time = time.time() - start_time
                get_logger().log_api_call(api, "success", response_time)
                return result
            except Exception as e:
                response_time = time.time() - start_time
                get_logger().log_api_call(api, "error", response_time, error=str(e))
                raise
        
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator
