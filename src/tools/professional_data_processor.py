# src/tools/professional_data_processor.py
"""
专业数据处理器
基于世界级投行标准处理财务数据和生成专业表格
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
import re
import json
from datetime import datetime, timedelta

class ProfessionalDataProcessor:
    """专业数据处理器类"""
    
    def __init__(self):
        self.financial_metrics = {
            "profitability": ["ROE", "ROA", "ROIC", "Gross Margin", "Operating Margin", "Net Margin"],
            "liquidity": ["Current Ratio", "Quick Ratio", "Cash Ratio", "Working Capital"],
            "leverage": ["Debt/Equity", "Debt/Assets", "Interest Coverage", "EBITDA/Interest"],
            "efficiency": ["Asset Turnover", "Inventory Turnover", "Receivables Turnover"],
            "valuation": ["P/E", "P/B", "EV/EBITDA", "P/S", "PEG Ratio"]
        }
        
        self.industry_benchmarks = {
            "technology": {"ROE": 15.2, "P/E": 28.5, "Gross Margin": 65.3},
            "healthcare": {"ROE": 12.8, "P/E": 22.1, "Gross Margin": 58.7},
            "financial": {"ROE": 11.5, "P/E": 12.8, "Net Margin": 18.2},
            "consumer": {"ROE": 14.1, "P/E": 18.9, "Gross Margin": 42.1},
            "industrial": {"ROE": 13.6, "P/E": 16.7, "Gross Margin": 35.8}
        }
    
    def extract_financial_data(self, content: str) -> Dict[str, Any]:
        """从文本内容中提取财务数据"""
        financial_data = {
            "revenue": self._extract_revenue_data(content),
            "profitability": self._extract_profitability_data(content),
            "balance_sheet": self._extract_balance_sheet_data(content),
            "cash_flow": self._extract_cash_flow_data(content),
            "ratios": self._extract_financial_ratios(content),
            "valuation": self._extract_valuation_data(content)
        }
        
        return financial_data
    
    def _extract_revenue_data(self, content: str) -> Dict[str, Any]:
        """提取营收数据"""
        revenue_patterns = [
            r'营收[：:]?\s*(\d+(?:\.\d+)?)\s*([亿万千百]?)元',
            r'收入[：:]?\s*(\d+(?:\.\d+)?)\s*([亿万千百]?)元',
            r'revenue[：:]?\s*\$?(\d+(?:\.\d+)?)\s*(billion|million|thousand)?',
            r'营业收入[：:]?\s*(\d+(?:\.\d+)?)\s*([亿万千百]?)元'
        ]
        
        revenue_data = {}
        for pattern in revenue_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                for match in matches:
                    value = float(match[0])
                    unit = match[1].lower() if len(match) > 1 else ""
                    
                    # 单位转换
                    if unit in ['亿', 'billion']:
                        value *= 100  # 转换为千万
                    elif unit in ['万', 'million']:
                        value *= 1
                    
                    revenue_data['amount'] = value
                    revenue_data['unit'] = '万元' if unit in ['万', 'million'] else '亿元'
                    break
        
        # 提取增长率
        growth_patterns = [
            r'增长[：:]?\s*(\d+(?:\.\d+)?)\s*%',
            r'同比[：:]?\s*(\d+(?:\.\d+)?)\s*%',
            r'growth[：:]?\s*(\d+(?:\.\d+)?)\s*%'
        ]
        
        for pattern in growth_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                revenue_data['growth_rate'] = float(matches[0])
                break
        
        return revenue_data
    
    def _extract_profitability_data(self, content: str) -> Dict[str, Any]:
        """提取盈利能力数据"""
        profitability_data = {}
        
        # 净利润
        profit_patterns = [
            r'净利润[：:]?\s*(\d+(?:\.\d+)?)\s*([亿万千百]?)元',
            r'net\s+income[：:]?\s*\$?(\d+(?:\.\d+)?)\s*(billion|million)?'
        ]
        
        for pattern in profit_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                value = float(matches[0][0])
                unit = matches[0][1].lower() if len(matches[0]) > 1 else ""
                profitability_data['net_income'] = {
                    'amount': value,
                    'unit': unit
                }
                break
        
        # 毛利率
        margin_patterns = [
            r'毛利率[：:]?\s*(\d+(?:\.\d+)?)\s*%',
            r'gross\s+margin[：:]?\s*(\d+(?:\.\d+)?)\s*%'
        ]
        
        for pattern in margin_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                profitability_data['gross_margin'] = float(matches[0])
                break
        
        return profitability_data
    
    def _extract_balance_sheet_data(self, content: str) -> Dict[str, Any]:
        """提取资产负债表数据"""
        balance_sheet_data = {}
        
        # 总资产
        asset_patterns = [
            r'总资产[：:]?\s*(\d+(?:\.\d+)?)\s*([亿万千百]?)元',
            r'total\s+assets[：:]?\s*\$?(\d+(?:\.\d+)?)\s*(billion|million)?'
        ]
        
        for pattern in asset_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                value = float(matches[0][0])
                unit = matches[0][1].lower() if len(matches[0]) > 1 else ""
                balance_sheet_data['total_assets'] = {
                    'amount': value,
                    'unit': unit
                }
                break
        
        return balance_sheet_data
    
    def _extract_cash_flow_data(self, content: str) -> Dict[str, Any]:
        """提取现金流数据"""
        cash_flow_data = {}
        
        # 经营现金流
        ocf_patterns = [
            r'经营[活动产生的]*现金流[：:]?\s*(\d+(?:\.\d+)?)\s*([亿万千百]?)元',
            r'operating\s+cash\s+flow[：:]?\s*\$?(\d+(?:\.\d+)?)\s*(billion|million)?'
        ]
        
        for pattern in ocf_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                value = float(matches[0][0])
                unit = matches[0][1].lower() if len(matches[0]) > 1 else ""
                cash_flow_data['operating_cash_flow'] = {
                    'amount': value,
                    'unit': unit
                }
                break
        
        return cash_flow_data
    
    def _extract_financial_ratios(self, content: str) -> Dict[str, Any]:
        """提取财务比率"""
        ratios_data = {}
        
        # ROE
        roe_patterns = [
            r'ROE[：:]?\s*(\d+(?:\.\d+)?)\s*%',
            r'净资产收益率[：:]?\s*(\d+(?:\.\d+)?)\s*%'
        ]
        
        for pattern in roe_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                ratios_data['ROE'] = float(matches[0])
                break
        
        # P/E比率
        pe_patterns = [
            r'P/E[：:]?\s*(\d+(?:\.\d+)?)',
            r'市盈率[：:]?\s*(\d+(?:\.\d+)?)'
        ]
        
        for pattern in pe_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                ratios_data['PE'] = float(matches[0])
                break
        
        return ratios_data
    
    def _extract_valuation_data(self, content: str) -> Dict[str, Any]:
        """提取估值数据"""
        valuation_data = {}
        
        # 市值
        market_cap_patterns = [
            r'市值[：:]?\s*(\d+(?:\.\d+)?)\s*([亿万千百]?)元',
            r'market\s+cap[：:]?\s*\$?(\d+(?:\.\d+)?)\s*(billion|million)?'
        ]
        
        for pattern in market_cap_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                value = float(matches[0][0])
                unit = matches[0][1].lower() if len(matches[0]) > 1 else ""
                valuation_data['market_cap'] = {
                    'amount': value,
                    'unit': unit
                }
                break
        
        return valuation_data
    
    def create_financial_summary_table(self, financial_data: Dict[str, Any], 
                                     company_name: str) -> str:
        """创建财务摘要表格"""
        
        # 构建标准财务摘要表格
        table_data = {
            "指标": [
                "营业收入 (亿元)",
                "营收增长率 (%)",
                "净利润 (亿元)", 
                "毛利率 (%)",
                "净资产收益率 (%)",
                "市盈率 (倍)",
                "总资产 (亿元)",
                "经营现金流 (亿元)"
            ],
            "数值": []
        }
        
        # 填充数据
        revenue = financial_data.get('revenue', {})
        table_data["数值"].append(f"{revenue.get('amount', 'N/A')}")
        table_data["数值"].append(f"{revenue.get('growth_rate', 'N/A')}")
        
        profitability = financial_data.get('profitability', {})
        net_income = profitability.get('net_income', {})
        table_data["数值"].append(f"{net_income.get('amount', 'N/A')}")
        table_data["数值"].append(f"{profitability.get('gross_margin', 'N/A')}")
        
        ratios = financial_data.get('ratios', {})
        table_data["数值"].append(f"{ratios.get('ROE', 'N/A')}")
        table_data["数值"].append(f"{ratios.get('PE', 'N/A')}")
        
        balance_sheet = financial_data.get('balance_sheet', {})
        total_assets = balance_sheet.get('total_assets', {})
        table_data["数值"].append(f"{total_assets.get('amount', 'N/A')}")
        
        cash_flow = financial_data.get('cash_flow', {})
        ocf = cash_flow.get('operating_cash_flow', {})
        table_data["数值"].append(f"{ocf.get('amount', 'N/A')}")
        
        # 转换为Markdown表格
        markdown_table = f"### {company_name} 财务摘要\n\n"
        markdown_table += "| 指标 | 数值 |\n"
        markdown_table += "| --- | --- |\n"
        
        for i, metric in enumerate(table_data["指标"]):
            value = table_data["数值"][i] if i < len(table_data["数值"]) else "N/A"
            markdown_table += f"| {metric} | {value} |\n"
        
        return markdown_table
    
    def create_peer_comparison_table(self, company_data: Dict[str, Any], 
                                   industry: str) -> str:
        """创建同行对比表格"""
        
        benchmarks = self.industry_benchmarks.get(industry.lower(), {})
        
        markdown_table = "### 同行业对比分析\n\n"
        markdown_table += "| 指标 | 公司数值 | 行业平均 | 相对表现 |\n"
        markdown_table += "| --- | --- | --- | --- |\n"
        
        # ROE对比
        company_roe = company_data.get('ratios', {}).get('ROE')
        industry_roe = benchmarks.get('ROE')
        if company_roe and industry_roe:
            performance = "优于行业" if company_roe > industry_roe else "低于行业"
            markdown_table += f"| 净资产收益率 (%) | {company_roe} | {industry_roe} | {performance} |\n"
        
        # P/E对比
        company_pe = company_data.get('ratios', {}).get('PE')
        industry_pe = benchmarks.get('P/E')
        if company_pe and industry_pe:
            performance = "高估值" if company_pe > industry_pe else "低估值"
            markdown_table += f"| 市盈率 (倍) | {company_pe} | {industry_pe} | {performance} |\n"
        
        return markdown_table
    
    def generate_investment_rating(self, financial_data: Dict[str, Any], 
                                 industry: str) -> Dict[str, Any]:
        """生成投资评级"""
        
        score = 0
        max_score = 100
        
        # 盈利能力评分 (30分)
        ratios = financial_data.get('ratios', {})
        roe = ratios.get('ROE', 0)
        if roe > 15:
            score += 30
        elif roe > 10:
            score += 20
        elif roe > 5:
            score += 10
        
        # 增长性评分 (25分)
        revenue = financial_data.get('revenue', {})
        growth_rate = revenue.get('growth_rate', 0)
        if growth_rate > 20:
            score += 25
        elif growth_rate > 10:
            score += 20
        elif growth_rate > 5:
            score += 15
        elif growth_rate > 0:
            score += 10
        
        # 估值评分 (25分)
        pe = ratios.get('PE', 0)
        industry_pe = self.industry_benchmarks.get(industry.lower(), {}).get('P/E', 20)
        if pe < industry_pe * 0.8:
            score += 25
        elif pe < industry_pe:
            score += 20
        elif pe < industry_pe * 1.2:
            score += 15
        
        # 财务健康度评分 (20分)
        # 这里可以加入更多财务健康指标
        score += 15  # 默认给予中等评分
        
        # 确定评级
        if score >= 80:
            rating = "强烈买入"
            recommendation = "BUY"
        elif score >= 65:
            rating = "买入"
            recommendation = "BUY"
        elif score >= 50:
            rating = "持有"
            recommendation = "HOLD"
        elif score >= 35:
            rating = "减持"
            recommendation = "SELL"
        else:
            rating = "卖出"
            recommendation = "STRONG SELL"
        
        return {
            "rating": rating,
            "recommendation": recommendation,
            "score": score,
            "max_score": max_score,
            "confidence": "中等" if score > 40 else "低"
        }
