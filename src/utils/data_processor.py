# src/utils/data_processor.py
import re
import json
from typing import Dict, List, Any, Optional
from config.settings import Config
from src.utils.logger import get_logger

class DataProcessor:
    """数据处理工具类"""

    def __init__(self, config: Config = None):
        self.config = config or Config()
        self.logger = get_logger("DataProcessor")

    def clean_text(self, text: str) -> str:
        """清理文本"""
        if not text:
            return ""

        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)

        # 移除特殊字符
        text = re.sub(r'[^\w\s\u4e00-\u9fff.,!?;:()[\]{}"\'-]', '', text)

        return text.strip()

    def extract_numbers(self, text: str) -> List[float]:
        """从文本中提取数字"""
        pattern = r'-?\d+\.?\d*'
        matches = re.findall(pattern, text)
        return [float(match) for match in matches if match]

    def format_financial_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """格式化金融数据"""
        formatted = {}

        for key, value in data.items():
            if isinstance(value, (int, float)):
                # 格式化数字
                if abs(value) >= 1e8:
                    formatted[key] = f"{value/1e8:.2f}亿"
                elif abs(value) >= 1e4:
                    formatted[key] = f"{value/1e4:.2f}万"
                else:
                    formatted[key] = f"{value:.2f}"
            else:
                formatted[key] = str(value)

        return formatted

    def validate_data(self, data: Any, required_fields: List[str] = None) -> bool:
        """验证数据完整性"""
        if not data:
            return False

        if required_fields and isinstance(data, dict):
            return all(field in data for field in required_fields)

        return True

    def merge_data(self, *data_sources: Dict[str, Any]) -> Dict[str, Any]:
        """合并多个数据源"""
        merged = {}

        for data in data_sources:
            if isinstance(data, dict):
                merged.update(data)

        return merged