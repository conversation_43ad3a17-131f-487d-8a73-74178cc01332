#!/usr/bin/env python3
"""
高级修复脚本：处理复杂的asyncio模式
"""

import os
import re
import glob

def advanced_fix_file(file_path):
    """高级修复单个文件"""
    print(f"🔧 高级修复文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 1. 修复复杂的asyncio.iscoroutinefunction模式
    pattern1 = r'if asyncio\.iscoroutinefunction\(tool_functions\[function_name\]\):\s*tool_result = await tool_functions\[function_name\]\(\*\*args\)\s*else:\s*tool_result = tool_functions\[function_name\]\(\*\*args\)'
    replacement1 = 'tool_result = tool_functions[function_name](**args)'
    content = re.sub(pattern1, replacement1, content, flags=re.MULTILINE | re.DOTALL)
    
    # 2. 修复多行的asyncio.iscoroutinefunction
    pattern2 = r'if asyncio\.iscoroutinefunction\([^)]+\):\s*[^=]+ = await [^(]+\([^)]*\)\s*else:\s*[^=]+ = [^(]+\([^)]*\)'
    replacement2 = lambda m: m.group(0).split('else:')[-1].strip()
    content = re.sub(pattern2, replacement2, content, flags=re.MULTILINE | re.DOTALL)
    
    # 3. 修复Generator类型注解
    content = re.sub(r'-> AsyncGenerator\[str, None\]', '-> Generator[str, None, None]', content)
    content = re.sub(r': AsyncGenerator\[str, None\]', ': Generator[str, None, None]', content)
    
    # 4. 修复yield from async
    content = re.sub(r'yield from await ', 'yield from ', content)
    
    # 5. 修复async with
    content = re.sub(r'async with ', 'with ', content)
    
    # 6. 修复run_in_executor模式
    pattern3 = r'loop = asyncio\.get_event_loop\(\)\s*with concurrent\.futures\.ThreadPoolExecutor\(\) as executor:\s*result = await loop\.run_in_executor\(\s*executor,\s*([^,]+),\s*([^)]+)\s*\)'
    replacement3 = r'result = \1(\2)'
    content = re.sub(pattern3, replacement3, content, flags=re.MULTILINE | re.DOTALL)
    
    # 7. 删除不必要的import
    content = re.sub(r'import concurrent\.futures\n?', '', content)
    content = re.sub(r'from typing import.*?AsyncGenerator.*?\n', lambda m: m.group(0).replace('AsyncGenerator, ', '').replace(', AsyncGenerator', ''), content)
    
    # 8. 修复LLM客户端调用
    content = re.sub(r'self\.llm_client\.chat_completion\(', 'self.llm_client.chat_completion(', content)
    
    # 9. 确保正确的import
    if 'Generator' in content and 'from typing import' in content and 'Generator' not in content.split('from typing import')[1].split('\n')[0]:
        content = re.sub(r'from typing import ([^\n]+)', r'from typing import \1, Generator', content)
    
    # 检查是否有修改
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 高级修复完成: {file_path}")
        return True
    else:
        print(f"⏭️  无需高级修改: {file_path}")
        return False

def fix_specific_patterns(file_path):
    """修复特定的复杂模式"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 修复LLM客户端中的特殊模式
    if 'llm_client.py' in file_path:
        # 删除异步客户端相关代码
        content = re.sub(r'# 初始化异步客户端.*?\n.*?self\.async_client.*?\n', '# 不再需要异步客户端\n', content, flags=re.DOTALL)
        
        # 修复async_chat_completion方法
        content = re.sub(r'def async_chat_completion\(self, messages: List\[Dict\[str, str\]\],\s*model: str = None, tools: List\[Dict\] = None, \*\*kwargs\) -> Dict\[str, Any\]:\s*"""同步聊天完成 - 去掉asyncio垃圾库"""\s*return self\.chat_completion\(messages, model, tools, \*\*kwargs\)', 
                        'def async_chat_completion(self, messages: List[Dict[str, str]], model: str = None, tools: List[Dict] = None, **kwargs) -> Dict[str, Any]:\n        """同步聊天完成 - 兼容性方法"""\n        return self.chat_completion(messages, model, tools, **kwargs)', content)
    
    # 修复Agent文件中的特殊模式
    if any(agent in file_path for agent in ['company_agent.py', 'industry_agent.py', 'macro_agent.py']):
        # 修复工具函数调用
        content = re.sub(r'# 调用工具函数 - 全部同步\s*tool_result = tool_functions\[function_name\]\(\*\*args\)', 
                        '# 调用工具函数\n                            tool_result = tool_functions[function_name](**args)', content)
    
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    return False

def main():
    """主函数"""
    print("🚀 开始高级修复asyncio问题...")
    
    # 要修复的文件模式
    patterns = [
        'src/agents/*.py',
        'src/models/*.py'
    ]
    
    fixed_count = 0
    total_count = 0
    
    for pattern in patterns:
        files = glob.glob(pattern)
        for file_path in files:
            if os.path.isfile(file_path):
                total_count += 1
                fixed1 = advanced_fix_file(file_path)
                fixed2 = fix_specific_patterns(file_path)
                if fixed1 or fixed2:
                    fixed_count += 1
    
    print(f"\n📊 高级修复完成统计:")
    print(f"总文件数: {total_count}")
    print(f"修复文件数: {fixed_count}")
    print(f"无需修改: {total_count - fixed_count}")
    
    print("\n🎉 高级修复完成！")

if __name__ == "__main__":
    main()
