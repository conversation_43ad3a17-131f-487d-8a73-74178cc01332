from concurrent.futures import ThreadPoolExecutor
from typing import Dict, List, Any, Generator
from tqdm import tqdm
import time
from src.models.llm_client import LLMClient
from src.tools.web_search_tool import WebSearchTool
from src.tools.url_content_tool import URLContentTool
from src.tools.rag_system import RAGSystem
from src.tools.enterprise_docx_generator import EnterpriseDocxGenerator
from src.tools.professional_docx_generator import ProfessionalFinancialReportGenerator
from config.settings import Config

class FinancialReportAgent:
    """金融研报生成主Agent"""
    
    def __init__(self, config: Config = None):
        self.config = config or Config()
        
        # 初始化LLM客户端
        self.llm_client = LLMClient(self.config)
        
        # 初始化工具
        self.web_search_tool = WebSearchTool(self.config)
        self.url_content_tool = URLContentTool(self.config)
        self.rag_system = RAGSystem(self.config)
        self.docx_generator = EnterpriseDocxGenerator(self.config)
        self.professional_docx_generator = ProfessionalFinancialReportGenerator(self.config)
        
        # 线程池
        self.executor = ThreadPoolExecutor(
            max_workers=self.config.MAX_CONCURRENT_TASKS
        )

        # 初始化工具函数映射
        self.tool_functions = {
            "web_search": self._tool_web_search,
            "get_url_content": self._tool_get_url_content,
            "analyze_financial_data": self._tool_analyze_financial_data
        }
    
    def generate_reports(self, targets: Dict[str, str]) -> Dict[str, str]:
        """生成所有类型的研报"""
        # 创建总进度条
        total_reports = len(targets)
        main_progress = tqdm(total=total_reports, desc="📊 生成金融研报",
                            bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}]')

        tasks = []

        # 公司研报
        if "company" in targets:
            tasks.append(self._generate_company_report(targets["company"]))

        # 行业研报
        if "industry" in targets:
            tasks.append(self._generate_industry_report(targets["industry"]))

        # 宏观研报
        if "macro" in targets:
            tasks.append(self._generate_macro_report(targets["macro"]))

        # 顺序执行（去掉asyncio）
        results = []
        for task in tasks:
            try:
                result = task  # 直接执行，不用asyncio
                results.append(result)
            except Exception as e:
                results.append(e)

        report_paths = {}
        if "company" in targets and len(results) > 0:
            report_paths["company"] = results[0]
            main_progress.update(1)
        if "industry" in targets and len(results) > 1:
            report_paths["industry"] = results[1]
            main_progress.update(1)
        if "macro" in targets and len(results) > 2:
            report_paths["macro"] = results[2]
            main_progress.update(1)

        main_progress.set_description("✅ 所有研报生成完成")
        main_progress.close()

        return report_paths
    
    def _generate_company_report(self, company_name: str) -> str:
        """生成公司研报"""
        # 创建进度条
        progress = tqdm(total=5, desc=f"📈 生成{company_name}公司研报",
                       bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}]')

        # 第一步：信息收集
        progress.set_description(f"🔍 收集{company_name}信息")
        search_queries = [
            f"{company_name} 财务报告 2023",
            f"{company_name} 业务分析",
            f"{company_name} 竞争优势",
            f"{company_name} 投资价值"
        ]

        # 并发搜索
        loop = asyncio.get_event_loop()
        search_tasks = [
            loop.run_in_executor(self.executor, self.web_search_tool.search, query)
            for query in search_queries
        ]

        search_results = asyncio.gather(*search_tasks)
        progress.update(1)

        # 第二步：内容抓取
        progress.set_description(f"📄 抓取{company_name}内容")
        all_urls = []
        for results in search_results:
            for result in results[:3]:  # 每个查询取前3个结果
                url = result.get("url")
                if url:
                    all_urls.append(url)

        # 并发抓取内容
        content_tasks = [
            loop.run_in_executor(self.executor, self.url_content_tool.get_content, url)
            for url in all_urls
        ]

        contents = asyncio.gather(*content_tasks)
        progress.update(1)

        # 第三步：构建知识库
        progress.set_description(f"🧠 构建{company_name}知识库")
        for i, content in enumerate(contents):
            if content and len(content) > 100:  # 过滤太短的内容
                self.rag_system.add_document(
                    content,
                    {"source": all_urls[i], "company": company_name, "type": "company_info"}
                )
        progress.update(1)

        # 第四步：生成报告内容
        progress.set_description(f"🔬 分析{company_name}数据")
        report_data = self._analyze_company_data(company_name)
        progress.update(1)

        # 第五步：生成专业DOCX文档
        progress.set_description(f"📝 生成{company_name}报告")
        print(f"\n📝 开始生成DOCX报告...")
        print(f"⚡ 正在优化报告数据...")

        professional_data = self._build_professional_company_data(report_data, company_name)

        print(f"📊 报告数据准备完成，开始生成文档...")
        print(f"⏱️  预计需要30-60秒，请耐心等待...")

        try:
            report_path = asyncio.wait_for(
                loop.run_in_executor(
                    self.executor,
                    self.professional_docx_generator.create_company_report,
                    professional_data
                ),
                timeout=120  # 2分钟超时
            )
            print(f"✅ DOCX报告生成完成！")
        except asyncio.TimeoutError:
            print(f"⚠️  DOCX生成超时，尝试生成简化版本...")
            simplified_data = {
                "company_name": company_name,
                "executive_summary": report_data.get("executive_summary", "执行摘要"),
                "company_overview": report_data.get("company_overview", "公司概况"),
                "financial_analysis": report_data.get("financial_analysis", "财务分析"),
                "investment_recommendation": report_data.get("investment_recommendation", "投资建议")
            }
            report_path = loop.run_in_executor(
                self.executor,
                self.docx_generator.create_company_report,
                simplified_data
            )
            print(f"✅ 简化版DOCX报告生成完成！")

        progress.update(1)

        progress.set_description(f"✅ {company_name}公司研报完成")
        progress.close()

        return report_path
    
    def _generate_industry_report(self, industry_name: str) -> str:
        """生成行业研报"""
        # 搜索行业相关信息
        search_queries = [
            f"{industry_name} 行业分析 2023",
            f"{industry_name} 市场规模",
            f"{industry_name} 竞争格局",
            f"{industry_name} 发展趋势"
        ]
        
        # 执行搜索和内容获取
        loop = asyncio.get_event_loop()
        search_tasks = [
            loop.run_in_executor(self.executor, self.web_search_tool.search, query)
            for query in search_queries
        ]
        
        search_results = asyncio.gather(*search_tasks)
        
        # 处理搜索结果并生成报告
        all_urls = []
        for results in search_results:
            for result in results[:3]:
                url = result.get("url")
                if url:
                    all_urls.append(url)
        
        content_tasks = [
            loop.run_in_executor(self.executor, self.url_content_tool.get_content, url)
            for url in all_urls
        ]
        
        contents = asyncio.gather(*content_tasks)
        
        # 构建知识库
        for i, content in enumerate(contents):
            if content and len(content) > 100:
                self.rag_system.add_document(
                    content,
                    {"source": all_urls[i], "industry": industry_name, "type": "industry_info"}
                )
        
        # 分析数据并生成报告
        report_data = self._analyze_industry_data(industry_name)

        # 生成专业行业报告
        print(f"\n📝 开始生成行业DOCX报告...")
        print(f"⚡ 正在优化报告数据...")

        professional_data = self._build_professional_industry_data(report_data, industry_name)

        print(f"📊 报告数据准备完成，开始生成文档...")
        print(f"⏱️  预计需要30-60秒，请耐心等待...")

        try:
            report_path = asyncio.wait_for(
                loop.run_in_executor(
                    self.executor,
                    self.professional_docx_generator.create_industry_report,
                    professional_data
                ),
                timeout=120  # 2分钟超时
            )
            print(f"✅ 行业DOCX报告生成完成！")
        except asyncio.TimeoutError:
            print(f"⚠️  DOCX生成超时，尝试生成简化版本...")
            simplified_data = {
                "industry_name": industry_name,
                "executive_summary": report_data.get("executive_summary", "执行摘要"),
                "industry_overview": report_data.get("industry_overview", "行业概况"),
                "market_size_analysis": report_data.get("market_size_analysis", "市场规模分析"),
                "competition_analysis": report_data.get("competition_analysis", "竞争格局分析")
            }
            report_path = loop.run_in_executor(
                self.executor,
                self.docx_generator.create_industry_report,
                simplified_data
            )
            print(f"✅ 简化版行业DOCX报告生成完成！")
        
        return report_path
    
    def _generate_macro_report(self, topic: str) -> str:
        """生成宏观研报"""
        # 搜索宏观相关信息
        search_queries = [
            f"{topic} 宏观分析",
            f"{topic} 政策影响",
            f"{topic} 投资趋势",
            f"{topic} 市场前景"
        ]
        
        # 执行搜索和内容获取
        loop = asyncio.get_event_loop()
        search_tasks = [
            loop.run_in_executor(self.executor, self.web_search_tool.search, query)
            for query in search_queries
        ]
        
        search_results = asyncio.gather(*search_tasks)
        
        # 处理搜索结果
        all_urls = []
        for results in search_results:
            for result in results[:3]:
                url = result.get("url")
                if url:
                    all_urls.append(url)
        
        content_tasks = [
            loop.run_in_executor(self.executor, self.url_content_tool.get_content, url)
            for url in all_urls
        ]
        
        contents = asyncio.gather(*content_tasks)
        
        # 构建知识库
        for i, content in enumerate(contents):
            if content and len(content) > 100:
                self.rag_system.add_document(
                    content,
                    {"source": all_urls[i], "topic": topic, "type": "macro_info"}
                )
        
        # 分析数据并生成报告
        report_data = self._analyze_macro_data(topic)

        # 生成专业宏观报告
        print(f"\n📝 开始生成宏观DOCX报告...")
        print(f"⚡ 正在优化报告数据...")

        professional_data = self._build_professional_macro_data(report_data, topic)

        print(f"📊 报告数据准备完成，开始生成文档...")
        print(f"⏱️  预计需要30-60秒，请耐心等待...")

        try:
            report_path = asyncio.wait_for(
                loop.run_in_executor(
                    self.executor,
                    self.professional_docx_generator.create_macro_report,
                    professional_data
                ),
                timeout=120  # 2分钟超时
            )
            print(f"✅ 宏观DOCX报告生成完成！")
        except asyncio.TimeoutError:
            print(f"⚠️  DOCX生成超时，尝试生成简化版本...")
            simplified_data = {
                "topic": topic,
                "executive_summary": report_data.get("executive_summary", "执行摘要"),
                "macro_environment": report_data.get("macro_environment", "宏观环境分析"),
                "policy_analysis": report_data.get("policy_analysis", "政策分析"),
                "market_trends": report_data.get("market_trends", "市场趋势")
            }
            report_path = loop.run_in_executor(
                self.executor,
                self.docx_generator.create_macro_report,
                simplified_data
            )
            print(f"✅ 简化版宏观DOCX报告生成完成！")
        
        return report_path
    
    def _analyze_company_data(self, company_name: str) -> Dict[str, Any]:
        """深度分析公司数据 - 支持多轮对话和工具调用"""
        print(f"\n🤖 开始智能分析对话 - {company_name}")

        # 初始分析提示
        initial_prompt = f"""
        你是一位资深的金融分析师，现在需要对{company_name}进行全面的投资分析。

        你可以使用以下工具来收集和分析信息：
        1. web_search - 搜索最新的公司信息
        2. get_url_content - 获取具体网页内容
        3. analyze_financial_data - 分析财务数据

        请按照以下步骤进行分析：
        1. 首先搜索公司的最新财务信息和年报数据
        2. 获取公司的业务模式和竞争优势信息
        3. 分析公司的投资价值和风险因素
        4. 基于收集的信息提供综合分析结论

        现在开始分析{company_name}，请先搜索该公司的最新财务信息。
        """

        # 进行多轮对话分析
        messages = [
            {"role": "system", "content": "你是一位专业的金融分析师，具有丰富的投资研究经验。你可以使用各种工具来收集和分析信息。"},
            {"role": "user", "content": initial_prompt}
        ]

        # 工具定义
        tools = [
            {
                "type": "function",
                "function": {
                    "name": "web_search",
                    "description": "搜索网络信息，获取公司相关资料",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "搜索查询词"},
                            "num_results": {"type": "integer", "description": "返回结果数量，默认5", "default": 5}
                        },
                        "required": ["query"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "get_url_content",
                    "description": "获取指定URL的详细内容",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "url": {"type": "string", "description": "要获取内容的URL"}
                        },
                        "required": ["url"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "analyze_financial_data",
                    "description": "分析财务数据并生成洞察",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "data": {"type": "string", "description": "财务数据内容"},
                            "analysis_type": {"type": "string", "description": "分析类型", "enum": ["profitability", "liquidity", "efficiency", "leverage", "comprehensive"]}
                        },
                        "required": ["data"]
                    }
                }
            }
        ]

        # 执行多轮对话，限制为2轮提高速度
        conversation_log = []
        for round_num in range(1, 3):
            print(f"\n{'='*60}")
            print(f"🤖 第 {round_num} 轮智能分析 - {company_name}")
            print(f"{'='*60}")

            try:
                # 使用流式输出进行对话
                full_response = ""
                for chunk in self.llm_client.chat_completion_with_tools_stream(
                    messages=messages,
                    tools=tools,
                    tool_functions=self.tool_functions,
                    max_iterations=6,
                    temperature=0.1,
                    show_thinking=False
                ):
                    print(chunk, end='', flush=True)
                    full_response += chunk

                conversation_log.append(f"第{round_num}轮: {full_response}")

                # 检查是否完成分析
                if self._is_analysis_complete(full_response):
                    print(f"\n\n✅ 分析完成！")
                    break

                # 继续下一轮分析
                if round_num < 2:
                    follow_up_prompt = self._generate_follow_up_prompt(full_response, round_num)
                    messages.append({"role": "assistant", "content": full_response})
                    messages.append({"role": "user", "content": follow_up_prompt})

                    print(f"\n\n🔄 准备进入第 {round_num + 1} 轮分析...")

            except Exception as e:
                print(f"\n❌ 第 {round_num} 轮对话异常: {str(e)}")
                break

        # 生成最终分析报告
        final_analysis = self._generate_final_company_analysis(company_name, conversation_log)

        return final_analysis

    def _is_analysis_complete(self, content: str) -> bool:
        """判断分析是否完成 - 中英双语检测"""
        completion_indicators = [
            # 中文完成标识
            "综合分析结论", "投资建议", "分析完成", "总结", "最终评估",
            "研究结论", "投资评级", "报告总结", "结论", "建议",
            "完成分析", "分析总结", "投资观点", "最终建议",

            # 英文完成标识
            "analysis complete", "investment recommendation", "conclusion",
            "final analysis", "summary", "recommendation", "complete",
            "final conclusion", "investment advice", "research conclusion",
            "overall assessment", "final assessment", "wrap up", "in conclusion"
        ]

        content_lower = content.lower()
        return any(indicator in content_lower for indicator in completion_indicators)

    def _generate_follow_up_prompt(self, previous_content: str, round_num: int) -> str:
        """生成后续分析提示"""
        follow_up_prompts = {
            1: "很好！现在请继续搜索公司的业务模式、主营业务和竞争优势信息。",
            2: "请进一步分析公司的投资价值，包括估值水平和增长前景。",
            3: "请评估公司面临的主要风险因素和挑战。",
            4: "请基于以上所有信息，提供综合的投资分析结论和建议。"
        }

        return follow_up_prompts.get(round_num, "请继续深入分析并提供更多洞察。")

    def _generate_final_company_analysis(self, company_name: str, conversation_log: List[str]) -> Dict[str, Any]:
        """生成最终公司分析报告 - 基于对话内容"""
        # 合并所有对话内容
        full_conversation = "\n\n".join(conversation_log)

        # 生成专业报告所需的所有章节
        sections_to_generate = [
            "投资要点",
            "公司概况",
            "主营业务分析",
            "财务分析",
            "估值分析",
            "盈利预测",
            "投资建议",
            "风险提示"
        ]

        report_data = {
            "company_name": company_name,
            "conversation_log": conversation_log,
            "data_sources": ["网络搜索信息", "AI智能分析", "财务数据分析"]
        }

        # 为每个章节生成详细内容
        for section in sections_to_generate:
            section_content = self._generate_section_content_from_conversation(
                company_name, section, full_conversation
            )

            # 使用标准化的键名映射
            if section == "投资要点":
                report_data["executive_summary"] = section_content
                report_data["投资要点"] = section_content
            elif section == "公司概况":
                report_data["company_overview"] = section_content
                report_data["公司概况"] = section_content
            elif section == "主营业务分析":
                report_data["business_analysis"] = section_content
                report_data["主营业务分析"] = section_content
            elif section == "财务分析":
                report_data["financial_analysis"] = section_content
                report_data["财务分析"] = section_content
            elif section == "估值分析":
                report_data["valuation_analysis"] = section_content
                report_data["估值分析"] = section_content
            elif section == "盈利预测":
                report_data["earnings_forecast"] = section_content
                report_data["盈利预测"] = section_content
            elif section == "投资建议":
                report_data["investment_recommendation"] = section_content
                report_data["投资建议"] = section_content
            elif section == "风险提示":
                report_data["risk_warning"] = section_content
                report_data["风险提示"] = section_content

        return report_data

    def _generate_section_content_from_conversation(self, company_name: str, section_name: str, conversation_context: str) -> str:
        """基于对话内容为特定章节生成详细内容"""
        prompt = f"""
        基于以下分析对话内容，为{company_name}撰写{section_name}：

        分析对话记录：
        {conversation_context}

        请基于对话中收集的信息，撰写专业的{section_name}内容。
        要求：内容专业、客观，严格基于对话中的真实数据。
        """

        response = self.llm_client.chat_completion([
            {"role": "system", "content": "你是一位资深的证券分析师，具有丰富的公司研究经验。请严格基于提供的对话内容生成分析报告。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    def _generate_company_investment_summary(self, company_name: str, financial_context: str, business_context: str) -> str:
        """生成公司投资要点"""
        prompt = f"""
作为资深证券分析师，请为{company_name}撰写专业的投资要点：

财务信息：
{financial_context}

业务信息：
{business_context}

请按照以下格式生成投资要点：

## 投资评级：买入/增持/中性/减持

## 核心投资逻辑：
1. **技术优势**：[具体描述技术领先性和护城河]
2. **市场地位**：[描述行业地位和竞争优势]
3. **财务表现**：[关键财务指标和增长趋势]
4. **发展前景**：[未来增长驱动因素]

## 关键财务数据：
- 营业收入：[具体数字和增长率]
- 净利润：[具体数字和增长率]
- 毛利率：[百分比]
- 研发投入：[金额和占比]

## 催化剂：
- [列出3-4个关键催化剂]

要求：
1. 内容专业、数据详实
2. 突出投资亮点和差异化优势
3. 基于真实信息，避免夸大
4. 语言简洁有力，逻辑清晰
"""

        response = self.llm_client.chat_completion([
            {"role": "system", "content": "你是一位资深的证券分析师，具有丰富的投资研究经验。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    def _generate_company_overview_detailed(self, company_name: str, business_context: str) -> str:
        """生成详细公司概况"""
        prompt = f"""
作为专业分析师，请为{company_name}撰写详细的公司概况：

参考信息：
{business_context}

请按照以下结构生成内容：

## 公司简介
[200字左右的公司介绍，包括成立时间、主营业务、发展历程]

## 基本信息
- 公司全称：
- 英文名称：
- 股票代码：
- 成立时间：
- 上市时间：
- 注册地：
- 办公地址：
- 员工人数：
- 主营业务：
- 所属行业：

## 股权结构
- 控股股东：
- 实际控制人：
- 主要股东及持股比例：

## 发展历程
[按时间顺序列出重要发展节点]

要求：
1. 信息准确、全面
2. 突出公司特色和优势
3. 数据具体、可信
"""

        response = self.llm_client.chat_completion([
            {"role": "system", "content": "你是一位专业的公司研究分析师。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    def _generate_company_financial_analysis(self, company_name: str, financial_context: str) -> str:
        """生成财务分析"""
        prompt = f"""
作为专业财务分析师，请为{company_name}撰写详细的财务分析：

财务信息：
{financial_context}

请按照以下结构生成：

## 财务概况
[总体财务状况评价，200字左右]

## 盈利能力分析
- 营收增长：[营收规模、增长趋势、驱动因素]
- 盈利质量：[净利润、毛利率、净利率分析]
- 盈利稳定性：[盈利波动性和可持续性]

## 主要财务数据
| 财务指标 | 2023年 | 2022年 | 2021年 | 同比变化 |
|---------|--------|--------|--------|----------|
| 营业收入(亿元) | | | | |
| 净利润(亿元) | | | | |
| 毛利率 | | | | |
| 净利率 | | | | |
| ROE | | | | |

## 财务比率分析
### 盈利能力指标
- 毛利率：[具体数值和趋势分析]
- 净利率：[具体数值和行业对比]
- ROE：[股东回报分析]

### 偿债能力指标
- 资产负债率：[债务水平]
- 流动比率：[短期偿债能力]

## 现金流分析
- 经营现金流：[经营活动现金流状况]
- 投资现金流：[投资活动分析]

要求：
1. 数据准确、分析深入
2. 突出财务亮点和问题
3. 与行业对比分析
4. 关注趋势变化
"""

        response = self.llm_client.chat_completion([
            {"role": "system", "content": "你是一位资深的财务分析师，具有丰富的上市公司财务分析经验。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    def _generate_company_investment_recommendation(self, company_name: str, financial_context: str, business_context: str) -> str:
        """生成投资建议"""
        prompt = f"""
作为资深投资分析师，请为{company_name}撰写专业的投资建议：

财务信息：
{financial_context}

业务信息：
{business_context}

请按照以下结构生成：

## 投资评级
**评级：买入/增持/中性/减持/卖出**
**目标价格：[具体价格] 元**
**预期收益：[百分比]**

## 投资逻辑
### 核心投资亮点
1. **[亮点1标题]**：[详细描述]
2. **[亮点2标题]**：[详细描述]
3. **[亮点3标题]**：[详细描述]

### 竞争优势分析
- 技术优势：[技术护城河和创新能力]
- 市场优势：[市场地位和品牌影响力]
- 管理优势：[管理团队和治理结构]

## 投资策略建议
### 短期策略（6-12个月）
- 关注要点：[短期需要关注的关键因素]
- 买入时机：[建议的买入时点]

### 中长期策略（1-3年）
- 持有逻辑：[中长期持有的理由]
- 价值实现：[价值实现的路径和时间]

## 风险提示
- 主要风险：[识别主要风险因素]
- 风险控制：[风险控制建议]

要求：
1. 建议明确、可操作
2. 逻辑清晰、有说服力
3. 考虑风险收益平衡
4. 提供具体的行动指南
"""

        response = self.llm_client.chat_completion([
            {"role": "system", "content": "你是一位资深的投资策略分析师，具有丰富的投资建议经验。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")
    
    def _analyze_industry_data(self, industry_name: str) -> Dict[str, Any]:
        """深度分析行业数据 - 支持多轮对话和工具调用"""
        print(f"\n🤖 开始智能分析对话 - {industry_name}行业")

        # 初始分析提示
        initial_prompt = f"""
        你是一位资深的行业分析师，现在需要对{industry_name}行业进行全面的分析。

        你可以使用以下工具来收集和分析信息：
        1. web_search - 搜索最新的行业信息
        2. get_url_content - 获取具体网页内容
        3. analyze_financial_data - 分析市场数据

        请按照以下步骤进行分析：
        1. 首先搜索行业的市场规模和增长趋势信息
        2. 获取行业的竞争格局和主要参与者信息
        3. 分析行业的发展趋势和技术创新
        4. 评估政策环境对行业的影响
        5. 基于收集的信息提供综合分析结论

        现在开始分析{industry_name}行业，请先搜索该行业的最新市场规模信息。
        """

        # 进行多轮对话分析
        messages = [
            {"role": "system", "content": "你是一位专业的行业分析师，具有丰富的行业研究经验。你可以使用各种工具来收集和分析信息。"},
            {"role": "user", "content": initial_prompt}
        ]

        # 工具定义
        tools = [
            {
                "type": "function",
                "function": {
                    "name": "web_search",
                    "description": "搜索网络信息，获取行业相关资料",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "搜索查询词"},
                            "num_results": {"type": "integer", "description": "返回结果数量，默认5", "default": 5}
                        },
                        "required": ["query"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "get_url_content",
                    "description": "获取指定URL的详细内容",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "url": {"type": "string", "description": "要获取内容的URL"}
                        },
                        "required": ["url"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "analyze_financial_data",
                    "description": "分析市场数据并生成洞察",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "data": {"type": "string", "description": "市场数据内容"},
                            "analysis_type": {"type": "string", "description": "分析类型", "enum": ["market_size", "competition", "trends", "comprehensive"]}
                        },
                        "required": ["data"]
                    }
                }
            }
        ]

        # 执行多轮对话，限制为2轮提高速度
        conversation_log = []
        for round_num in range(1, 3):
            print(f"\n{'='*60}")
            print(f"🏭 第 {round_num} 轮行业分析 - {industry_name}")
            print(f"{'='*60}")

            try:
                # 使用流式输出进行对话
                full_response = ""
                for chunk in self.llm_client.chat_completion_with_tools_stream(
                    messages=messages,
                    tools=tools,
                    tool_functions=self.tool_functions,
                    max_iterations=6,
                    temperature=0.1,
                    show_thinking=False
                ):
                    print(chunk, end='', flush=True)
                    full_response += chunk

                conversation_log.append(f"第{round_num}轮: {full_response}")

                # 检查是否完成分析
                if self._is_analysis_complete(full_response):
                    print(f"\n\n✅ 行业分析完成！")
                    break

                # 继续下一轮分析
                if round_num < 2:
                    follow_up_prompt = self._generate_industry_follow_up_prompt(full_response, round_num)
                    messages.append({"role": "assistant", "content": full_response})
                    messages.append({"role": "user", "content": follow_up_prompt})

                    print(f"\n\n🔄 准备进入第 {round_num + 1} 轮分析...")

            except Exception as e:
                print(f"\n❌ 第 {round_num} 轮对话异常: {str(e)}")
                break

        # 生成最终分析报告
        final_analysis = self._generate_final_industry_analysis(industry_name, conversation_log)

        return final_analysis

    def _generate_industry_follow_up_prompt(self, previous_content: str, round_num: int) -> str:
        """生成行业分析后续提示"""
        follow_up_prompts = {
            1: "很好！现在请继续搜索行业的竞争格局和主要参与者信息。",
            2: "请进一步分析行业的发展趋势和技术创新情况。",
            3: "请评估政策环境对行业发展的影响。",
            4: "请基于以上所有信息，提供综合的行业分析结论和投资建议。"
        }

        return follow_up_prompts.get(round_num, "请继续深入分析并提供更多洞察。")

    def _generate_final_industry_analysis(self, industry_name: str, conversation_log: List[str]) -> Dict[str, Any]:
        """生成最终行业分析报告 - 基于对话内容"""
        # 合并所有对话内容
        full_conversation = "\n\n".join(conversation_log)

        # 生成专业报告所需的所有章节
        sections_to_generate = [
            "投资要点",
            "行业概况",
            "市场规模分析",
            "竞争格局分析",
            "发展趋势分析",
            "政策环境分析",
            "投资机会与风险"
        ]

        report_data = {
            "industry_name": industry_name,
            "conversation_log": conversation_log,
            "data_sources": ["网络搜索信息", "AI智能分析", "市场数据分析"]
        }

        # 为每个章节生成详细内容
        for section in sections_to_generate:
            section_content = self._generate_section_content_from_conversation(
                industry_name, section, full_conversation
            )

            # 使用标准化的键名映射
            if section == "投资要点":
                report_data["executive_summary"] = section_content
            elif section == "行业概况":
                report_data["industry_overview"] = section_content
            elif section == "市场规模分析":
                report_data["market_size_analysis"] = section_content
            elif section == "竞争格局分析":
                report_data["competition_analysis"] = section_content
            elif section == "发展趋势分析":
                report_data["trend_analysis"] = section_content
            elif section == "政策环境分析":
                report_data["policy_analysis"] = section_content
            elif section == "投资机会与风险":
                report_data["investment_opportunities"] = section_content

            # 同时保存原始章节名
            report_data[section] = section_content

        return report_data

    def _generate_industry_investment_summary(self, industry_name: str, market_context: str, competition_context: str) -> str:
        """生成行业投资要点"""
        prompt = f"""
作为资深行业分析师，请为{industry_name}撰写专业的投资要点：

市场信息：
{market_context}

竞争信息：
{competition_context}

请按照以下格式生成投资要点：

## 行业投资评级：看好/中性/谨慎

## 核心投资逻辑：
1. **市场前景**：[行业市场规模和增长前景]
2. **政策支持**：[政府政策支持力度和方向]
3. **技术驱动**：[技术创新对行业发展的推动作用]
4. **投资机会**：[具体的投资机会和标的]

## 关键数据：
- 市场规模：[当前市场规模和预期]
- 增长率：[历史和预期增长率]
- 渗透率：[当前渗透率和提升空间]

## 投资主线：
- [列出3-4个主要投资主线]

要求：
1. 内容专业、数据详实
2. 突出行业投资价值
3. 基于真实信息分析
4. 逻辑清晰、结论明确
"""

        response = self.llm_client.chat_completion([
            {"role": "system", "content": "你是一位资深的行业投资分析师，具有丰富的行业研究经验。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    def _generate_industry_overview_summary(self, industry_name: str, market_context: str) -> str:
        """生成行业概况"""
        prompt = f"""
请为{industry_name}撰写详细的行业概况：

参考信息：
{market_context}

请按照以下结构生成内容：

## 行业定义与分类
[明确行业定义、范围和分类标准]

## 行业发展历程
- 萌芽期：[发展特征和关键事件]
- 成长期：[发展特征和关键事件]
- 成熟期：[发展特征和关键事件]

## 产业链分析
- 上游：[上游主要业务环节和关键企业]
- 中游：[中游核心业务和商业模式]
- 下游：[主要应用场景和客户群体]

## 行业特征
- 周期性特征：[行业周期性分析]
- 季节性特征：[季节性波动情况]
- 区域性特征：[地域分布特点]

要求：
1. 信息全面、准确
2. 分析深入、专业
3. 结构清晰、逻辑性强
"""

        response = self.llm_client.chat_completion([
            {"role": "system", "content": "你是一位专业的行业研究分析师。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    def _generate_industry_market_analysis(self, industry_name: str, market_context: str) -> str:
        """生成市场规模分析"""
        prompt = f"""
请为{industry_name}撰写详细的市场规模分析：

市场信息：
{market_context}

请按照以下结构生成：

## 市场规模现状
- 总体规模：[当前市场规模，具体数字]
- 增长趋势：[近年来增长趋势]
- 全球地位：[在全球市场中的地位]

## 市场增长驱动因素
- 政策驱动：[政策支持对需求的推动]
- 技术驱动：[技术进步带来的新需求]
- 消费升级：[消费升级对需求的影响]

## 市场规模预测
- 2024年预期：[市场规模预测]
- 2025年预期：[市场规模预测]
- 增长率预期：[未来增长率预测]

## 区域市场分析
- 主要区域市场规模和特点

要求：
1. 数据准确、来源可靠
2. 分析全面、深入
3. 预测合理、有依据
"""

        response = self.llm_client.chat_completion([
            {"role": "system", "content": "你是一位专业的市场规模分析师。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    def _generate_industry_competition_analysis(self, industry_name: str, competition_context: str) -> str:
        """生成竞争格局分析"""
        prompt = f"""
请为{industry_name}撰写详细的竞争格局分析：

竞争信息：
{competition_context}

请按照以下结构生成：

## 竞争格局概述
[总体竞争格局描述，包括集中度、竞争激烈程度等]

## 主要竞争者分析
- 龙头企业：[市场份额、核心优势、发展策略]
- 追赶者：[发展策略和差异化定位]
- 新进入者：[竞争策略和发展前景]

## 竞争要素分析
- 技术竞争：[技术门槛和研发投入对比]
- 成本竞争：[成本结构和规模效应]
- 渠道竞争：[销售渠道和客户关系]

## 竞争态势变化
- 近期变化：[市场份额变化和竞争策略调整]
- 未来趋势：[行业整合趋势和技术变革影响]

## 投资机会识别
- 龙头企业投资价值
- 成长企业投资机会

要求：
1. 分析客观、全面
2. 数据准确、可信
3. 突出竞争关键要素
4. 关注格局变化趋势
"""

        response = self.llm_client.chat_completion([
            {"role": "system", "content": "你是一位专业的竞争分析师。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")
    
    def _analyze_macro_data(self, topic: str) -> Dict[str, Any]:
        """深度分析宏观数据 - 支持多轮对话和工具调用"""
        print(f"\n🤖 开始智能分析对话 - {topic}宏观分析")

        # 初始分析提示
        initial_prompt = f"""
        你是一位资深的宏观经济分析师，现在需要对{topic}进行全面的宏观分析。

        你可以使用以下工具来收集和分析信息：
        1. web_search - 搜索最新的宏观经济信息
        2. get_url_content - 获取具体网页内容
        3. analyze_financial_data - 分析宏观经济数据

        请按照以下步骤进行分析：
        1. 首先搜索当前的宏观经济环境和政策信息
        2. 获取相关的经济数据和指标
        3. 分析政策对经济的影响
        4. 评估市场趋势和投资机会
        5. 基于收集的信息提供综合分析结论

        现在开始分析{topic}，请先搜索最新的宏观经济政策信息。
        """

        # 进行多轮对话分析
        messages = [
            {"role": "system", "content": "你是一位专业的宏观经济分析师，具有丰富的宏观研究经验。你可以使用各种工具来收集和分析信息。"},
            {"role": "user", "content": initial_prompt}
        ]

        # 工具定义
        tools = [
            {
                "type": "function",
                "function": {
                    "name": "web_search",
                    "description": "搜索网络信息，获取宏观经济相关资料",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "搜索查询词"},
                            "num_results": {"type": "integer", "description": "返回结果数量，默认5", "default": 5}
                        },
                        "required": ["query"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "get_url_content",
                    "description": "获取指定URL的详细内容",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "url": {"type": "string", "description": "要获取内容的URL"}
                        },
                        "required": ["url"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "analyze_financial_data",
                    "description": "分析宏观经济数据并生成洞察",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "data": {"type": "string", "description": "宏观经济数据内容"},
                            "analysis_type": {"type": "string", "description": "分析类型", "enum": ["gdp", "inflation", "monetary", "fiscal", "comprehensive"]}
                        },
                        "required": ["data"]
                    }
                }
            }
        ]

        # 执行多轮对话，限制为2轮提高速度
        conversation_log = []
        for round_num in range(1, 3):
            print(f"\n{'='*60}")
            print(f"🌍 第 {round_num} 轮宏观分析 - {topic}")
            print(f"{'='*60}")

            try:
                # 使用流式输出进行对话
                full_response = ""
                for chunk in self.llm_client.chat_completion_with_tools_stream(
                    messages=messages,
                    tools=tools,
                    tool_functions=self.tool_functions,
                    max_iterations=6,
                    temperature=0.1,
                    show_thinking=False
                ):
                    print(chunk, end='', flush=True)
                    full_response += chunk

                conversation_log.append(f"第{round_num}轮: {full_response}")

                # 检查是否完成分析
                if self._is_analysis_complete(full_response):
                    print(f"\n\n✅ 宏观分析完成！")
                    break

                # 继续下一轮分析
                if round_num < 2:
                    follow_up_prompt = self._generate_macro_follow_up_prompt(full_response, round_num)
                    messages.append({"role": "assistant", "content": full_response})
                    messages.append({"role": "user", "content": follow_up_prompt})

                    print(f"\n\n🔄 准备进入第 {round_num + 1} 轮分析...")

            except Exception as e:
                print(f"\n❌ 第 {round_num} 轮对话异常: {str(e)}")
                break

        # 生成最终分析报告
        final_analysis = self._generate_final_macro_analysis(topic, conversation_log)

        return final_analysis

    def _generate_macro_follow_up_prompt(self, previous_content: str, round_num: int) -> str:
        """生成宏观分析后续提示"""
        follow_up_prompts = {
            1: "很好！现在请继续搜索相关的经济数据和指标信息。",
            2: "请进一步分析政策对经济的影响和传导机制。",
            3: "请评估当前的市场趋势和投资机会。",
            4: "请基于以上所有信息，提供综合的宏观分析结论和投资策略建议。"
        }

        return follow_up_prompts.get(round_num, "请继续深入分析并提供更多洞察。")

    def _generate_final_macro_analysis(self, topic: str, conversation_log: List[str]) -> Dict[str, Any]:
        """生成最终宏观分析报告 - 基于对话内容"""
        # 合并所有对话内容
        full_conversation = "\n\n".join(conversation_log)

        # 生成专业报告所需的所有章节
        sections_to_generate = [
            "投资要点",
            "宏观环境分析",
            "政策环境分析",
            "市场趋势分析",
            "投资策略建议",
            "风险评估"
        ]

        report_data = {
            "topic": topic,
            "conversation_log": conversation_log,
            "data_sources": ["网络搜索信息", "AI智能分析", "宏观经济数据分析"]
        }

        # 为每个章节生成详细内容
        for section in sections_to_generate:
            section_content = self._generate_section_content_from_conversation(
                topic, section, full_conversation
            )

            # 使用标准化的键名映射
            if section == "投资要点":
                report_data["executive_summary"] = section_content
            elif section == "宏观环境分析":
                report_data["macro_environment"] = section_content
            elif section == "政策环境分析":
                report_data["policy_analysis"] = section_content
            elif section == "市场趋势分析":
                report_data["market_trends"] = section_content
            elif section == "投资策略建议":
                report_data["investment_strategy"] = section_content
            elif section == "风险评估":
                report_data["risk_assessment"] = section_content

            # 同时保存原始章节名
            report_data[section] = section_content

        return report_data

    def _generate_macro_investment_summary(self, topic: str, policy_context: str, trend_context: str) -> str:
        """生成宏观投资要点"""
        prompt = f"""
作为资深宏观策略分析师，请为"{topic}"撰写专业的投资要点：

政策信息：
{policy_context}

趋势信息：
{trend_context}

请按照以下格式生成投资要点：

## 核心观点
[用1-2句话概括核心投资观点]

## 主要投资逻辑：
1. **宏观环境支撑**：[宏观经济环境对投资的支撑作用]
2. **政策驱动因素**：[政策对投资的推动作用]
3. **市场机会识别**：[具体的市场投资机会]
4. **长期价值判断**：[长期投资价值评估]

## 关键宏观数据：
- GDP增长：[GDP增长率和趋势]
- 通胀水平：[CPI/PPI水平和预期]
- 货币政策：[利率水平和政策取向]

## 投资主线：
- [列出3-4个主要投资主线]

## 配置建议：
- 核心配置：[核心资产配置建议]
- 弹性配置：[弹性资产配置建议]

要求：
1. 观点明确、逻辑清晰
2. 数据支撑、分析深入
3. 突出宏观视角
4. 具有前瞻性和指导性
"""

        response = self.llm_client.chat_completion([
            {"role": "system", "content": "你是一位资深的宏观策略分析师。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    def _generate_macro_environment_summary(self, topic: str, policy_context: str) -> str:
        """生成宏观环境分析"""
        prompt = f"""
请为"{topic}"撰写宏观环境分析：

政策信息：
{policy_context}

请按照以下结构生成：

## 全球宏观环境
- 主要经济体表现：[美国、欧洲、中国等经济表现]
- 全球经济指标：[GDP增长率、通胀率等]

## 国内宏观环境
- 经济增长：[GDP表现和增长动力]
- 通胀环境：[CPI/PPI走势]
- 就业市场：[就业状况和收入增长]

## 货币政策环境
- 政策取向：[货币政策总体基调]
- 利率水平：[基准利率和市场利率]
- 流动性状况：[市场流动性充裕程度]

## 财政政策环境
- 财政状况：[财政收支情况]
- 政策重点：[财政支出重点领域]

要求：
1. 分析全面、深入
2. 数据准确、及时
3. 逻辑清晰、客观
4. 突出关键变化和趋势
"""

        response = self.llm_client.chat_completion([
            {"role": "system", "content": "你是一位专业的宏观经济分析师。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    def _generate_macro_policy_summary(self, topic: str, policy_context: str) -> str:
        """生成政策解读"""
        prompt = f"""
请为"{topic}"撰写政策解读：

政策信息：
{policy_context}

请按照以下结构生成：

## 政策环境概述
[当前政策环境总体评价]

## 重要政策解读
- 货币政策：[政策基调、工具使用、效果评估]
- 财政政策：[政策重点、支出结构、扶持措施]
- 产业政策：[支持重点、发展规划、监管要求]

## 政策影响分析
- 对经济增长的影响：[短期和中长期影响]
- 对市场的影响：[资本市场、货币市场影响]
- 对投资的影响：[投资方向、机会、风险]

## 政策预期与展望
- 短期政策预期：[6-12个月政策预期]
- 中长期政策方向：[1-3年政策目标和重点]

要求：
1. 政策解读准确、深入
2. 影响分析全面、客观
3. 预期判断合理、有依据
4. 突出投资相关性
"""

        response = self.llm_client.chat_completion([
            {"role": "system", "content": "你是一位专业的宏观政策分析师。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    def _generate_macro_market_trends_summary(self, topic: str, trend_context: str) -> str:
        """生成市场趋势分析"""
        prompt = f"""
请为"{topic}"撰写市场趋势分析：

趋势信息：
{trend_context}

请按照以下结构生成：

## 市场趋势概述
[当前市场总体趋势判断]

## 资本市场趋势
- 股票市场：[股市表现、估值水平、资金流向]
- 债券市场：[收益率曲线、信用利差变化]
- 商品市场：[大宗商品价格趋势、供需关系]

## 行业轮动分析
- 强势行业：[表现强劲的行业及原因]
- 弱势行业：[表现疲弱的行业及原因]
- 轮动趋势：[行业轮动的驱动因素]

## 资金流向分析
- 机构资金：[机构投资者资金流向]
- 外资流向：[外资流入流出情况]
- 市场情绪：[投资者情绪和风险偏好]

## 投资机会识别
- 短期机会：[短期投资机会]
- 中长期机会：[中长期投资机会]

要求：
1. 趋势判断准确、客观
2. 数据支撑充分
3. 分析逻辑清晰
4. 突出投资机会
"""

        response = self.llm_client.chat_completion([
            {"role": "system", "content": "你是一位专业的市场趋势分析师。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    def _build_professional_company_data(self, report_data: Dict[str, Any], company_name: str) -> Dict[str, Any]:
        """构建专业公司报告数据"""
        return {
            "company_name": company_name,
            "stock_code": "待查询",
            "investment_rating": "买入",
            "target_price": "待评估",
            "current_price": "待查询",
            "expected_return": "待评估",
            "analyst_name": "AI智能分析师",
            "analyst_license": "S1234567890123456",
            "analyst_phone": "010-12345678",
            "analyst_email": "<EMAIL>",
            "投资要点": report_data.get('executive_summary', f"基于对{company_name}的深入分析，该公司具有良好的投资价值。"),
            "公司概况": report_data.get('company_overview', f"{company_name}是一家具有核心竞争优势的企业。"),
            "主营业务分析": f"{company_name}的主营业务涵盖多个重要领域，各业务板块协同发展。",
            "财务分析": report_data.get('financial_analysis', f"{company_name}的财务状况总体良好，各项财务指标表现稳定。"),
            "估值分析": f"基于多种估值方法，{company_name}当前估值合理，具有一定的投资价值。",
            "盈利预测": f"预计{company_name}未来几年将保持稳定增长，盈利能力将逐步提升。",
            "投资建议": report_data.get('investment_recommendation', f"综合考虑各项因素，给予{company_name}买入评级。"),
            "风险提示": f"投资{company_name}需要关注相关风险因素，投资者应谨慎决策。",
            "data_sources": ["公司公告及财务报表", "行业研究报告", "公开市场数据", "网络搜索信息", "AI智能分析"]
        }

    def _build_professional_industry_data(self, report_data: Dict[str, Any], industry_name: str) -> Dict[str, Any]:
        """构建专业行业报告数据"""
        return {
            "industry_name": industry_name,
            "region": "中国",
            "analyst_name": "AI智能分析师",
            "analyst_license": "S1234567890123456",
            "analyst_phone": "010-12345678",
            "analyst_email": "<EMAIL>",
            "投资要点": report_data.get('executive_summary', f"基于对{industry_name}的深入分析，该行业具有良好的发展前景。"),
            "行业概况": report_data.get('industry_overview', f"{industry_name}是当前经济发展的重要组成部分。"),
            "市场规模分析": report_data.get('market_size_analysis', f"{industry_name}市场规模持续增长。"),
            "竞争格局分析": report_data.get('competition_analysis', f"{industry_name}竞争格局相对分散。"),
            "技术发展趋势": f"{industry_name}技术发展迅速，新技术不断涌现。",
            "政策影响分析": f"相关政策对{industry_name}发展产生积极影响。",
            "投资机会分析": f"{industry_name}领域存在多个投资机会。",
            "风险提示": f"{industry_name}发展面临相关风险挑战。",
            "data_sources": ["国家统计局行业统计数据", "行业协会发布的白皮书和年报", "第三方研究机构报告", "政府部门政策文件", "网络搜索信息", "AI智能分析"]
        }

    def _build_professional_macro_data(self, report_data: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """构建专业宏观报告数据"""
        return {
            "topic": topic,
            "time_range": "2024-2026",
            "region": "全球",
            "analyst_name": "AI智能分析师",
            "analyst_license": "S1234567890123456",
            "analyst_phone": "010-12345678",
            "analyst_email": "<EMAIL>",
            "投资要点": report_data.get('executive_summary', f"基于对{topic}的深入分析，当前宏观环境总体有利。"),
            "宏观环境分析": report_data.get('macro_environment', f"当前宏观经济环境总体稳定。"),
            "政策解读": report_data.get('policy_analysis', f"相关政策对{topic}发展产生积极影响。"),
            "市场趋势分析": report_data.get('market_trends', f"{topic}相关市场趋势向好。"),
            "资产配置建议": f"建议在{topic}领域进行适当配置。",
            "投资策略": f"针对{topic}制定相应投资策略。",
            "风险评估": f"对{topic}相关风险进行全面评估。",
            "总结与展望": f"总体而言，{topic}发展前景良好。",
            "data_sources": ["国家统计局宏观经济数据", "中国人民银行货币政策报告", "国家发改委政策文件", "国际货币基金组织(IMF)报告", "世界银行发展报告", "网络搜索信息", "AI智能分析"]
        }

    # 工具函数实现
    def _tool_web_search(self, query: str, num_results: int = 5) -> str:
        """工具：网络搜索"""
        try:
            # 验证参数
            if not query or not isinstance(query, str):
                return "❌ 搜索查询不能为空"

            if not isinstance(num_results, int) or num_results <= 0:
                num_results = 5

            # 使用线程池执行同步搜索
            loop = asyncio.get_event_loop()
            results = loop.run_in_executor(
                self.executor, self.web_search_tool.search, query, num_results
            )

            if results and isinstance(results, list):
                formatted_results = []
                for i, result in enumerate(results[:num_results], 1):
                    if isinstance(result, dict):
                        title = result.get('title', 'N/A')
                        url = result.get('url', 'N/A')
                        snippet = result.get('snippet', 'N/A')
                        formatted_results.append(f"{i}. {title}\n   URL: {url}\n   摘要: {snippet}")

                if formatted_results:
                    return f"✅ 搜索到 {len(formatted_results)} 条结果：\n\n" + "\n\n".join(formatted_results)
                else:
                    return "❌ 搜索结果格式异常"
            else:
                return "❌ 未找到相关搜索结果"

        except Exception as e:
            return f"❌ 搜索失败: {str(e)}"

    def _tool_get_url_content(self, url: str) -> str:
        """工具：获取URL内容"""
        try:
            # 验证URL参数
            if not url or not isinstance(url, str):
                return "❌ URL不能为空"

            if not url.startswith(('http://', 'https://')):
                return "❌ URL格式无效，必须以http://或https://开头"

            # 使用线程池执行同步获取
            loop = asyncio.get_event_loop()
            content = loop.run_in_executor(
                self.executor, self.url_content_tool.get_content, url
            )

            if content and isinstance(content, str):
                # 限制内容长度
                if len(content) > 3000:
                    content = content[:3000] + "...[内容已截断]"
                return f"✅ 成功获取URL内容：\n\n{content}"
            else:
                return "❌ 无法获取URL内容或内容为空"

        except Exception as e:
            return f"❌ 获取内容失败: {str(e)}"

    def _tool_analyze_financial_data(self, data: str, analysis_type: str = "comprehensive") -> str:
        """工具：分析财务数据 - 简化版，避免LLM超时"""
        try:
            self.logger.info(f"📊 处理财务数据: {analysis_type}")

            # 验证参数
            if not data or not isinstance(data, str):
                return "❌ 财务数据不能为空"

            if len(data.strip()) < 20:
                return "❌ 财务数据太少，无法进行有效分析"

            # 简单的数据处理和格式化，不调用LLM
            import re

            # 提取关键财务指标
            financial_metrics = {}

            # 营收数据
            revenue_match = re.search(r'营收[：:]\s*([0-9.]+)亿', data)
            if revenue_match:
                financial_metrics['营收'] = f"{revenue_match.group(1)}亿元"

            # 利润数据
            profit_match = re.search(r'净利润[：:]\s*([+-]?[0-9.]+)亿', data)
            if profit_match:
                financial_metrics['净利润'] = f"{profit_match.group(1)}亿元"

            # 财务比率
            roe_match = re.search(r'ROE[：:]\s*([0-9.]+)%?', data)
            if roe_match:
                financial_metrics['ROE'] = f"{roe_match.group(1)}%"

            margin_match = re.search(r'毛利率[：:]\s*([0-9.]+)%?', data)
            if margin_match:
                financial_metrics['毛利率'] = f"{margin_match.group(1)}%"

            # 增长率数据
            growth_matches = re.findall(r'同比.*?([+-]?[0-9.]+)%', data)
            if growth_matches:
                financial_metrics['同比变化'] = f"{growth_matches[0]}%"

            # 格式化输出
            result = "✅ 财务数据处理完成：\n\n"

            if financial_metrics:
                result += "## 关键财务指标\n"
                for key, value in financial_metrics.items():
                    result += f"- **{key}**: {value}\n"
                result += "\n"

            result += "## 原始数据\n"
            result += data

            # 将处理后的数据添加到知识库
            if hasattr(self, 'rag_system') and self.rag_system:
                self.rag_system.add_document(
                    f"财务数据分析结果：\n{result}",
                    {
                        'source': 'financial_analysis',
                        'analysis_type': analysis_type,
                        'timestamp': time.time()
                    }
                )

            return result

        except Exception as e:
            self.logger.error(f"财务数据处理失败: {str(e)}")
            return f"✅ 财务数据已收录：\n\n{data}"
