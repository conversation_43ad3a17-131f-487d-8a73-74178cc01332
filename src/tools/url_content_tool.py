import requests
from bs4 import BeautifulSoup
import hashlib
import json
import os
from typing import Optional, Dict, Any
from config.settings import Config

class URLContentTool:
    """URL内容获取工具，支持智能缓存和HTML解析"""
    
    def __init__(self, config: Config = None):
        self.config = config or Config()
        self.cache_dir = self.config.URL_CACHE_DIR
        self.max_cache_files = self.config.MAX_CACHE_FILES
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # URL映射文件
        self.url_mapping_file = os.path.join(self.cache_dir, "url_mapping.json")
        self.url_mapping = self._load_url_mapping()
    
    def _load_url_mapping(self) -> Dict[str, str]:
        """加载URL映射表"""
        if os.path.exists(self.url_mapping_file):
            try:
                with open(self.url_mapping_file, "r", encoding="utf-8") as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError):
                return {}
        return {}
    
    def _save_url_mapping(self):
        """保存URL映射表"""
        with open(self.url_mapping_file, "w", encoding="utf-8") as f:
            json.dump(self.url_mapping, f, ensure_ascii=False, indent=2)
    
    def _get_cache_path(self, url: str) -> str:
        """获取URL对应的缓存文件路径"""
        if url in self.url_mapping:
            return self.url_mapping[url]
        
        # 创建新的缓存条目
        url_hash = hashlib.md5(url.encode()).hexdigest()
        cache_filename = f"{url_hash}.html"
        cache_path = os.path.join(self.cache_dir, cache_filename)
        
        # 检查缓存文件数量限制
        if len(self.url_mapping) >= self.max_cache_files:
            self._cleanup_cache()
        
        self.url_mapping[url] = cache_path
        self._save_url_mapping()
        return cache_path
    
    def _cleanup_cache(self):
        """清理过期缓存文件"""
        # 删除最旧的20%缓存文件
        items_to_remove = int(len(self.url_mapping) * 0.2)
        urls_to_remove = list(self.url_mapping.keys())[:items_to_remove]
        
        for url in urls_to_remove:
            cache_path = self.url_mapping[url]
            if os.path.exists(cache_path):
                os.remove(cache_path)
            del self.url_mapping[url]
    
    def _is_cached(self, url: str) -> bool:
        """检查URL是否已缓存"""
        cache_path = self._get_cache_path(url)
        return os.path.exists(cache_path)
    
    def _get_cached_content(self, url: str) -> Optional[str]:
        """获取缓存的URL内容"""
        if not self._is_cached(url):
            return None
        
        cache_path = self._get_cache_path(url)
        try:
            with open(cache_path, "r", encoding="utf-8", errors="replace") as f:
                return f.read()
        except IOError:
            return None
    
    def _cache_content(self, url: str, content: str):
        """缓存URL内容"""
        cache_path = self._get_cache_path(url)
        with open(cache_path, "w", encoding="utf-8", errors="replace") as f:
            f.write(content)
    
    def _fetch_url_content(self, url: str) -> str:
        """从网络获取URL内容"""
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1"
        }
        
        try:
            response = requests.get(url, headers=headers, timeout=30, allow_redirects=True)
            response.raise_for_status()
            
            # 使用BeautifulSoup解析HTML并提取文本
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 移除脚本和样式元素
            for script in soup(["script", "style"]):
                script.decompose()
            
            # 提取主要内容
            text = soup.get_text()
            
            # 清理文本
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            return text
            
        except requests.RequestException as e:
            return f"获取URL内容失败: {str(e)}"
        except Exception as e:
            return f"解析内容失败: {str(e)}"
    
    def get_content(self, url: str, force_refresh: bool = False) -> str:
        """获取URL内容，优先使用缓存"""
        if not force_refresh:
            cached_content = self._get_cached_content(url)
            if cached_content:
                return cached_content
        
        # 获取新内容
        content = self._fetch_url_content(url)
        
        # 缓存内容
        if content and not content.startswith("获取URL内容失败") and not content.startswith("解析内容失败"):
            self._cache_content(url, content)
        
        return content
