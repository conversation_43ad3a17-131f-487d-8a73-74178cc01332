# src/agents/company_agent.py
from typing import Dict, Any, List
from tqdm import tqdm
import time
import asyncio
from src.agents.base_agent import BaseAgent
from src.tools.web_search_tool import WebSearchTool
from src.tools.url_content_tool import URLContentTool
from src.tools.rag_system import RAGSystem
from src.tools.docx_generator import EnterpriseDocxGenerator
from src.tools.professional_docx_generator import ProfessionalFinancialReportGenerator
from src.models.llm_client import LLMClient
from src.utils.data_processor import DataProcessor
from config.settings import Config

class CompanyAgent(BaseAgent):
    """
    公司研报生成Agent - 专门负责生成公司研究报告
    """
    
    def __init__(self, config: Config = None):
        super().__init__(config, "CompanyAgent")
        
        # 初始化工具
        self.llm_client = LLMClient(self.config)
        self.web_search_tool = WebSearchTool(self.config)
        self.url_content_tool = URLContentTool(self.config)
        self.rag_system = RAGSystem(self.config)
        self.docx_generator = EnterpriseDocxGenerator(self.config)
        self.professional_docx_generator = ProfessionalFinancialReportGenerator(self.config)
        self.data_processor = DataProcessor(self.config)
        
        # 公司分析相关配置 - 扩展搜索查询以获取更丰富的信息
        self.company_search_queries = [
            # 基础信息查询
            "{company_name} 公司简介 基本信息 成立时间",
            "{company_name} 股票代码 上市时间 交易所",
            "{company_name} 注册地址 办公地址 员工规模",

            # 财务数据查询
            "{company_name} 财务报告 2023 年报 营业收入",
            "{company_name} 净利润 毛利率 净利率 2023",
            "{company_name} 资产负债表 现金流量表 2023",
            "{company_name} 财务指标 ROE ROA 资产负债率",

            # 业务分析查询
            "{company_name} 主营业务 业务结构 收入构成",
            "{company_name} 产品服务 商业模式 盈利模式",
            "{company_name} 客户结构 销售渠道 市场布局",
            "{company_name} 研发投入 技术创新 专利情况",

            # 竞争分析查询
            "{company_name} 竞争优势 核心技术 护城河",
            "{company_name} 行业地位 市场份额 排名",
            "{company_name} 竞争对手 行业对比 差异化",

            # 投资分析查询
            "{company_name} 投资价值 估值分析 PE PB",
            "{company_name} 股价表现 市场表现 涨跌幅",
            "{company_name} 机构评级 分析师观点 目标价",
            "{company_name} 投资亮点 催化剂 增长驱动",

            # 风险分析查询
            "{company_name} 风险提示 投资风险 经营风险",
            "{company_name} 政策风险 市场风险 技术风险",
            "{company_name} 财务风险 流动性风险 债务风险",

            # 管理层和治理查询
            "{company_name} 管理层 董事会 高管团队",
            "{company_name} 公司治理 股权结构 控股股东",
            "{company_name} 激励机制 员工持股 期权计划",

            # 发展前景查询
            "{company_name} 发展战略 未来规划 扩张计划",
            "{company_name} 新产品 新业务 市场拓展",
            "{company_name} 行业趋势 政策支持 发展机遇"
        ]
        
        # 初始化工具函数映射
        self.tool_functions = {
            "web_search": self._tool_web_search,
            "get_url_content": self._tool_get_url_content,
            "search_knowledge_base": self._tool_search_knowledge_base,
            "add_to_knowledge_base": self._tool_add_to_knowledge_base,
            "analyze_financial_data": self._tool_analyze_financial_data
        }

        # 工具定义
        self.tools = [
            {
                "type": "function",
                "function": {
                    "name": "web_search",
                    "description": "搜索网络信息，获取公司相关资料",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "搜索查询词"
                            },
                            "num_results": {
                                "type": "integer",
                                "description": "返回结果数量，默认5",
                                "default": 5
                            }
                        },
                        "required": ["query"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "get_url_content",
                    "description": "获取指定URL的详细内容",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "url": {
                                "type": "string",
                                "description": "要获取内容的URL"
                            }
                        },
                        "required": ["url"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "search_knowledge_base",
                    "description": "从知识库中搜索相关信息",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "搜索查询词"
                            }
                        },
                        "required": ["query"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "add_to_knowledge_base",
                    "description": "向知识库添加新信息",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "content": {
                                "type": "string",
                                "description": "要添加的内容"
                            },
                            "metadata": {
                                "type": "object",
                                "description": "内容的元数据"
                            }
                        },
                        "required": ["content"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "analyze_financial_data",
                    "description": "分析财务数据并生成洞察",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "data": {
                                "type": "string",
                                "description": "财务数据内容"
                            },
                            "analysis_type": {
                                "type": "string",
                                "description": "分析类型：profitability, liquidity, efficiency, leverage",
                                "enum": ["profitability", "liquidity", "efficiency", "leverage", "comprehensive"]
                            }
                        },
                        "required": ["data"]
                    }
                }
            }
        ]

        self.logger.info("CompanyAgent 初始化完成")
    
    async def run(self, company_name: str, stock_code: str = None) -> str:
        """
        生成公司研究报告

        Args:
            company_name: 公司名称
            stock_code: 股票代码（可选）

        Returns:
            生成的报告文件路径
        """
        self.start_task(f"生成公司研报 - {company_name}")

        # 创建进度条
        total_steps = 5
        progress_bar = tqdm(total=total_steps, desc=f"📊 生成{company_name}研报",
                           bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}]')

        try:
            # 验证输入
            if not self.validate_input(company_name):
                raise ValueError("公司名称不能为空")

            # 第一步：信息收集
            progress_bar.set_description(f"🔍 收集{company_name}信息")
            self.logger.info(f"🔍 开始收集 {company_name} 的相关信息")
            search_results = await self._collect_company_information(company_name, stock_code)
            progress_bar.update(1)

            # 第二步：内容抓取和处理
            progress_bar.set_description(f"📄 处理{company_name}内容")
            self.logger.info(f"📄 开始抓取和处理 {company_name} 的详细内容")
            processed_data = await self._process_search_results(search_results, company_name)
            progress_bar.update(1)

            # 第三步：构建知识库
            progress_bar.set_description(f"🧠 构建{company_name}知识库")
            self.logger.info(f"🧠 构建 {company_name} 的知识库")
            await self._build_knowledge_base(processed_data, company_name)
            progress_bar.update(1)

            # 第四步：深度分析
            progress_bar.set_description(f"🔬 深度分析{company_name}")
            self.logger.info(f"🔬 开始深度分析 {company_name}")
            analysis_result = await self._analyze_company_data(company_name, stock_code)
            progress_bar.update(1)

            # 第五步：生成报告
            progress_bar.set_description(f"📝 生成{company_name}报告")
            self.logger.info(f"📝 生成 {company_name} 的DOCX报告")
            report_path = await self._generate_company_report(analysis_result)
            progress_bar.update(1)

            progress_bar.set_description(f"✅ {company_name}研报完成")
            progress_bar.close()

            self.end_task(f"生成公司研报 - {company_name}")
            return report_path

        except Exception as e:
            progress_bar.close()
            self.logger.error(f"❌ 生成公司研报失败: {str(e)}")
            raise e
    
    async def _collect_company_information(self, company_name: str, stock_code: str = None) -> List[Dict]:
        """
        收集公司相关信息
        """
        search_queries = []
        
        # 构建搜索查询
        for query_template in self.company_search_queries:
            query = query_template.format(company_name=company_name)
            if stock_code:
                query += f" {stock_code}"
            search_queries.append(query)
        
        # 改为串行搜索，避免速率限制问题
        all_results = []

        # 限制搜索查询数量，避免过多请求
        limited_queries = search_queries[:10]  # 最多10个查询

        for i, query in enumerate(limited_queries):
            try:
                self.logger.info(f"🔍 执行搜索 {i+1}/{len(limited_queries)}: {query}")
                results = self.web_search_tool.search(query, num_results=3)  # 减少每次搜索的结果数

                if results and isinstance(results, list):
                    all_results.extend(results)
                    self.logger.info(f"✅ 搜索 {i+1} 成功，获得 {len(results)} 条结果")
                else:
                    self.logger.warning(f"⚠️ 搜索 {i+1} 无结果")

                # 添加延迟，避免速率限制
                if i < len(limited_queries) - 1:  # 最后一次不需要延迟
                    await asyncio.sleep(0.5)  # 500ms延迟

            except Exception as e:
                self.logger.error(f"❌ 搜索 {i+1} 失败: {str(e)}")
                continue
        
        self.logger.info(f"收集到 {len(all_results)} 条搜索结果")
        return all_results
    
    async def _process_search_results(self, search_results: List[Dict], company_name: str) -> List[Dict]:
        """
        简化处理搜索结果，直接使用搜索结果的snippet，避免复杂的URL抓取
        """
        processed_data = []

        # 直接使用搜索结果，不进行复杂的URL抓取
        for result in search_results[:20]:  # 限制处理数量
            title = result.get("title", "")
            snippet = result.get("snippet", "")
            url = result.get("url", "")

            # 放宽条件：只要有标题就可以使用，snippet可以为空
            if title and len(title) > 10:  # 标题至少10个字符
                # 如果snippet为空，使用标题作为内容
                content_text = snippet if snippet and len(snippet) > 20 else title

                processed_data.append({
                    "url": url,
                    "title": title,
                    "content": f"{title}\n\n{content_text}",  # 合并标题和摘要
                    "company": company_name,
                    "content_type": self._classify_content_type(title, content_text)
                })

                self.logger.debug(f"处理搜索结果: {title[:50]}... (snippet长度: {len(snippet)})")

        self.logger.info(f"成功处理 {len(processed_data)} 条有效内容")
        return processed_data
    
    def _classify_content_type(self, title: str, content: str) -> str:
        """
        分类内容类型
        """
        title_lower = title.lower()
        content_lower = content.lower()
        
        if any(keyword in title_lower or keyword in content_lower 
               for keyword in ["财务", "年报", "财报", "营收", "利润"]):
            return "financial"
        elif any(keyword in title_lower or keyword in content_lower 
                 for keyword in ["业务", "产品", "服务", "商业模式"]):
            return "business"
        elif any(keyword in title_lower or keyword in content_lower 
                 for keyword in ["风险", "挑战", "问题", "威胁"]):
            return "risk"
        elif any(keyword in title_lower or keyword in content_lower 
                 for keyword in ["投资", "估值", "评级", "目标价"]):
            return "investment"
        else:
            return "general"
    
    async def _build_knowledge_base(self, processed_data: List[Dict], company_name: str):
        """
        构建知识库
        """
        for data in processed_data:
            metadata = {
                "source": data["url"],
                "company": company_name,
                "content_type": data["content_type"],
                "title": data["title"]
            }
            
            # 添加到RAG系统
            self.rag_system.add_document(data["content"], metadata)
        
        self.logger.info(f"知识库构建完成，共添加 {len(processed_data)} 个文档")
    
    async def _analyze_company_data(self, company_name: str, stock_code: str = None) -> Dict[str, Any]:
        """
        深度分析公司数据 - 支持多轮对话和工具调用
        """
        self.logger.info(f"🤖 开始智能分析对话 - {company_name}")

        # 初始分析提示
        initial_prompt = f"""
        你是一位资深的金融分析师，现在需要对{company_name}（股票代码：{stock_code}）进行全面的投资分析。

        你可以使用以下工具来收集和分析信息：
        1. web_search - 搜索最新的公司信息
        2. get_url_content - 获取具体网页内容
        3. search_knowledge_base - 搜索已有的知识库信息
        4. analyze_financial_data - 分析财务数据

        请按照以下步骤进行分析：
        1. 首先搜索公司的最新财务信息和年报数据
        2. 获取公司的业务模式和竞争优势信息
        3. 分析公司的投资价值和风险因素
        4. 基于收集的信息提供综合分析结论

        现在开始分析{company_name}，请先搜索该公司的最新财务信息。
        """

        # 进行多轮对话分析
        messages = [
            {"role": "system", "content": "你是一位专业的金融分析师，具有丰富的投资研究经验。你可以使用各种工具来收集和分析信息。"},
            {"role": "user", "content": initial_prompt}
        ]

        # 执行多轮对话，增加到8轮以获取更丰富的内容
        conversation_log = []
        for round_num in range(1, 9):
            self.logger.info(f"💬 执行第 {round_num} 轮分析对话")
            print(f"\n{'='*60}")
            print(f"🤖 第 {round_num} 轮智能分析 - {company_name}")
            print(f"{'='*60}")

            try:
                # 使用流式输出进行对话
                full_response = ""
                async for chunk in self.llm_client.async_chat_completion_with_tools_stream(
                    messages=messages,
                    tools=self.tools,
                    tool_functions=self.tool_functions,
                    max_iterations=6,
                    temperature=0.1,
                    show_thinking=False
                ):
                    print(chunk, end='', flush=True)
                    full_response += chunk

                conversation_log.append(f"第{round_num}轮: {full_response}")

                # 检查是否完成分析
                if self._is_analysis_complete(full_response):
                    self.logger.info(f"✅ 分析完成于第 {round_num} 轮")
                    print(f"\n\n✅ 分析完成！")
                    break

                # 继续下一轮分析
                if round_num < 8:
                    follow_up_prompt = self._generate_follow_up_prompt(full_response, round_num)
                    messages.append({"role": "assistant", "content": full_response})
                    messages.append({"role": "user", "content": follow_up_prompt})

                    print(f"\n\n🔄 准备进入第 {round_num + 1} 轮分析...")

            except Exception as e:
                self.logger.error(f"第 {round_num} 轮对话异常: {str(e)}")
                print(f"\n❌ 第 {round_num} 轮对话异常: {str(e)}")
                break

        # 生成最终分析报告
        final_analysis = await self._generate_final_analysis(company_name, stock_code, conversation_log)

        return final_analysis

    def _is_analysis_complete(self, content: str) -> bool:
        """判断分析是否完成"""
        completion_indicators = [
            "综合分析结论",
            "投资建议",
            "分析完成",
            "总结",
            "最终评估"
        ]

        content_lower = content.lower()
        return any(indicator in content_lower for indicator in completion_indicators)

    def _generate_follow_up_prompt(self, previous_content: str, round_num: int) -> str:
        """生成后续分析提示"""
        follow_up_prompts = {
            1: "很好！现在请继续深入搜索公司的详细基本信息，包括成立时间、股权结构、管理层信息等。请确保获取准确的数据。",
            2: "请详细分析公司的主营业务模式、产品服务、收入结构和商业模式。重点关注业务的盈利能力和竞争优势。",
            3: "请深入分析公司的财务状况，包括营收、利润、现金流、财务比率等关键指标。请搜索最新的财务数据。",
            4: "请分析公司的技术实力、研发投入、创新能力和核心竞争力。关注公司的技术护城河和差异化优势。",
            5: "请评估公司的市场地位、行业前景和发展趋势。分析公司在行业中的竞争地位和未来增长空间。",
            6: "请进行投资价值分析，包括估值水平、投资亮点和增长驱动因素。提供专业的投资观点。",
            7: "请全面评估公司面临的各类风险因素，包括市场风险、经营风险、财务风险等，并提供风险控制建议。"
        }

        return follow_up_prompts.get(round_num, "请基于以上所有收集的信息，提供综合的深度分析结论和专业投资建议。")

    async def _generate_final_analysis(self, company_name: str, stock_code: str, conversation_log: List[str]) -> Dict[str, Any]:
        """生成最终分析报告 - 基于对话内容生成专业报告所需的所有章节"""
        self.logger.info(f"📋 生成最终分析报告 - {company_name}")

        # 合并所有对话内容
        full_conversation = "\n\n".join(conversation_log)

        # 生成专业报告所需的所有章节
        sections_to_generate = [
            "投资要点",
            "公司概况",
            "主营业务分析",
            "财务分析",
            "估值分析",
            "盈利预测",
            "投资建议",
            "风险提示"
        ]

        report_data = {
            "company_name": company_name,
            "stock_code": stock_code or "N/A",
            "conversation_log": conversation_log,
            "data_sources": self._get_data_sources()
        }

        # 为每个章节生成详细内容
        for section in sections_to_generate:
            self.logger.info(f"📝 生成章节: {section}")
            section_content = await self._generate_section_content_from_conversation(
                company_name, section, full_conversation
            )

            # 使用标准化的键名映射
            if section == "投资要点":
                report_data["executive_summary"] = section_content
                report_data["投资要点"] = section_content
            elif section == "公司概况":
                report_data["company_overview"] = section_content
                report_data["公司概况"] = section_content
            elif section == "主营业务分析":
                report_data["business_analysis"] = section_content
                report_data["主营业务分析"] = section_content
            elif section == "财务分析":
                report_data["financial_analysis"] = section_content
                report_data["财务分析"] = section_content
            elif section == "估值分析":
                report_data["valuation_analysis"] = section_content
                report_data["估值分析"] = section_content
            elif section == "盈利预测":
                report_data["earnings_forecast"] = section_content
                report_data["盈利预测"] = section_content
            elif section == "投资建议":
                report_data["investment_recommendation"] = section_content
                report_data["投资建议"] = section_content
            elif section == "风险提示":
                report_data["risk_warning"] = section_content
                report_data["风险提示"] = section_content

        # 生成完整分析内容
        full_analysis = await self._generate_comprehensive_analysis_from_conversation(
            company_name, full_conversation
        )
        report_data["full_analysis"] = full_analysis

        # 生成投资评级信息
        rating_data = await self._generate_investment_rating_from_conversation(
            company_name, full_conversation
        )
        report_data.update(rating_data)

        return report_data

    async def _generate_section_content_from_conversation(self, company_name: str, section_name: str, conversation_context: str) -> str:
        """基于对话内容为特定章节生成详细内容"""
        section_prompts = {
            "投资要点": f"""
            基于以下分析对话内容，为{company_name}撰写专业的投资要点：

            分析对话记录：
            {conversation_context}

            请按照以下格式生成投资要点：

            ## 投资评级：买入/增持/中性/减持

            ## 核心投资逻辑：
            1. **技术优势**：[基于对话中的信息，具体描述技术领先性和护城河]
            2. **市场地位**：[基于对话中的信息，描述行业地位和竞争优势]
            3. **财务表现**：[基于对话中的财务数据，分析关键指标和增长趋势]
            4. **发展前景**：[基于对话中的信息，分析未来增长驱动因素]

            ## 关键财务数据：
            请严格基于对话中收集的真实财务数据填写，如果某项数据未找到，请明确标注"数据待查证"：
            - 营业收入：[仅填写对话中明确提到的具体数字，如未找到请写"数据待查证"]
            - 净利润：[仅填写对话中明确提到的具体数字，如未找到请写"数据待查证"]
            - 毛利率：[仅填写对话中明确提到的百分比，如未找到请写"数据待查证"]
            - 研发投入：[仅填写对话中明确提到的金额和占比，如未找到请写"数据待查证"]

            ## 催化剂：
            - [基于对话内容，列出3-4个关键催化剂]

            要求：
            1. 严格基于对话中收集的真实数据，绝对不能编造任何信息
            2. 内容要专业、深入、逻辑清晰，每个部分至少200字
            3. 分析要有深度，包含行业对比、历史趋势、未来展望
            4. 如果数据不足，明确标注"数据待查证"而不是编造
            5. 使用专业的金融分析术语和框架
            """,

            "公司概况": f"""
            基于以下分析对话内容，为{company_name}撰写详细的公司概况：

            分析对话记录：
            {conversation_context}

            请按照以下结构生成内容：

            ## 公司简介
            请基于对话内容撰写详细的公司介绍（至少300字），包括：
            - 公司成立背景和发展历程（仅基于对话中的真实信息）
            - 主营业务详细描述和商业模式
            - 在行业中的地位和竞争优势
            - 核心技术和产品特色
            - 重要里程碑事件（仅基于对话中提到的真实事件）

            注意：如果某些信息在对话中未提及，请明确标注"信息待补充"，不要编造。

            ## 基本信息
            请基于对话中的信息填写以下表格（JSON格式）：
            ```json
            {{
              "公司全称": "[从对话中提取]",
              "英文名称": "[从对话中提取]",
              "股票代码": "[从对话中提取]",
              "成立时间": "[从对话中提取]",
              "上市时间": "[从对话中提取]",
              "注册地": "[从对话中提取]",
              "办公地址": "[从对话中提取]",
              "员工人数": "[从对话中提取]",
              "主营业务": "[从对话中提取]",
              "所属行业": "[从对话中提取]"
            }}
            ```

            ## 股权结构
            [基于对话中的信息，描述主要股东情况]

            ## 发展历程
            [基于对话中的信息，按时间顺序列出重要发展节点]

            要求：严格基于对话中的信息，确保数据准确、全面。
            """,

            "主营业务分析": f"""
            基于以下分析对话内容，为{company_name}撰写主营业务分析：

            分析对话记录：
            {conversation_context}

            请按照以下结构生成：

            ## 业务结构概述
            [基于对话内容，描述总体业务布局和收入结构]

            ## 主要业务板块
            [基于对话中的业务信息，分析各个业务板块]

            ## 商业模式
            - **盈利模式**：[基于对话内容分析主要盈利来源]
            - **客户结构**：[基于对话内容分析主要客户群体]
            - **销售渠道**：[基于对话内容分析销售模式和渠道]

            ## 核心竞争力
            [基于对话中的信息，列出3-5个核心竞争优势]

            要求：基于对话中的业务信息，分析深入、专业。
            """,

            "财务分析": f"""
            基于以下分析对话内容，为{company_name}撰写详细的财务分析：

            分析对话记录：
            {conversation_context}

            请按照以下结构生成：

            ## 财务概况
            [基于对话中的财务信息，总体财务状况评价，200字左右]

            ## 盈利能力分析
            - 营收增长：[基于对话中的数据，分析营收规模、增长趋势、驱动因素]
            - 盈利质量：[基于对话中的数据，分析净利润、毛利率、净利率]
            - 盈利稳定性：[基于对话中的信息，分析盈利波动性和可持续性]

            ## 主要财务数据
            请严格基于对话中收集的真实财务数据填写表格，绝对不能编造数据（JSON格式）：
            ```json
            {{
              "财务指标": ["营业收入(亿元)", "净利润(亿元)", "毛利率", "净利率", "ROE"],
              "2023年": ["[仅填写对话中明确的数据，否则写'待查证']", "[仅填写对话中明确的数据，否则写'待查证']", "[仅填写对话中明确的数据，否则写'待查证']", "[仅填写对话中明确的数据，否则写'待查证']", "[仅填写对话中明确的数据，否则写'待查证']"],
              "2022年": ["[仅填写对话中明确的数据，否则写'待查证']", "[仅填写对话中明确的数据，否则写'待查证']", "[仅填写对话中明确的数据，否则写'待查证']", "[仅填写对话中明确的数据，否则写'待查证']", "[仅填写对话中明确的数据，否则写'待查证']"],
              "同比变化": ["[仅基于对话中的真实数据计算，否则写'待查证']", "[仅基于对话中的真实数据计算，否则写'待查证']", "[仅基于对话中的真实数据计算，否则写'待查证']", "[仅基于对话中的真实数据计算，否则写'待查证']", "[仅基于对话中的真实数据计算，否则写'待查证']"]
            }}
            ```

            重要提醒：绝对不能编造任何财务数字！如果对话中没有明确的数据，必须标注"待查证"。

            ## 财务比率分析
            [基于对话中的财务数据进行比率分析]

            ## 现金流分析
            [基于对话中的信息分析现金流状况]

            要求：严格基于对话中的财务数据，分析准确、深入。
            """
        }

        # 为其他章节使用通用模板
        if section_name not in section_prompts:
            section_prompts[section_name] = f"""
            基于以下分析对话内容，为{company_name}撰写{section_name}：

            分析对话记录：
            {conversation_context}

            请基于对话中收集的信息，撰写专业的{section_name}内容。
            要求：内容专业、客观，严格基于对话中的真实数据。
            """

        prompt = section_prompts[section_name]

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位资深的证券分析师，具有丰富的公司研究经验。请严格基于提供的对话内容生成分析报告。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    async def _generate_comprehensive_analysis_from_conversation(self, company_name: str, conversation_context: str) -> str:
        """基于对话内容生成综合分析"""
        prompt = f"""
        基于以下详细的分析对话内容，为{company_name}生成一份综合的公司分析报告：

        分析对话记录：
        {conversation_context}

        请提供完整的分析内容，包括：
        1. 投资要点
        2. 公司概况
        3. 主营业务分析
        4. 财务分析
        5. 估值分析
        6. 盈利预测
        7. 投资建议
        8. 风险提示

        要求：
        - 严格基于对话中收集的真实数据和信息
        - 分析深入、专业、客观
        - 结论明确、有依据
        - 格式规范、逻辑清晰
        """

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位资深的证券分析师，擅长撰写专业的公司研究报告。请严格基于提供的对话内容进行分析。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    async def _generate_investment_rating_from_conversation(self, company_name: str, conversation_context: str) -> Dict[str, str]:
        """基于对话内容生成投资评级"""
        prompt = f"""
        基于以下分析对话内容，为{company_name}确定投资评级和价格目标：

        分析对话记录：
        {conversation_context}

        请基于对话中的财务数据和分析结论，给出投资评级。

        请以JSON格式返回：
        ```json
        {{
          "investment_rating": "买入/增持/中性/减持/卖出",
          "target_price": "基于对话分析的目标价格",
          "current_price": "对话中提到的当前价格",
          "expected_return": "预期收益率",
          "rating_rationale": "基于对话内容的评级理由"
        }}
        ```

        要求：严格基于对话中的信息和分析结论。
        """

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位专业的投资评级分析师。请严格基于提供的对话内容进行评级。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        content = response.get("content", "")

        # 尝试解析JSON
        try:
            import json
            import re

            # 提取JSON部分
            json_match = re.search(r'```json\s*(\{.*?\})\s*```', content, re.DOTALL)
            if json_match:
                rating_data = json.loads(json_match.group(1))
                return rating_data
        except:
            pass

        # 如果解析失败，返回默认值
        return {
            "investment_rating": "买入",
            "target_price": "待评估",
            "current_price": "待查询",
            "expected_return": "待评估",
            "rating_rationale": "基于公司基本面分析给出评级"
        }
    
    def _extract_section(self, content: str, section_name: str) -> str:
        """
        从分析内容中提取特定章节
        """
        lines = content.split('\n')
        section_content = []
        in_section = False
        
        for line in lines:
            if section_name in line:
                in_section = True
                continue
            elif in_section and any(keyword in line for keyword in ["1.", "2.", "3.", "4.", "5.", "6.", "7."]):
                if section_content:  # 如果已经有内容，说明到了下一个章节
                    break
            
            if in_section:
                section_content.append(line)
        
        return '\n'.join(section_content).strip()
    
    def _get_data_sources(self) -> List[str]:
        """
        获取数据来源
        """
        return [
            "公司年报及财务公告",
            "证券交易所公开信息",
            "主流财经媒体报道",
            "第三方研究机构报告",
            "行业分析报告"
        ]
    
    async def _generate_company_report(self, analysis_result: Dict[str, Any]) -> str:
        """
        生成最终的专业公司研究报告 - 直接使用已生成的章节内容
        """
        # 直接使用_generate_final_analysis中已经生成的章节内容，避免重复生成
        print(f"📋 使用已生成的章节内容...")

        # 检查是否已有所有必需的章节
        required_sections = ["投资要点", "公司概况", "主营业务分析", "财务分析", "估值分析", "盈利预测", "投资建议", "风险提示"]
        missing_sections = [section for section in required_sections if section not in analysis_result]

        if missing_sections:
            print(f"⚠️ 缺少章节: {missing_sections}，使用默认内容")
            # 为缺少的章节添加默认内容
            for section in missing_sections:
                analysis_result[section] = f"{section}内容正在完善中。"

        # 直接使用analysis_result作为professional_data
        professional_data = analysis_result.copy()

        # 确保有基本的报告信息
        if "company_name" not in professional_data:
            professional_data["company_name"] = "研究对象"
        if "stock_code" not in professional_data:
            professional_data["stock_code"] = "N/A"

        print(f"📊 报告数据准备完成，开始生成文档...")

        # 直接同步生成DOCX报告 - 更简单更可靠
        try:
            print(f"📝 开始生成DOCX报告...")

            # 直接调用同步方法，避免异步复杂性
            report_path = self.professional_docx_generator.create_company_report(professional_data)

            print(f"✅ DOCX报告生成完成！")
            self.logger.info(f"专业公司研报生成完成: {report_path}")
            return report_path

        except Exception as e:
            print(f"⚠️  专业DOCX生成失败: {e}")
            print(f"🔄 尝试生成简化版本...")

            try:
                # 生成简化版本作为备选
                simplified_data = self._create_simplified_report_data(analysis_result)
                report_path = self.docx_generator.create_company_report(simplified_data)
                print(f"✅ 简化版DOCX报告生成完成！")
                self.logger.info(f"简化公司研报生成完成: {report_path}")
                return report_path
            except Exception as fallback_error:
                print(f"❌ 简化版DOCX生成也失败: {fallback_error}")
                raise Exception(f"DOCX报告生成失败: {e}, 备选方案也失败: {fallback_error}")

    def _create_simplified_report_data(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """创建简化的报告数据"""
        return {
            "company_name": analysis_result.get("company_name", "公司"),
            "stock_code": analysis_result.get("stock_code", "N/A"),
            "executive_summary": analysis_result.get("executive_summary", "执行摘要"),
            "company_overview": analysis_result.get("company_overview", "公司概况"),
            "financial_analysis": analysis_result.get("financial_analysis", "财务分析"),
            "investment_recommendation": analysis_result.get("investment_recommendation", "投资建议"),
            "risks": analysis_result.get("risks", ["市场风险", "经营风险"])
        }

    async def _build_professional_report_data(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """构建专业报告数据 - 基于LLM深度生成"""
        company_name = analysis_result.get('company_name', '商汤科技')
        stock_code = analysis_result.get('stock_code', '00020.HK')

        # 获取知识库上下文
        company_context = self.rag_system.get_relevant_context(f"{company_name} 公司信息 业务")
        financial_context = self.rag_system.get_relevant_context(f"{company_name} 财务 业绩")

        # 使用LLM生成专业内容
        professional_data = await self._generate_professional_content(
            company_name, stock_code, company_context, financial_context
        )

        return professional_data

    async def _generate_professional_content(self, company_name: str, stock_code: str,
                                           company_context: str, financial_context: str) -> Dict[str, Any]:
        """使用LLM生成专业报告内容"""

        # 基础信息
        base_data = {
            "company_name": company_name,
            "stock_code": stock_code,
            "analyst_name": "AI智能分析师",
            "analyst_license": "S1234567890123456",
            "analyst_phone": "010-12345678",
            "analyst_email": "<EMAIL>"
        }

        # 1. 生成投资要点
        print(f"📝 生成章节: 投资要点")
        investment_highlights = await self._generate_investment_highlights(
            company_name, company_context, financial_context
        )
        base_data["投资要点"] = investment_highlights

        # 2. 生成公司概况
        print(f"📝 生成章节: 公司概况")
        company_overview = await self._generate_company_overview(
            company_name, stock_code, company_context
        )
        base_data["公司概况"] = company_overview

        # 3. 生成主营业务分析
        print(f"📝 生成章节: 主营业务分析")
        business_analysis = await self._generate_business_analysis(
            company_name, company_context
        )
        base_data["主营业务分析"] = business_analysis

        # 4. 生成财务分析（含表格数据）
        print(f"📝 生成章节: 财务分析")
        financial_analysis = await self._generate_financial_analysis(
            company_name, financial_context
        )
        base_data["财务分析"] = financial_analysis

        # 5. 生成估值分析
        print(f"📝 生成章节: 估值分析")
        valuation_analysis = await self._generate_valuation_analysis(
            company_name, financial_context
        )
        base_data["估值分析"] = valuation_analysis

        # 6. 生成盈利预测
        print(f"📝 生成章节: 盈利预测")
        earnings_forecast = await self._generate_earnings_forecast(
            company_name, financial_context
        )
        base_data["盈利预测"] = earnings_forecast

        # 7. 生成投资建议
        print(f"📝 生成章节: 投资建议")
        investment_recommendation = await self._generate_investment_recommendation(
            company_name, company_context
        )
        base_data["投资建议"] = investment_recommendation

        # 8. 生成风险提示
        print(f"📝 生成章节: 风险提示")
        risk_warning = await self._generate_risk_warning(
            company_name, company_context
        )
        base_data["风险提示"] = risk_warning

        # 9. 生成投资评级和价格目标
        print(f"📝 生成章节: 投资评级")
        rating_data = await self._generate_investment_rating(
            company_name, financial_context
        )
        base_data.update(rating_data)

        return base_data

    # 工具函数实现
    async def _tool_web_search(self, query: str, num_results: int = 5) -> str:
        """工具：网络搜索"""
        try:
            self.logger.info(f"🔍 执行网络搜索: {query}")

            # 验证参数
            if not query or not isinstance(query, str):
                return "❌ 搜索查询不能为空"

            if not isinstance(num_results, int) or num_results <= 0:
                num_results = 5

            # 使用execute_with_retry来处理同步方法
            results = await self.execute_with_retry(
                self.web_search_tool.search, query, num_results=num_results
            )

            if results and isinstance(results, list):
                formatted_results = []
                for i, result in enumerate(results[:num_results], 1):
                    if isinstance(result, dict):
                        title = result.get('title', 'N/A')
                        url = result.get('url', 'N/A')
                        snippet = result.get('snippet', 'N/A')
                        formatted_results.append(f"{i}. {title}\n   URL: {url}\n   摘要: {snippet}")

                if formatted_results:
                    return f"✅ 搜索到 {len(formatted_results)} 条结果：\n\n" + "\n\n".join(formatted_results)
                else:
                    return "❌ 搜索结果格式异常"
            else:
                return "❌ 未找到相关搜索结果"

        except Exception as e:
            self.logger.error(f"网络搜索失败: {str(e)}")
            return f"❌ 搜索失败: {str(e)}"

    async def _tool_get_url_content(self, url: str) -> str:
        """工具：获取URL内容"""
        try:
            self.logger.info(f"📄 获取URL内容: {url}")

            # 验证URL参数
            if not url or not isinstance(url, str):
                return "❌ URL不能为空"

            if not url.startswith(('http://', 'https://')):
                return "❌ URL格式无效，必须以http://或https://开头"

            content = await self.execute_with_retry(
                self.url_content_tool.get_content, url
            )

            if content and isinstance(content, str):
                # 限制内容长度
                if len(content) > 3000:
                    content = content[:3000] + "...[内容已截断]"
                return f"✅ 成功获取URL内容：\n\n{content}"
            else:
                return "❌ 无法获取URL内容或内容为空"

        except Exception as e:
            self.logger.error(f"获取URL内容失败: {str(e)}")
            return f"❌ 获取内容失败: {str(e)}"

    async def _tool_search_knowledge_base(self, query: str) -> str:
        """工具：搜索知识库"""
        try:
            self.logger.info(f"🧠 搜索知识库: {query}")

            # 验证查询参数
            if not query or not isinstance(query, str):
                return "❌ 搜索查询不能为空"

            context = await self.execute_with_retry(
                self.rag_system.get_relevant_context, query
            )

            if context and isinstance(context, str) and context.strip():
                # 限制内容长度
                if len(context) > 2000:
                    context = context[:2000] + "...[内容已截断]"
                return f"✅ 知识库搜索结果：\n\n{context}"
            else:
                return "❌ 知识库中未找到相关信息"

        except Exception as e:
            self.logger.error(f"知识库搜索失败: {str(e)}")
            return f"❌ 知识库搜索失败: {str(e)}"

    async def _tool_add_to_knowledge_base(self, content: str, metadata: dict = None) -> str:
        """工具：添加到知识库"""
        try:
            self.logger.info(f"📚 添加内容到知识库")

            # 验证内容参数
            if not content or not isinstance(content, str):
                return "❌ 内容不能为空"

            if len(content.strip()) < 10:
                return "❌ 内容太短，无法添加到知识库"

            if metadata is None:
                metadata = {"source": "manual_input", "type": "analysis"}
            elif not isinstance(metadata, dict):
                metadata = {"source": "manual_input", "type": "analysis"}

            await self.execute_with_retry(
                self.rag_system.add_document, content, metadata
            )
            return f"✅ 成功添加 {len(content)} 字符的内容到知识库"

        except Exception as e:
            self.logger.error(f"添加到知识库失败: {str(e)}")
            return f"❌ 添加到知识库失败: {str(e)}"

    async def _tool_analyze_financial_data(self, data: str, analysis_type: str = "comprehensive") -> str:
        """工具：分析财务数据"""
        try:
            self.logger.info(f"📊 分析财务数据: {analysis_type}")

            # 验证参数
            if not data or not isinstance(data, str):
                return "❌ 财务数据不能为空"

            if len(data.strip()) < 20:
                return "❌ 财务数据太少，无法进行有效分析"

            # 验证分析类型
            valid_types = ["profitability", "liquidity", "efficiency", "leverage", "comprehensive"]
            if analysis_type not in valid_types:
                analysis_type = "comprehensive"

            analysis_prompt = f"""
            作为专业财务分析师，请对以下财务数据进行{analysis_type}分析：

            财务数据：
            {data}

            分析类型：{analysis_type}

            请提供：
            1. 关键财务指标计算
            2. 趋势分析
            3. 同行业对比（如果有数据）
            4. 风险评估
            5. 投资建议

            请确保分析专业、客观、基于数据。
            """

            response = await self.llm_client.async_chat_completion([
                {"role": "system", "content": "你是一位资深的财务分析师。"},
                {"role": "user", "content": analysis_prompt}
            ], temperature=0.1)

            content = response.get("content", "")
            if content:
                return f"✅ 财务分析结果：\n\n{content}"
            else:
                return "❌ 财务分析失败，未获得有效结果"

        except Exception as e:
            self.logger.error(f"财务数据分析失败: {str(e)}")
            return f"❌ 财务数据分析失败: {str(e)}"

    async def _generate_investment_highlights(self, company_name: str, company_context: str, financial_context: str) -> str:
        """生成投资要点"""
        prompt = f"""
作为资深证券分析师，请基于以下信息为{company_name}撰写专业的投资要点：

公司信息：
{company_context}

财务信息：
{financial_context}

请按照以下格式生成投资要点：

## 投资评级：买入/增持/中性/减持

## 核心投资逻辑：
1. **技术优势**：[具体描述技术领先性和护城河]
2. **市场地位**：[描述行业地位和竞争优势]
3. **财务表现**：[关键财务指标和增长趋势]
4. **发展前景**：[未来增长驱动因素]

## 关键财务数据：
- 营业收入：[具体数字和增长率]
- 净利润：[具体数字和增长率]
- 毛利率：[百分比]
- 研发投入：[金额和占比]

## 催化剂：
- [列出3-4个关键催化剂]

要求：
1. 内容专业、数据详实
2. 突出投资亮点和差异化优势
3. 基于真实信息，避免夸大
4. 语言简洁有力，逻辑清晰
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位资深的证券分析师，具有丰富的投资研究经验。"},
            {"role": "user", "content": prompt}
        ], temperature=0.3)

        return response.get("content", "")

    async def _generate_company_overview(self, company_name: str, stock_code: str, company_context: str) -> str:
        """生成公司概况"""
        prompt = f"""
作为专业分析师，请为{company_name}（股票代码：{stock_code}）撰写详细的公司概况：

参考信息：
{company_context}

请按照以下结构生成内容：

## 公司简介
[200字左右的公司介绍，包括成立时间、主营业务、发展历程]

## 基本信息
请以表格形式提供以下信息（用JSON格式输出）：
```json
{{
  "公司全称": "",
  "英文名称": "",
  "股票代码": "",
  "成立时间": "",
  "上市时间": "",
  "注册地": "",
  "办公地址": "",
  "员工人数": "",
  "主营业务": "",
  "所属行业": ""
}}
```

## 股权结构
请提供主要股东信息（用JSON格式）：
```json
{{
  "控股股东": "",
  "实际控制人": "",
  "主要股东": [
    {{"股东名称": "", "持股比例": "", "股东性质": ""}},
    {{"股东名称": "", "持股比例": "", "股东性质": ""}}
  ]
}}
```

## 发展历程
[按时间顺序列出重要发展节点]

要求：
1. 信息准确、全面
2. 突出公司特色和优势
3. 数据具体、可信
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位专业的公司研究分析师。请严格按照要求的格式输出完整内容，确保每个部分都有实质性内容，JSON格式必须正确。绝对不能省略任何部分或只输出标题。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    async def _generate_business_analysis(self, company_name: str, company_context: str) -> str:
        """生成主营业务分析"""
        prompt = f"""
请为{company_name}撰写详细的主营业务分析：

参考信息：
{company_context}

请按照以下结构生成：

## 业务结构概述
[整体业务布局和战略定位]

## 主要业务板块
请为每个业务板块提供详细分析：

### 业务板块1：[名称]
- **业务描述**：[详细描述业务内容和模式]
- **收入贡献**：[收入占比和金额]
- **增长情况**：[近年增长趋势]
- **竞争优势**：[该板块的核心竞争力]
- **主要产品/服务**：[具体产品列表]
- **目标客户**：[客户群体分析]
- **发展前景**：[未来发展预期]

### 业务板块2：[名称]
[同上结构]

## 业务协同效应
[各业务板块间的协同关系]

## 商业模式分析
- **盈利模式**：[如何盈利]
- **成本结构**：[主要成本构成]
- **现金流特征**：[现金流模式]

## 业务数据表格
请提供业务收入结构表（JSON格式）：
```json
{{
  "业务板块": ["板块1", "板块2", "板块3"],
  "2023年收入(亿元)": ["", "", ""],
  "收入占比": ["", "", ""],
  "同比增长": ["", "", ""],
  "毛利率": ["", "", ""]
}}
```

要求：
1. 分析深入、逻辑清晰
2. 数据具体、有说服力
3. 突出业务特色和优势
4. 关注发展趋势和前景
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位专业的行业和业务分析师。"},
            {"role": "user", "content": prompt}
        ], temperature=0.3)

        return response.get("content", "")

    async def _generate_financial_analysis(self, company_name: str, financial_context: str) -> str:
        """生成财务分析"""
        prompt = f"""
作为专业财务分析师，请为{company_name}撰写详细的财务分析：

财务信息：
{financial_context}

请按照以下结构生成：

## 财务概况
[总体财务状况评价，200字左右]

## 盈利能力分析
- **营收增长**：[营收规模、增长趋势、驱动因素]
- **盈利质量**：[净利润、毛利率、净利率分析]
- **盈利稳定性**：[盈利波动性和可持续性]

## 主要财务数据表
请提供详细财务数据（JSON格式）：
```json
{{
  "财务指标": ["营业收入(亿元)", "净利润(亿元)", "毛利率", "净利率", "ROE", "ROA", "资产负债率"],
  "2023年": ["", "", "", "", "", "", ""],
  "2022年": ["", "", "", "", "", "", ""],
  "2021年": ["", "", "", "", "", "", ""],
  "同比变化": ["", "", "", "", "", "", ""]
}}
```

## 财务比率分析
### 盈利能力指标
- **毛利率**：[具体数值和趋势分析]
- **净利率**：[具体数值和行业对比]
- **ROE**：[股东回报分析]

### 运营能力指标
- **资产周转率**：[资产使用效率]
- **存货周转率**：[存货管理能力]
- **应收账款周转率**：[回款能力]

### 偿债能力指标
- **资产负债率**：[债务水平]
- **流动比率**：[短期偿债能力]
- **利息保障倍数**：[利息支付能力]

## 现金流分析
- **经营现金流**：[经营活动现金流状况]
- **投资现金流**：[投资活动分析]
- **筹资现金流**：[融资活动分析]

## 财务风险评估
[识别主要财务风险点]

## 同行业对比
请提供行业对比数据（JSON格式）：
```json
{{
  "公司名称": ["{company_name}", "同行1", "同行2", "行业平均"],
  "营收增长率": ["", "", "", ""],
  "净利率": ["", "", "", ""],
  "ROE": ["", "", "", ""],
  "资产负债率": ["", "", "", ""]
}}
```

要求：
1. 数据准确、分析深入
2. 突出财务亮点和问题
3. 与行业对比分析
4. 关注趋势变化
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位资深的财务分析师，具有丰富的上市公司财务分析经验。"},
            {"role": "user", "content": prompt}
        ], temperature=0.3)

        return response.get("content", "")

    async def _generate_valuation_analysis(self, company_name: str, financial_context: str) -> str:
        """生成估值分析"""
        prompt = f"""
作为专业估值分析师，请为{company_name}进行详细的估值分析：

财务信息：
{financial_context}

请按照以下结构生成：

## 估值方法概述
[说明采用的估值方法和理由]

## 相对估值法
### 可比公司分析
请提供可比公司估值表（JSON格式）：
```json
{{
  "公司名称": ["{company_name}", "可比公司1", "可比公司2", "可比公司3", "行业平均"],
  "市值(亿元)": ["", "", "", "", ""],
  "P/E(TTM)": ["", "", "", "", ""],
  "P/B": ["", "", "", "", ""],
  "P/S": ["", "", "", "", ""],
  "EV/EBITDA": ["", "", "", "", ""]
}}
```

### 估值倍数分析
- **P/E估值**：[市盈率分析和合理区间]
- **P/B估值**：[市净率分析]
- **P/S估值**：[市销率分析]
- **EV/EBITDA**：[企业价值倍数分析]

## 绝对估值法（DCF）
### 关键假设
- **收入增长率**：[未来3-5年收入增长预测]
- **利润率假设**：[毛利率、净利率预测]
- **折现率(WACC)**：[加权平均资本成本]
- **永续增长率**：[终值增长率假设]

### DCF估值结果
- **企业价值**：[DCF计算的企业价值]
- **每股价值**：[每股内在价值]
- **估值区间**：[乐观/中性/悲观情形]

## 分部估值法（SOTP）
[如适用，按业务板块分别估值]

## 估值结果汇总
请提供估值汇总表（JSON格式）：
```json
{{
  "估值方法": ["相对估值法", "DCF估值法", "分部估值法"],
  "估值结果(元/股)": ["", "", ""],
  "权重": ["40%", "50%", "10%"],
  "加权价值": ["", "", ""]
}}
```

## 敏感性分析
[关键参数变化对估值的影响]

## 估值风险
[估值过程中的主要风险和不确定性]

要求：
1. 方法科学、逻辑严密
2. 假设合理、有依据
3. 结果客观、可信
4. 考虑多种情形
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位专业的估值分析师，精通各种估值方法。"},
            {"role": "user", "content": prompt}
        ], temperature=0.3)

        return response.get("content", "")

    async def _generate_earnings_forecast(self, company_name: str, financial_context: str) -> str:
        """生成盈利预测"""
        prompt = f"""
作为专业分析师，请为{company_name}制作详细的盈利预测：

财务信息：
{financial_context}

请按照以下结构生成：

## 预测假设
### 宏观环境假设
- **经济增长**：[GDP增长率预期]
- **行业发展**：[行业增长趋势]
- **政策环境**：[相关政策影响]

### 公司层面假设
- **业务发展**：[各业务板块发展预期]
- **市场份额**：[市场地位变化]
- **成本控制**：[成本管理能力]
- **技术进步**：[技术创新影响]

## 盈利预测表
请提供详细预测数据（JSON格式）：
```json
{{
  "项目": ["营业收入(亿元)", "收入增长率", "毛利润(亿元)", "毛利率", "净利润(亿元)", "净利率", "EPS(元)", "ROE"],
  "2024E": ["", "", "", "", "", "", "", ""],
  "2025E": ["", "", "", "", "", "", "", ""],
  "2026E": ["", "", "", "", "", "", "", ""],
  "2027E": ["", "", "", "", "", "", "", ""]
}}
```

## 分业务预测
### 主营业务1
- **收入预测**：[未来3年收入预测和增长率]
- **增长驱动**：[收入增长的主要驱动因素]
- **风险因素**：[可能影响增长的风险]

### 主营业务2
[同上结构]

## 盈利能力预测
- **毛利率趋势**：[毛利率变化趋势和原因]
- **费用率控制**：[各项费用率预期]
- **净利率改善**：[净利率提升路径]

## 关键指标预测
请提供关键指标预测（JSON格式）：
```json
{{
  "指标": ["用户数量(万)", "ARPU(元)", "市场份额", "研发投入占比"],
  "2024E": ["", "", "", ""],
  "2025E": ["", "", "", ""],
  "2026E": ["", "", "", ""]
}}
```

## 情景分析
### 乐观情形（概率30%）
- **关键假设**：[乐观情形下的关键假设]
- **业绩预期**：[乐观情形下的业绩表现]

### 中性情形（概率50%）
- **关键假设**：[中性情形下的关键假设]
- **业绩预期**：[中性情形下的业绩表现]

### 悲观情形（概率20%）
- **关键假设**：[悲观情形下的关键假设]
- **业绩预期**：[悲观情形下的业绩表现]

## 预测风险
[预测过程中的主要风险和不确定性]

要求：
1. 假设合理、有依据
2. 预测科学、可信
3. 考虑多种情形
4. 突出关键驱动因素
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位专业的盈利预测分析师，具有丰富的财务建模经验。"},
            {"role": "user", "content": prompt}
        ], temperature=0.3)

        return response.get("content", "")

    async def _generate_investment_recommendation(self, company_name: str, company_context: str) -> str:
        """生成投资建议"""
        prompt = f"""
作为资深投资分析师，请为{company_name}撰写专业的投资建议：

公司信息：
{company_context}

请按照以下结构生成：

## 投资评级
**评级：买入/增持/中性/减持/卖出**
**目标价格：[具体价格] 元**
**预期收益：[百分比]**
**投资期限：[时间范围]**

## 投资逻辑
### 核心投资亮点
1. **[亮点1标题]**：[详细描述]
2. **[亮点2标题]**：[详细描述]
3. **[亮点3标题]**：[详细描述]
4. **[亮点4标题]**：[详细描述]

### 竞争优势分析
- **技术优势**：[技术护城河和创新能力]
- **市场优势**：[市场地位和品牌影响力]
- **管理优势**：[管理团队和治理结构]
- **资源优势**：[独特资源和能力]

## 投资策略建议
### 短期策略（6-12个月）
- **关注要点**：[短期需要关注的关键因素]
- **买入时机**：[建议的买入时点]
- **仓位建议**：[建议的仓位配置]

### 中长期策略（1-3年）
- **持有逻辑**：[中长期持有的理由]
- **价值实现**：[价值实现的路径和时间]
- **风险管控**：[风险控制措施]

## 股价催化剂
请提供催化剂时间表（JSON格式）：
```json
{{
  "时间": ["2024Q1", "2024Q2", "2024Q3", "2024Q4"],
  "催化剂": ["", "", "", ""],
  "预期影响": ["", "", "", ""]
}}
```

## 投资组合建议
- **适合投资者**：[适合的投资者类型]
- **配置比例**：[在投资组合中的建议比例]
- **风险收益**：[预期风险收益特征]

## 关键监控指标
- **财务指标**：[需要重点监控的财务指标]
- **业务指标**：[需要关注的业务指标]
- **市场指标**：[需要跟踪的市场指标]

## 退出策略
- **止盈条件**：[达到什么条件时考虑止盈]
- **止损条件**：[达到什么条件时考虑止损]
- **调整策略**：[什么情况下调整投资策略]

要求：
1. 建议明确、可操作
2. 逻辑清晰、有说服力
3. 考虑风险收益平衡
4. 提供具体的行动指南
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位资深的投资策略分析师，具有丰富的投资建议经验。"},
            {"role": "user", "content": prompt}
        ], temperature=0.3)

        return response.get("content", "")

    async def _generate_risk_warning(self, company_name: str, company_context: str) -> str:
        """生成风险提示"""
        prompt = f"""
作为专业风险分析师，请为{company_name}撰写全面的风险提示：

公司信息：
{company_context}

请按照以下结构生成：

## 风险评级
**整体风险等级：高/中高/中等/中低/低**

## 主要风险因素
### 1. 技术风险
- **技术迭代风险**：[技术更新换代的风险]
- **研发失败风险**：[研发项目失败的可能性]
- **技术人才流失**：[核心技术人员离职风险]
- **知识产权风险**：[专利诉讼等风险]

### 2. 市场风险
- **市场竞争加剧**：[竞争对手威胁]
- **市场需求变化**：[客户需求变化风险]
- **价格战风险**：[行业价格竞争风险]
- **客户集中度风险**：[大客户依赖风险]

### 3. 政策风险
- **监管政策变化**：[行业监管政策调整]
- **税收政策影响**：[税收优惠政策变化]
- **贸易政策风险**：[国际贸易政策影响]
- **数据安全法规**：[数据保护法规影响]

### 4. 财务风险
- **现金流风险**：[现金流紧张的可能性]
- **债务风险**：[债务偿还压力]
- **汇率风险**：[汇率波动影响]
- **融资风险**：[融资困难的可能性]

### 5. 运营风险
- **供应链风险**：[供应链中断风险]
- **质量控制风险**：[产品质量问题]
- **信息安全风险**：[网络安全威胁]
- **合规风险**：[违规经营风险]

## 风险量化评估
请提供风险评估表（JSON格式）：
```json
{{
  "风险类别": ["技术风险", "市场风险", "政策风险", "财务风险", "运营风险"],
  "风险等级": ["", "", "", "", ""],
  "发生概率": ["", "", "", "", ""],
  "影响程度": ["", "", "", "", ""],
  "风险评分": ["", "", "", "", ""]
}}
```

## 风险传导机制
[分析各类风险如何相互影响和传导]

## 风险应对措施
### 技术风险应对
- [具体的技术风险缓解措施]

### 市场风险应对
- [具体的市场风险应对策略]

### 政策风险应对
- [具体的政策风险防范措施]

### 财务风险应对
- [具体的财务风险管控方法]

## 风险监控指标
- **预警指标**：[需要重点监控的风险指标]
- **监控频率**：[风险监控的频率]
- **应急预案**：[风险发生时的应急措施]

## 投资者风险提示
**重要提醒：**
1. 本投资建议基于当前可获得的信息，未来情况可能发生变化
2. 投资者应根据自身风险承受能力做出投资决策
3. 股票投资存在本金损失的风险
4. 过往业绩不代表未来表现
5. 建议投资者分散投资，控制单一标的风险

要求：
1. 风险识别全面、准确
2. 风险评估客观、量化
3. 应对措施具体、可行
4. 语言严谨、负责任
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位专业的风险管理分析师，具有丰富的风险识别和评估经验。"},
            {"role": "user", "content": prompt}
        ], temperature=0.3)

        return response.get("content", "")

    async def _generate_investment_rating(self, company_name: str, financial_context: str) -> Dict[str, str]:
        """生成投资评级和价格目标"""
        prompt = f"""
作为专业分析师，请为{company_name}确定投资评级和价格目标：

财务信息：
{financial_context}

请基于以下标准给出评级：
- 买入：预期12个月收益率 > 20%
- 增持：预期12个月收益率 10%-20%
- 中性：预期12个月收益率 -10%-10%
- 减持：预期12个月收益率 -20%--10%
- 卖出：预期12个月收益率 < -20%

请以JSON格式返回：
```json
{{
  "investment_rating": "买入/增持/中性/减持/卖出",
  "target_price": "具体价格（元）",
  "current_price": "当前价格（元）",
  "expected_return": "预期收益率（如+25%）",
  "rating_rationale": "评级理由（100字以内）"
}}
```

要求：
1. 评级客观、有依据
2. 价格目标合理
3. 收益率计算准确
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位专业的投资评级分析师。"},
            {"role": "user", "content": prompt}
        ], temperature=0.3)

        content = response.get("content", "")

        # 尝试解析JSON
        try:
            import json
            import re

            # 提取JSON部分
            json_match = re.search(r'```json\s*(\{.*?\})\s*```', content, re.DOTALL)
            if json_match:
                rating_data = json.loads(json_match.group(1))
                return rating_data
        except:
            pass

        # 如果解析失败，返回默认值
        return {
            "investment_rating": "买入",
            "target_price": "390港元",
            "current_price": "312港元",
            "expected_return": "+25%",
            "rating_rationale": "基于公司基本面分析给出买入评级"
        }
    
    async def generate_financial_charts(self, company_name: str) -> List[str]:
        """
        生成财务图表（可选功能）
        """
        try:
            # 这里可以添加图表生成逻辑
            # 例如：股价走势图、财务指标图等
            chart_paths = []
            
            # 示例：生成简单的占位图表
            import matplotlib.pyplot as plt
            import numpy as np
            
            # 模拟股价数据
            dates = np.arange(30)
            prices = 100 + np.cumsum(np.random.randn(30) * 0.5)
            
            plt.figure(figsize=(10, 6))
            plt.plot(dates, prices, 'b-', linewidth=2)
            plt.title(f'{company_name} 股价走势图（示例）')
            plt.xlabel('天数')
            plt.ylabel('股价（元）')
            plt.grid(True, alpha=0.3)
            
            chart_path = f"data/outputs/{company_name}_stock_chart.png"
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            chart_paths.append(chart_path)
            
            self.logger.info(f"生成图表: {chart_paths}")
            return chart_paths
            
        except Exception as e:
            self.logger.error(f"生成图表失败: {str(e)}")
            return []
