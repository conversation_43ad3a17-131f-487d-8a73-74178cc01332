#!/usr/bin/env python3
# test_intelligent_agent.py
"""
智能金融研报生成Agent测试脚本
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.agents.intelligent_agent import IntelligentFinancialAgent
from config.settings import Config

async def test_intelligent_agent():
    """测试智能Agent"""
    print("🚀 开始测试智能金融研报生成Agent")
    print("=" * 60)
    
    # 初始化配置和Agent
    config = Config()
    config.create_directories()
    
    agent = IntelligentFinancialAgent(config)
    
    # 测试用例
    test_cases = [
        {
            "name": "简单目标测试",
            "goal": "生成商汤科技的公司研报",
            "requirements": None
        },
        {
            "name": "复合目标测试", 
            "goal": "生成智能风控行业分析和生成式AI投资趋势报告",
            "requirements": "重点关注中国市场"
        },
        {
            "name": "比赛模式测试",
            "goal": "生成比赛要求的三份研报：商汤科技公司研报、智能风控行业研报、生成式AI宏观研报",
            "requirements": "符合比赛提交格式要求"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {test_case['name']}")
        print(f"🎯 目标: {test_case['goal']}")
        if test_case['requirements']:
            print(f"📝 要求: {test_case['requirements']}")
        print("-" * 40)
        
        try:
            start_time = datetime.now()
            
            # 执行测试
            result = await agent.run(test_case['goal'], test_case['requirements'])
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # 记录结果
            test_result = {
                "test_case": test_case['name'],
                "success": result.get('success', False),
                "duration": duration,
                "summary": result.get('summary', ''),
                "generated_reports": result.get('generated_reports', {}),
                "errors": result.get('errors', [])
            }
            
            results.append(test_result)
            
            # 打印结果
            print(f"✅ 执行状态: {'成功' if test_result['success'] else '失败'}")
            print(f"⏱️  执行时间: {duration:.2f} 秒")
            print(f"📊 摘要: {test_result['summary']}")
            
            if test_result['generated_reports']:
                print("📁 生成的报告:")
                for report_type, path in test_result['generated_reports'].items():
                    print(f"   {report_type}: {path}")
            
            if test_result['errors']:
                print("❌ 错误:")
                for error in test_result['errors']:
                    print(f"   - {error}")
                    
        except Exception as e:
            print(f"💥 测试失败: {str(e)}")
            results.append({
                "test_case": test_case['name'],
                "success": False,
                "error": str(e)
            })
    
    # 打印总结
    print("\n" + "=" * 60)
    print("📈 测试总结")
    print("=" * 60)
    
    success_count = sum(1 for r in results if r.get('success', False))
    total_count = len(results)
    
    print(f"✅ 成功: {success_count}/{total_count}")
    print(f"❌ 失败: {total_count - success_count}/{total_count}")
    
    if success_count > 0:
        avg_duration = sum(r.get('duration', 0) for r in results if r.get('success', False)) / success_count
        print(f"⏱️  平均执行时间: {avg_duration:.2f} 秒")
    
    print("\n📋 详细结果:")
    for result in results:
        status = "✅" if result.get('success', False) else "❌"
        print(f"{status} {result['test_case']}")
        if not result.get('success', False) and 'error' in result:
            print(f"   错误: {result['error']}")
    
    print("\n🎉 测试完成!")
    return results

async def test_function_calling():
    """测试Function Calling功能"""
    print("\n🔧 测试Function Calling功能")
    print("-" * 40)
    
    config = Config()
    agent = IntelligentFinancialAgent(config)
    
    # 测试工具注册
    tools = agent.function_manager.get_registered_tools()
    print(f"📋 已注册工具数量: {len(tools)}")
    print("🛠️  工具列表:")
    for tool in tools:
        print(f"   - {tool}")
    
    # 测试简单工具调用
    try:
        print("\n🔍 测试网络搜索工具...")
        search_result = await agent.function_manager.simple_tool_call(
            "web_search", 
            query="商汤科技 最新新闻", 
            num_results=3
        )
        print(f"搜索结果长度: {len(search_result)} 字符")
        print("✅ 网络搜索工具测试成功")
        
    except Exception as e:
        print(f"❌ 网络搜索工具测试失败: {e}")
    
    # 测试知识库工具
    try:
        print("\n📚 测试知识库工具...")
        add_result = await agent.function_manager.simple_tool_call(
            "add_to_knowledge_base",
            content="这是一个测试文档，用于验证知识库功能。",
            metadata={"type": "test", "timestamp": datetime.now().isoformat()}
        )
        print(f"添加结果: {add_result}")
        
        search_result = await agent.function_manager.simple_tool_call(
            "search_knowledge_base",
            query="测试文档",
            top_k=1
        )
        print(f"搜索结果长度: {len(search_result)} 字符")
        print("✅ 知识库工具测试成功")
        
    except Exception as e:
        print(f"❌ 知识库工具测试失败: {e}")

def main():
    """主函数"""
    print("🤖 智能金融研报生成Agent - 测试程序")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 运行测试
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        # 测试Function Calling
        loop.run_until_complete(test_function_calling())
        
        # 测试智能Agent
        results = loop.run_until_complete(test_intelligent_agent())
        
        # 检查是否有成功的测试
        if any(r.get('success', False) for r in results):
            print("\n🎊 恭喜！智能Agent测试通过，系统可以正常工作！")
            return 0
        else:
            print("\n⚠️  所有测试都失败了，请检查配置和网络连接。")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        loop.close()

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
