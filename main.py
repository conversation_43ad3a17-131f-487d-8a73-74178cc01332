# main.py
import argparse
import sys
import os
from datetime import datetime
from typing import Dict, Any
from src.agents.financial_agent import FinancialReportAgent
from src.agents.company_agent import CompanyAgent
from src.agents.industry_agent import IndustryAgent
from src.agents.macro_agent import MacroAgent
from src.agents.intelligent_agent import IntelligentFinancialAgent
from config.settings import Config
from src.utils.logger import get_logger
from src.models.data_models import ReportType, TaskInfo, TaskStatus

class FinancialReportSystem:
    """
    金融研报生成系统主程序
    """
    
    def __init__(self):
        self.config = Config()
        self.config.create_directories()
        self.logger = get_logger("FinancialReportSystem")
        
        # 初始化Agent
        self.financial_agent = FinancialReportAgent(self.config)
        self.company_agent = CompanyAgent(self.config)
        self.industry_agent = IndustryAgent(self.config)
        self.macro_agent = MacroAgent(self.config)

        # 初始化智能Agent（新的主控Agent）
        self.intelligent_agent = IntelligentFinancialAgent(self.config)
        
        self.logger.info("金融研报生成系统初始化完成")

    def generate_intelligent_reports(self, goal: str, requirements: str = None) -> Dict[str, Any]:
        """
        使用智能Agent生成报告 - 新的智能模式

        Args:
            goal: 用户目标描述
            requirements: 具体要求

        Returns:
            执行结果
        """
        self.logger.info(f"启动智能模式生成报告: {goal}")

        try:
            # 使用智能Agent自主决策和执行
            results = self.intelligent_agent.run(goal, requirements)

            self.logger.info("智能模式报告生成完成")
            return results

        except Exception as e:
            self.logger.error(f"智能模式生成报告失败: {str(e)}", exception=e)
            raise e
    
    def generate_single_report(self, report_type: str, target: str) -> str:
        """
        生成单个报告
        
        Args:
            report_type: 报告类型 (company/industry/macro)
            target: 目标对象
            
        Returns:
            生成的报告文件路径
        """
        self.logger.info(f"开始生成{report_type}报告: {target}")
        
        try:
            if report_type == "company":
                # 处理股票代码
                if "(" in target and ")" in target:
                    company_name = target.split("(")[0].strip()
                    stock_code = target.split("(")[1].split(")")[0].strip()
                else:
                    company_name = target
                    stock_code = None
                
                report_path = self.company_agent.run(company_name, stock_code)
                
            elif report_type == "industry":
                report_path = self.industry_agent.run(target)
                
            elif report_type == "macro":
                report_path = self.macro_agent.run(target)
                
            else:
                raise ValueError(f"不支持的报告类型: {report_type}")
            
            self.logger.info(f"{report_type}报告生成完成: {report_path}")
            return report_path
            
        except Exception as e:
            self.logger.error(f"生成{report_type}报告失败: {str(e)}", exception=e)
            raise e
    
    def generate_all_reports(self, targets: dict) -> dict:
        """
        生成所有类型的报告
        
        Args:
            targets: 目标字典，包含company, industry, macro
            
        Returns:
            报告路径字典
        """
        self.logger.info(f"开始生成所有报告: {targets}")
        
        # 使用金融Agent进行统一生成
        try:
            report_paths = self.financial_agent.generate_reports(targets)
            
            self.logger.info("所有报告生成完成")
            return report_paths
            
        except Exception as e:
            self.logger.error(f"生成报告失败: {str(e)}", exception=e)
            raise e
    
    def print_report_summary(self, report_paths: dict):
        """
        打印报告生成摘要
        """
        print("\n" + "="*60)
        print("📊 金融研报生成完成")
        print("="*60)
        
        success_count = 0
        total_count = len(report_paths)
        
        for report_type, path in report_paths.items():
            if isinstance(path, str) and os.path.exists(path):
                print(f"✅ {report_type.upper()}报告: {path}")
                success_count += 1
            else:
                print(f"❌ {report_type.upper()}报告: 生成失败")
        
        print("="*60)
        print(f"📈 成功生成 {success_count}/{total_count} 份报告")
        
        if success_count == total_count:
            print("🎉 所有报告生成成功！")
        elif success_count > 0:
            print("⚠️  部分报告生成成功")
        else:
            print("💥 所有报告生成失败")
        
        print("="*60)
        print(f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📁 输出目录: {self.config.REPORT_OUTPUT_DIR}")
        print("="*60)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='🚀 金融研报AI生成系统',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  🤖 智能模式（推荐）:
    python main.py --intelligent
    python main.py --goal "生成商汤科技的投资分析报告"
    python main.py --goal "分析AI行业发展趋势" --requirements "重点关注中国市场"

  📊 传统模式:
    python main.py  # 生成所有报告
    python main.py --company "商汤科技（00020.HK）"
    python main.py --industry "智能风控&大数据征信服务"
    python main.py --macro "生成式AI基建与算力投资趋势"

  🏆 比赛模式:
    python main.py --intelligent  # 自动生成比赛要求的三份报告
        """
    )
    
    parser.add_argument('--company', type=str, help='公司名称（可包含股票代码）')
    parser.add_argument('--industry', type=str, help='行业名称')
    parser.add_argument('--macro', type=str, help='宏观主题')
    parser.add_argument('--goal', type=str, help='智能模式：描述你的目标，AI将自主决策完成任务')
    parser.add_argument('--requirements', type=str, help='智能模式：具体要求（可选）')
    parser.add_argument('--intelligent', action='store_true', help='启用智能模式（推荐）')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--output-dir', type=str, help='输出目录')
    parser.add_argument('--log-level', type=str, choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='日志级别')
    parser.add_argument('--version', action='version', version='金融研报AI生成系统 v2.0.0')
    
    args = parser.parse_args()
    
    # 创建系统实例
    try:
        system = FinancialReportSystem()
        
        # 更新配置
        if args.output_dir:
            system.config.REPORT_OUTPUT_DIR = args.output_dir
            os.makedirs(args.output_dir, exist_ok=True)
        
        if args.log_level:
            system.config.LOG_LEVEL = args.log_level
        
        # 检查是否使用智能模式
        if args.intelligent or args.goal:
            # 智能模式
            goal = args.goal or "生成金融研报"

            # 如果没有明确目标但指定了传统参数，构建目标
            if not args.goal and (args.company or args.industry or args.macro):
                goal_parts = []
                if args.company:
                    goal_parts.append(f"公司研报: {args.company}")
                if args.industry:
                    goal_parts.append(f"行业研报: {args.industry}")
                if args.macro:
                    goal_parts.append(f"宏观研报: {args.macro}")
                goal = "生成 " + ", ".join(goal_parts)

            # 如果没有任何指定，使用比赛默认目标
            if goal == "生成金融研报":
                goal = "生成商汤科技公司研报、智能风控行业研报和生成式AI宏观研报，符合比赛要求"

            print("🤖 智能模式启动")
            print(f"🎯 目标: {goal}")
            if args.requirements:
                print(f"📋 要求: {args.requirements}")
            print("-" * 60)

            # 使用智能Agent生成报告
            results = system.generate_intelligent_reports(goal, args.requirements)

            # 打印智能模式结果
            print("\n" + "="*60)
            print("🤖 智能模式执行完成")
            print("="*60)
            print(f"✅ 执行状态: {'成功' if results.get('success') else '失败'}")
            print(f"📊 摘要: {results.get('summary', '无摘要')}")

            if results.get("generated_reports"):
                print("\n📁 生成的报告:")
                for report_type, path in results["generated_reports"].items():
                    print(f"   {report_type.upper()}: {path}")

            if results.get("zip_file"):
                print(f"\n📦 提交文件: {results['zip_file']}")

            if results.get("errors"):
                print(f"\n❌ 错误信息:")
                for error in results["errors"]:
                    print(f"   - {error}")

            print("="*60)
            return 0

        else:
            # 传统模式
            targets = {}

            if args.company:
                targets["company"] = args.company
            if args.industry:
                targets["industry"] = args.industry
            if args.macro:
                targets["macro"] = args.macro

            # 如果没有指定任何目标，使用默认值（比赛题目）
            if not targets:
                targets = {
                    "company": "商汤科技（00020.HK）",
                    "industry": "智能风控&大数据征信服务",
                    "macro": "生成式AI基建与算力投资趋势（2023-2026）"
                }

                print("🔧 未指定目标，使用比赛默认配置...")

            print("🚀 金融研报AI生成系统启动（传统模式）")
            print(f"📋 生成目标: {list(targets.keys())}")
            for report_type, target in targets.items():
                print(f"   {report_type}: {target}")
            print("-" * 60)
        
        # 生成报告
        if len(targets) == 1:
            # 单个报告
            report_type = list(targets.keys())[0]
            target = list(targets.values())[0]
            report_path = system.generate_single_report(report_type, target)
            report_paths = {report_type: report_path}
        else:
            # 多个报告
            report_paths = system.generate_all_reports(targets)
        
        # 打印摘要
        system.print_report_summary(report_paths)
        
        # 检查是否为比赛模式（生成3个特定报告）
        if len(targets) == 3 and all(key in targets for key in ["company", "industry", "macro"]):
            print("\n🏆 比赛模式检测到！")
            print("📁 可直接打包提交以下文件:")
            for report_type, path in report_paths.items():
                if isinstance(path, str) and os.path.exists(path):
                    filename = os.path.basename(path)
                    print(f"   {filename}")
            
            # 创建results.zip
            try:
                import zipfile
                zip_path = os.path.join(system.config.REPORT_OUTPUT_DIR, "results.zip")
                
                with zipfile.ZipFile(zip_path, 'w') as zipf:
                    for report_type, path in report_paths.items():
                        if isinstance(path, str) and os.path.exists(path):
                            filename = os.path.basename(path)
                            zipf.write(path, filename)
                
                print(f"📦 已创建提交文件: {zip_path}")
                
            except Exception as e:
                print(f"⚠️  创建ZIP文件失败: {e}")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⏹️  用户取消操作")
        return 1
    except Exception as e:
        print(f"\n💥 系统错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️  程序被用户中断")
        sys.exit(1)
