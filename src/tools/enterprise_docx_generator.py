# src/tools/enterprise_docx_generator.py
"""
企业级DOCX报告生成器
基于世界级投行标准，生成专业格式的研报
"""

import os
import re
import json
from datetime import datetime
from typing import Dict, Any, List, Optional
from src.templates.enterprise_report_template import EnterpriseReportTemplate
from src.tools.professional_data_processor import ProfessionalDataProcessor

class EnterpriseDocxGenerator:
    """企业级DOCX生成器"""
    
    def __init__(self, output_dir: str = "reports"):
        self.output_dir = output_dir
        self.template = EnterpriseReportTemplate()
        self.data_processor = ProfessionalDataProcessor()
        os.makedirs(output_dir, exist_ok=True)
        
        # 专业报告样式配置
        self.report_styles = {
            "font_family": "Times New Roman",
            "font_size_body": 11,
            "font_size_heading1": 16,
            "font_size_heading2": 14,
            "font_size_heading3": 12,
            "line_spacing": 1.15,
            "margin_top": 2.54,
            "margin_bottom": 2.54,
            "margin_left": 3.17,
            "margin_right": 3.17
        }
    
    def create_company_report(self, data: Dict[str, Any]) -> str:
        """生成企业级公司研报"""
        try:
            # 1. 数据预处理和标准化
            processed_data = self._preprocess_company_data(data)
            
            # 2. 生成Markdown内容
            markdown_content = self._generate_enterprise_markdown(processed_data, "company")
            
            # 3. 转换为DOCX
            return self._convert_to_docx(markdown_content, processed_data, "company")
            
        except Exception as e:
            print(f"❌ 企业级公司研报生成失败: {e}")
            raise
    
    def create_industry_report(self, data: Dict[str, Any]) -> str:
        """生成企业级行业研报"""
        try:
            processed_data = self._preprocess_industry_data(data)
            markdown_content = self._generate_enterprise_markdown(processed_data, "industry")
            return self._convert_to_docx(markdown_content, processed_data, "industry")
        except Exception as e:
            print(f"❌ 企业级行业研报生成失败: {e}")
            raise
    
    def create_macro_report(self, data: Dict[str, Any]) -> str:
        """生成企业级宏观研报"""
        try:
            processed_data = self._preprocess_macro_data(data)
            markdown_content = self._generate_enterprise_markdown(processed_data, "macro")
            return self._convert_to_docx(markdown_content, processed_data, "macro")
        except Exception as e:
            print(f"❌ 企业级宏观研报生成失败: {e}")
            raise
    
    def _preprocess_company_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """预处理公司数据"""
        processed = data.copy()
        
        # 提取和标准化财务数据
        financial_data = {}
        for section_name, content in data.items():
            if isinstance(content, str):
                extracted = self.data_processor.extract_financial_data(content)
                if extracted:
                    financial_data.update(extracted)
        
        processed['financial_data_structured'] = financial_data
        
        # 生成专业表格
        company_name = data.get('company_name', '目标公司')
        if financial_data:
            processed['financial_summary_table'] = self.data_processor.create_financial_summary_table(
                financial_data, company_name
            )
            
            # 生成投资评级
            industry = self._extract_industry(data)
            processed['investment_rating_data'] = self.data_processor.generate_investment_rating(
                financial_data, industry
            )
        
        return processed
    
    def _preprocess_industry_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """预处理行业数据"""
        processed = data.copy()
        
        # 行业特定的数据处理
        industry_name = data.get('industry_name', '目标行业')
        processed['industry_metrics'] = self._extract_industry_metrics(data)
        
        return processed
    
    def _preprocess_macro_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """预处理宏观数据"""
        processed = data.copy()
        
        # 宏观经济数据处理
        topic = data.get('topic', '宏观分析')
        processed['macro_indicators'] = self._extract_macro_indicators(data)
        
        return processed
    
    def _generate_enterprise_markdown(self, data: Dict[str, Any], report_type: str) -> str:
        """生成企业级Markdown内容"""
        
        # 获取报告结构模板
        if report_type == "company":
            structure = self.template.get_company_report_structure()
            subject_name = data.get('company_name', '目标公司')
            report_title = "Equity Research Report"
        elif report_type == "industry":
            structure = self.template.get_industry_report_structure()
            subject_name = data.get('industry_name', '目标行业')
            report_title = "Industry Research Report"
        else:  # macro
            structure = self.template.get_macro_report_structure()
            subject_name = data.get('topic', '宏观分析')
            report_title = "Macroeconomic Strategy Report"
        
        # 生成报告头部
        markdown = self._generate_report_header(subject_name, report_title, structure['metadata'])
        
        # 生成各个章节
        for section in structure['sections']:
            section_content = self._generate_section_content(section, data, report_type)
            markdown += section_content
        
        # 添加附录和免责声明
        markdown += self._generate_report_footer(structure['metadata'])
        
        return markdown
    
    def _generate_report_header(self, subject_name: str, report_title: str, metadata: Dict) -> str:
        """生成报告头部"""
        current_date = datetime.now().strftime('%B %d, %Y')
        
        header = f"""# {subject_name} - {report_title}

---

## Report Information

| **Item** | **Details** |
|----------|-------------|
| **Subject** | {subject_name} |
| **Report Type** | {metadata['report_type']} |
| **Date** | {current_date} |
| **Analyst** | AI Research Team |
| **Classification** | {metadata['classification']} |
| **Compliance** | {metadata['compliance_standard']} |

---

{metadata['disclaimer']}

---

"""
        return header
    
    def _generate_section_content(self, section: Dict, data: Dict[str, Any], report_type: str) -> str:
        """生成章节内容"""
        section_id = section['id']
        section_title = section['title']
        section_subtitle = section.get('subtitle', '')
        
        # 获取章节数据
        section_data = data.get(section_id, data.get(section_title, ''))
        
        # 清理内容（移除JSON代码块等）
        clean_content = self._clean_section_content(section_data)
        
        # 生成章节Markdown
        markdown = f"""## {section_title}

*{section_subtitle}*

{clean_content}

"""
        
        # 添加专业表格
        if section_id == "financial_analysis" and 'financial_summary_table' in data:
            markdown += f"\n{data['financial_summary_table']}\n\n"
        
        # 添加投资评级表格
        if section_id == "investment_recommendation" and 'investment_rating_data' in data:
            rating_data = data['investment_rating_data']
            markdown += f"""
### Investment Rating Summary

| **Metric** | **Value** |
|------------|-----------|
| **Investment Rating** | {rating_data['rating']} |
| **Recommendation** | {rating_data['recommendation']} |
| **Confidence Level** | {rating_data['confidence']} |
| **Score** | {rating_data['score']}/{rating_data['max_score']} |

"""
        
        # 添加图表建议
        chart_suggestions = self._generate_chart_suggestions(section_id, data)
        if chart_suggestions:
            markdown += f"\n{chart_suggestions}\n\n"
        
        return markdown
    
    def _clean_section_content(self, content: str) -> str:
        """清理章节内容"""
        if not isinstance(content, str):
            return str(content)
        
        # 移除JSON代码块
        content = re.sub(r'```json\s*\n.*?\n```', '', content, flags=re.DOTALL)
        
        # 移除多余空行
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        
        # 清理开头结尾空白
        content = content.strip()
        
        # 如果内容为空或太短，提供默认内容
        if not content or len(content) < 50:
            content = "This section is under development. Detailed analysis will be provided in future updates."
        
        return content
    
    def _generate_chart_suggestions(self, section_id: str, data: Dict[str, Any]) -> str:
        """生成图表建议"""
        chart_specs = self.template.get_chart_specifications()
        
        suggestions = []
        
        if section_id == "financial_analysis":
            suggestions.append("> 📊 **Recommended Charts**: Revenue Trend, Profitability Margins, Cash Flow Analysis")
            suggestions.append("> 💡 **Data Source**: Company financial statements, quarterly reports")
        elif section_id == "valuation_analysis":
            suggestions.append("> 📈 **Recommended Charts**: P/E vs Industry, DCF Sensitivity Analysis, Peer Valuation Comparison")
            suggestions.append("> 💡 **Data Source**: Market data, comparable company analysis")
        elif section_id == "investment_thesis":
            suggestions.append("> 🎯 **Recommended Charts**: Investment Thesis Framework, Competitive Positioning Matrix")
            suggestions.append("> 💡 **Data Source**: Strategic analysis, market research")
        
        return '\n'.join(suggestions) if suggestions else ''
    
    def _convert_to_docx(self, markdown_content: str, data: Dict[str, Any], report_type: str) -> str:
        """转换Markdown为DOCX"""
        try:
            import pypandoc
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            subject_name = self._get_subject_name(data, report_type)
            safe_name = re.sub(r'[^\w\s-]', '', subject_name).strip()
            safe_name = re.sub(r'[-\s]+', '_', safe_name)
            
            filename = f"Enterprise_{report_type.title()}_Report_{safe_name}_{timestamp}"
            
            # 保存Markdown文件
            md_filepath = os.path.join(self.output_dir, f"{filename}.md")
            with open(md_filepath, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            # 转换为DOCX
            docx_filepath = os.path.join(self.output_dir, f"{filename}.docx")
            
            # 使用pypandoc转换，应用专业样式
            extra_args = [
                '--reference-doc=template.docx',  # 如果有模板文件
                '--toc',  # 生成目录
                '--toc-depth=2'  # 目录深度
            ]
            
            pypandoc.convert_file(
                md_filepath,
                'docx',
                outputfile=docx_filepath,
                extra_args=extra_args
            )
            
            # 删除临时Markdown文件
            os.remove(md_filepath)
            
            print(f"✅ 企业级{report_type}研报生成完成: {docx_filepath}")
            return docx_filepath
            
        except ImportError:
            print("⚠️ pypandoc未安装，使用备用方法")
            return self._fallback_docx_generation(markdown_content, data, report_type)
        except Exception as e:
            print(f"⚠️ DOCX转换失败: {e}，使用备用方法")
            return self._fallback_docx_generation(markdown_content, data, report_type)
    
    def _fallback_docx_generation(self, markdown_content: str, data: Dict[str, Any], report_type: str) -> str:
        """备用DOCX生成方法"""
        # 简单保存为文本文件作为备用
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        subject_name = self._get_subject_name(data, report_type)
        safe_name = re.sub(r'[^\w\s-]', '', subject_name).strip()
        safe_name = re.sub(r'[-\s]+', '_', safe_name)
        
        filename = f"Enterprise_{report_type.title()}_Report_{safe_name}_{timestamp}.txt"
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        print(f"✅ 备用格式报告生成完成: {filepath}")
        return filepath
    
    def _get_subject_name(self, data: Dict[str, Any], report_type: str) -> str:
        """获取报告主题名称"""
        if report_type == "company":
            return data.get('company_name', 'Company')
        elif report_type == "industry":
            return data.get('industry_name', 'Industry')
        else:
            return data.get('topic', 'Macro')
    
    def _extract_industry(self, data: Dict[str, Any]) -> str:
        """提取行业信息"""
        # 从数据中提取行业信息
        industry_keywords = {
            'technology': ['科技', '技术', 'AI', '人工智能', '软件', '互联网'],
            'healthcare': ['医疗', '健康', '医药', '生物', '制药'],
            'financial': ['金融', '银行', '保险', '证券', '投资'],
            'consumer': ['消费', '零售', '电商', '品牌'],
            'industrial': ['工业', '制造', '机械', '设备']
        }
        
        content = str(data)
        for industry, keywords in industry_keywords.items():
            if any(keyword in content for keyword in keywords):
                return industry
        
        return 'technology'  # 默认
    
    def _extract_industry_metrics(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """提取行业指标"""
        # 这里可以添加行业特定的指标提取逻辑
        return {}
    
    def _extract_macro_indicators(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """提取宏观指标"""
        # 这里可以添加宏观经济指标提取逻辑
        return {}
    
    def _generate_report_footer(self, metadata: Dict) -> str:
        """生成报告尾部"""
        footer = f"""
---

## Important Disclosures

{metadata['disclaimer']}

---

**Report Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

**Compliance**: This report complies with {metadata['compliance_standard']} and applicable regulatory requirements.

---
"""
        return footer
