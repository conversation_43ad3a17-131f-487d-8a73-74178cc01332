#!/usr/bin/env python3
"""
批量修复脚本：去掉所有的asyncio垃圾库
"""

import os
import re
import glob

def fix_file(file_path):
    """修复单个文件"""
    print(f"🔧 修复文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 1. 删除asyncio相关的import
    content = re.sub(r'import asyncio\n?', '', content)
    content = re.sub(r'from asyncio import.*?\n', '', content)
    content = re.sub(r', AsyncGenerator', '', content)
    content = re.sub(r'AsyncGenerator, ', '', content)
    content = re.sub(r'AsyncGenerator', 'Generator', content)
    content = re.sub(r'from openai import OpenAI, AsyncOpenAI', 'from openai import OpenAI', content)
    
    # 2. 修复async def -> def
    content = re.sub(r'async def ', 'def ', content)
    
    # 3. 修复await调用
    content = re.sub(r'await ', '', content)
    
    # 4. 修复async for -> for
    content = re.sub(r'async for ', 'for ', content)
    
    # 5. 修复asyncio.sleep -> time.sleep
    content = re.sub(r'asyncio\.sleep\(', 'time.sleep(', content)
    
    # 6. 修复AsyncOpenAI相关
    content = re.sub(r'AsyncOpenAI\(', 'OpenAI(', content)
    content = re.sub(r'self\.async_client', 'self.client', content)
    
    # 7. 修复async_chat_completion -> chat_completion
    content = re.sub(r'async_chat_completion', 'chat_completion', content)
    
    # 8. 修复async_chat_completion_with_tools_stream -> chat_completion_with_tools_stream
    content = re.sub(r'async_chat_completion_with_tools_stream', 'chat_completion_with_tools_stream', content)
    
    # 9. 删除asyncio.iscoroutinefunction检查
    content = re.sub(r'if asyncio\.iscoroutinefunction\([^)]+\):\s*\n\s*[^=]+ = await [^(]+\([^)]*\)\s*\n\s*else:\s*\n\s*', '', content)
    content = re.sub(r'if asyncio\.iscoroutinefunction\([^)]+\):\s*[^=]+ = await [^(]+\([^)]*\)\s*else:\s*', '', content)
    
    # 10. 修复工具函数调用
    content = re.sub(r'tool_result = await tool_functions\[function_name\]\(\*\*args\)', 'tool_result = tool_functions[function_name](**args)', content)
    
    # 11. 确保有time import（如果使用了time.sleep）
    if 'time.sleep(' in content and 'import time' not in content:
        # 在其他import后添加time import
        import_match = re.search(r'(import [^\n]+\n)', content)
        if import_match:
            content = content.replace(import_match.group(1), import_match.group(1) + 'import time\n')
    
    # 检查是否有修改
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 修复完成: {file_path}")
        return True
    else:
        print(f"⏭️  无需修改: {file_path}")
        return False

def main():
    """主函数"""
    print("🚀 开始批量修复asyncio问题...")
    
    # 要修复的文件模式
    patterns = [
        'src/agents/*.py',
        'src/models/*.py',
        'src/tools/*.py'
    ]
    
    fixed_count = 0
    total_count = 0
    
    for pattern in patterns:
        files = glob.glob(pattern)
        for file_path in files:
            if os.path.isfile(file_path):
                total_count += 1
                if fix_file(file_path):
                    fixed_count += 1
    
    print(f"\n📊 修复完成统计:")
    print(f"总文件数: {total_count}")
    print(f"修复文件数: {fixed_count}")
    print(f"无需修改: {total_count - fixed_count}")
    
    print("\n🎉 批量修复完成！asyncio垃圾库已清理干净！")

if __name__ == "__main__":
    main()
