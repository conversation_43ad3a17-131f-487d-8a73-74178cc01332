# src/agents/base_agent.py
import time
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from concurrent.futures import ThreadPoolExecutor
from config.settings import Config
from src.utils.logger import get_logger
from src.utils.cache_manager import CacheManager

class BaseAgent(ABC):
    """
    Agent基类 - 定义所有Agent的通用接口和基础方法
    """
    
    def __init__(self, config: Config = None, agent_name: str = "BaseAgent"):
        self.config = config or Config()
        self.agent_name = agent_name
        self.logger = get_logger(f"{agent_name}")
        self.cache_manager = CacheManager(self.config)
        self.executor = ThreadPoolExecutor(max_workers=self.config.MAX_CONCURRENT_TASKS)
        self.start_time = None
        self.end_time = None
        
        # 初始化目录
        self.config.create_directories()
        
        self.logger.info(f"{self.agent_name} 初始化完成")
    
    @abstractmethod
    def run(self, *args, **kwargs) -> Any:
        """
        执行Agent的主要任务 - 子类必须实现
        """
        raise NotImplementedError("子类必须实现run方法")
    
    def start_task(self, task_name: str = None):
        """开始执行任务"""
        self.start_time = time.time()
        task_name = task_name or "默认任务"
        self.logger.info(f"{self.agent_name} 开始执行任务: {task_name}")
    
    def end_task(self, task_name: str = None):
        """结束任务并记录耗时"""
        self.end_time = time.time()
        task_name = task_name or "默认任务"
        
        if self.start_time:
            duration = self.end_time - self.start_time
            self.logger.info(f"{self.agent_name} 完成任务: {task_name}, 耗时: {duration:.2f}秒")
        else:
            self.logger.info(f"{self.agent_name} 完成任务: {task_name}")
    
    def execute_with_retry(self, func, *args, max_retries: int = None, **kwargs):
        """
        带重试机制的函数执行
        """
        max_retries = max_retries or self.config.MAX_RETRY_ATTEMPTS
        last_exception = None
        
        for attempt in range(max_retries):
            try:
                # 全部同步调用
                result = func(*args, **kwargs)

                self.logger.debug(f"{self.agent_name} 执行成功，尝试次数: {attempt + 1}")
                return result
                
            except Exception as e:
                last_exception = e
                self.logger.warning(f"{self.agent_name} 执行失败，尝试次数: {attempt + 1}, 错误: {str(e)}")
                
                if attempt < max_retries - 1:
                    time.sleep(self.config.RETRY_DELAY_SECONDS * (attempt + 1))
                else:
                    self.logger.error(f"{self.agent_name} 达到最大重试次数，最终失败")
        
        raise last_exception
    
    def validate_input(self, input_data: Any, required_fields: List[str] = None) -> bool:
        """
        验证输入数据
        """
        if not input_data:
            self.logger.error(f"{self.agent_name} 输入数据为空")
            return False
        
        if required_fields and isinstance(input_data, dict):
            missing_fields = [field for field in required_fields if field not in input_data]
            if missing_fields:
                self.logger.error(f"{self.agent_name} 缺少必要字段: {missing_fields}")
                return False
        
        return True
    
    def format_output(self, data: Any, format_type: str = "dict") -> Any:
        """
        格式化输出数据
        """
        try:
            if format_type == "dict" and not isinstance(data, dict):
                return {"result": data, "agent": self.agent_name, "timestamp": time.time()}
            elif format_type == "json":
                import json
                return json.dumps(data, ensure_ascii=False, indent=2)
            else:
                return data
        except Exception as e:
            self.logger.error(f"{self.agent_name} 输出格式化失败: {str(e)}")
            return data
    
    def parallel_execute(self, tasks: List[tuple], timeout: int = None) -> List[Any]:
        """
        并行执行多个任务
        tasks: [(func, args, kwargs), ...]
        """
        timeout = timeout or self.config.TIMEOUT_SECONDS
        
        def execute_single_task(func, args, kwargs):
            try:
                # 全部同步调用
                return func(*args, **kwargs)
            except Exception as e:
                self.logger.error(f"{self.agent_name} 并行任务执行失败: {str(e)}")
                return None
        
        # 创建任务列表，使用copy避免并发修改问题
        task_coroutines = [
            execute_single_task(func, args or (), (kwargs or {}).copy())
            for func, args, kwargs in tasks
        ]
        
        try:
            # 顺序执行所有任务
            results = []
            for func, args, kwargs in tasks:
                result = execute_single_task(func, args, kwargs)
                results.append(result)
            return results
        except Exception as e:
            self.logger.warning(f"{self.agent_name} 任务执行失败: {str(e)}")
            return [None] * len(tasks)
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        获取性能指标
        """
        metrics = {
            "agent_name": self.agent_name,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "duration": self.end_time - self.start_time if self.start_time and self.end_time else None,
            "cache_stats": self.cache_manager.get_stats() if hasattr(self.cache_manager, 'get_stats') else None
        }
        return metrics
    
    def cleanup(self):
        """
        清理资源
        """
        try:
            if hasattr(self, 'executor') and self.executor:
                self.executor.shutdown(wait=True)
            
            if hasattr(self, 'cache_manager') and self.cache_manager:
                self.cache_manager.cleanup()
            
            self.logger.info(f"{self.agent_name} 资源清理完成")
        except Exception as e:
            self.logger.error(f"{self.agent_name} 资源清理失败: {str(e)}")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.cleanup()
    
    def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        self.cleanup()
