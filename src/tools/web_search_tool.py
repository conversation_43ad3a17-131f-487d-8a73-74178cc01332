import os
import json
import requests
import hashlib
import time
from typing import List, Dict, Any, Optional
from config.settings import Config

try:
    from exa_py import Exa
    EXA_AVAILABLE = True
except ImportError:
    EXA_AVAILABLE = False
    print("⚠️  exa_py未安装，将使用智博API。安装命令: pip install exa_py")

class WebSearchTool:
    """智能Web搜索工具，支持Exa AI和智博API，优先使用Exa AI"""

    def __init__(self, config: Config = None, rag_system=None):
        self.config = config or Config()
        self.api_url = self.config.SEARCH_API_URL
        self.api_key = self.config.SEARCH_API_KEY
        self.cache_dir = self.config.SEARCH_CACHE_DIR
        self.search_mode = self.config.SEARCH_MODE

        # 添加RAG系统支持，用于智能缓存
        self.rag_system = rag_system

        # 设置Exa AI API密钥
        self.exa_api_key = "715b14b0-a64c-48ca-a799-89983090bf00"

        # 检查是否使用Exa AI
        self.use_exa = EXA_AVAILABLE and self.exa_api_key

        # Exa AI速率限制控制 - 优化为更快的间隔
        self.last_exa_request_time = 0
        self.exa_min_interval = 0.1  # 减少到100ms间隔，提高速度

        if self.use_exa:
            print("🚀 使用Exa AI搜索引擎（高质量）+ 智能知识库缓存")
        else:
            print("🔍 使用智博API搜索引擎 + 智能知识库缓存")

        os.makedirs(self.cache_dir, exist_ok=True)
    
    def _get_cache_path(self, query: str) -> str:
        """生成查询缓存路径"""
        query_hash = hashlib.md5(query.encode()).hexdigest()
        return os.path.join(self.cache_dir, f"{query_hash}.json")
    
    def _is_cached(self, query: str) -> bool:
        """检查查询是否已缓存"""
        cache_path = self._get_cache_path(query)
        return os.path.exists(cache_path)
    
    def _get_cached_results(self, query: str) -> Optional[List[Dict]]:
        """获取缓存的搜索结果"""
        if not self._is_cached(query):
            return None
        
        cache_path = self._get_cache_path(query)
        try:
            with open(cache_path, "r", encoding="utf-8") as f:
                cached_data = json.load(f)
                cache_time = cached_data.get("timestamp", 0)
                current_time = time.time()
                
                if current_time - cache_time > self.config.CACHE_EXPIRY_HOURS * 3600:
                    return None
                
                return cached_data.get("results", [])
        except (json.JSONDecodeError, IOError):
            return None
    
    def _cache_results(self, query: str, results: List[Dict]):
        """缓存搜索结果"""
        cache_path = self._get_cache_path(query)
        cache_data = {
            "timestamp": time.time(),
            "query": query,
            "results": results
        }
        
        with open(cache_path, "w", encoding="utf-8") as f:
            json.dump(cache_data, f, ensure_ascii=False, indent=2)

    def _search_exa(self, query: str, num_results: int = 10) -> List[Dict]:
        """使用Exa AI搜索，带速率限制"""
        if not self.use_exa:
            return []

        try:
            # 速率限制控制
            current_time = time.time()
            time_since_last_request = current_time - self.last_exa_request_time

            if time_since_last_request < self.exa_min_interval:
                sleep_time = self.exa_min_interval - time_since_last_request
                print(f"⏱️  Exa AI速率限制，等待 {sleep_time:.2f} 秒...")
                time.sleep(sleep_time)

            # 更新最后请求时间
            self.last_exa_request_time = time.time()

            # 使用Exa AI进行搜索
            response = self.exa_client.search(
                query=query,
                num_results=min(num_results, 10),
                include_domains=None,
                exclude_domains=None,
                start_crawl_date=None,
                end_crawl_date=None,
                start_published_date=None,
                end_published_date=None,
                use_autoprompt=True,  # 让Exa优化查询
                type="neural"  # 使用神经搜索
            )

            results = []
            for result in response.results:
                formatted_result = {
                    "title": result.title or "",
                    "url": result.url or "",
                    "snippet": result.text[:500] if result.text else "",  # 限制摘要长度
                    "displayUrl": result.url or "",
                    "siteName": "",
                    "datePublished": result.published_date or ""
                }
                results.append(formatted_result)

            return results

        except Exception as e:
            print(f"🚀 Exa AI搜索失败: {e}")
            return []

    def _search_api(self, query: str, num_results: int = 10) -> List[Dict]:
        """调用搜索API"""
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        payload = {
            "query": query,
            "summary": True,
            "count": num_results
        }

        try:
            # 添加延迟避免API限制 - 优化延迟时间
            time.sleep(0.3)  # 减少到300ms延迟，提高速度
            response = requests.post(self.api_url, headers=headers, json=payload, timeout=30)
            response.raise_for_status()

            # 解析响应
            result_data = response.json()

            # 根据博查AI的API响应格式提取结果
            if result_data.get("code") == 200 and "data" in result_data:
                data = result_data["data"]
                if "webPages" in data and "value" in data["webPages"]:
                    results = data["webPages"]["value"]

                    # 转换为标准格式
                    formatted_results = []
                    for item in results:
                        formatted_result = {
                            "title": item.get("name", ""),
                            "url": item.get("url", ""),
                            "snippet": item.get("snippet", "") or item.get("summary", ""),
                            "displayUrl": item.get("displayUrl", ""),
                            "siteName": item.get("siteName", ""),
                            "datePublished": item.get("datePublished", "")
                        }
                        formatted_results.append(formatted_result)

                    return formatted_results
                else:
                    print(f"API响应中没有找到webPages.value: {data}")
                    return []
            else:
                print(f"API响应错误，code: {result_data.get('code')}, msg: {result_data.get('msg')}")
                return []

        except requests.RequestException as e:
            print(f"搜索API调用失败: {e}")
            return []
        except json.JSONDecodeError as e:
            print(f"搜索API响应解析失败: {e}")
            return []
    
    def _perform_search(self, query: str, num_results: int = 10) -> List[Dict]:
        """执行实际搜索，优先使用Exa AI"""
        if self.use_exa:
            # 尝试Exa AI搜索
            results = self._search_exa(query, num_results)
            if results:
                return results
            else:
                print("🔄 Exa AI搜索失败，回退到智博API...")
                return self._search_api(query, num_results)
        else:
            return self._search_api(query, num_results)

    def search(self, query: str, num_results: int = 10) -> List[Dict]:
        """执行Web搜索，支持多种缓存策略 - 优化版：先检查知识库"""

        # 1. 首先检查知识库是否有相关信息（智能缓存）
        kb_results = self._check_knowledge_base(query)
        if kb_results and len(kb_results) >= 3:  # 如果知识库有足够信息
            print(f"📚 知识库中已有 '{query}' 相关信息({len(kb_results)}条)，跳过网络搜索")
            return kb_results[:num_results]

        # 2. 如果知识库信息不足，继续原有的搜索逻辑
        if self.search_mode == "cache_only":
            return self._get_cached_results(query) or []

        elif self.search_mode == "cache_first":
            cached_results = self._get_cached_results(query)
            if cached_results:
                return cached_results

            results = self._perform_search(query, num_results)
            if results:
                self._cache_results(query, results)
                # 同时添加到知识库
                self._add_results_to_knowledge_base(query, results)
            return results

        elif self.search_mode == "search_only":
            results = self._perform_search(query, num_results)
            if results:
                self._add_results_to_knowledge_base(query, results)
            return results

        elif self.search_mode == "cache_and_search":
            cached_results = self._get_cached_results(query)
            fresh_results = self._perform_search(query, num_results)

            # 确保cached_results是列表
            if not isinstance(cached_results, list):
                cached_results = []

            # 确保fresh_results是列表
            if not isinstance(fresh_results, list):
                fresh_results = []

            if fresh_results:
                self._cache_results(query, fresh_results)
                self._add_results_to_knowledge_base(query, fresh_results)

            # 合并结果，去重
            seen_urls = set()
            combined_results = []

            for result in fresh_results + cached_results:
                url = result.get("url", "")
                if url and url not in seen_urls:
                    seen_urls.add(url)
                    combined_results.append(result)

            return combined_results[:num_results]

        results = self._perform_search(query, num_results)
        if results:
            self._add_results_to_knowledge_base(query, results)
        return results

    def _check_knowledge_base(self, query: str) -> List[Dict]:
        """检查知识库中是否已有相关信息"""
        try:
            # 如果没有RAG系统，返回空
            if not hasattr(self, 'rag_system') or not self.rag_system:
                return []

            # 搜索知识库
            kb_results = self.rag_system.search(query, top_k=5)
            if not kb_results:
                return []

            # 转换为搜索结果格式
            formatted_results = []
            for i, result in enumerate(kb_results):
                if result.get('similarity', 0) > 0.7:  # 相似度阈值
                    content = result.get('content', '')
                    # 处理乱码
                    content = self._fix_garbled_text(content)

                    formatted_results.append({
                        'title': f"知识库结果 {i+1}: {query}",
                        'url': 'knowledge_base',
                        'snippet': content[:200] + '...' if len(content) > 200 else content,
                        'content': content,
                        'source': 'knowledge_base'
                    })

            return formatted_results

        except Exception as e:
            print(f"⚠️ 检查知识库失败: {e}")
            return []

    def _add_results_to_knowledge_base(self, query: str, results: List[Dict]):
        """将搜索结果添加到知识库"""
        try:
            if not hasattr(self, 'rag_system') or not self.rag_system:
                return

            for result in results:
                title = result.get('title', '')
                snippet = result.get('snippet', '')
                url = result.get('url', '')

                # 处理乱码
                title = self._fix_garbled_text(title)
                snippet = self._fix_garbled_text(snippet)

                if title and snippet:
                    content = f"{title}\n\n{snippet}"
                    metadata = {
                        'source': 'web_search',
                        'query': query,
                        'url': url,
                        'timestamp': time.time()
                    }

                    self.rag_system.add_document(content, metadata)

        except Exception as e:
            print(f"⚠️ 添加到知识库失败: {e}")

    def _fix_garbled_text(self, text: str) -> str:
        """修复乱码文本"""
        if not text or not isinstance(text, str):
            return text

        try:
            # 检查是否包含乱码字符
            garbled_indicators = ['Ã', 'Â', 'Ä', 'Ç', 'É', 'Ì', 'Î', 'Ï', 'Ñ', 'Ò', 'Ó', 'Ô', 'Õ', 'Ö', 'Ù', 'Ú', 'Û', 'Ü', 'Ý']

            # 如果包含大量乱码字符，尝试修复
            garbled_count = sum(1 for char in garbled_indicators if char in text)
            if garbled_count > len(text) * 0.1:  # 如果乱码字符超过10%
                print(f"🔧 检测到乱码内容，尝试修复: {text[:50]}...")

                # 尝试不同的编码修复
                try:
                    # 尝试从latin-1解码再用utf-8编码
                    fixed_text = text.encode('latin-1').decode('utf-8', errors='ignore')
                    if len(fixed_text) > len(text) * 0.5:  # 如果修复后长度合理
                        return fixed_text
                except:
                    pass

                try:
                    # 尝试从cp1252解码再用utf-8编码
                    fixed_text = text.encode('cp1252').decode('utf-8', errors='ignore')
                    if len(fixed_text) > len(text) * 0.5:
                        return fixed_text
                except:
                    pass

                # 如果无法修复，返回清理后的文本
                cleaned_text = ''.join(char for char in text if ord(char) < 128 or char.isalnum())
                if len(cleaned_text) > 10:
                    return cleaned_text
                else:
                    return "[内容包含乱码，无法正确显示]"

            return text

        except Exception as e:
            print(f"⚠️ 修复乱码文本失败: {e}")
            return text
