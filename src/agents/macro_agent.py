# src/agents/macro_agent.py
from typing import Dict, Any, List
from tqdm import tqdm
import time
import asyncio
import asyncio
from src.agents.base_agent import BaseAgent
from src.tools.web_search_tool import WebSearchTool
from src.tools.url_content_tool import URLContentTool
from src.tools.rag_system import RAGSystem
from src.tools.enterprise_docx_generator import EnterpriseDocxGenerator
from src.tools.professional_docx_generator import ProfessionalFinancialReportGenerator
from src.models.llm_client import LLMClient
from src.utils.data_processor import DataProcessor
from config.settings import Config

class MacroAgent(BaseAgent):
    """
    宏观研报生成Agent - 专门负责生成宏观经济和策略研究报告
    """
    
    def __init__(self, config: Config = None):
        super().__init__(config, "MacroAgent")
        
        # 初始化工具
        self.llm_client = LLMClient(self.config)
        self.web_search_tool = WebSearchTool(self.config)
        self.url_content_tool = URLContentTool(self.config)
        self.rag_system = RAGSystem(self.config)
        self.docx_generator = EnterpriseDocxGenerator(self.config)
        self.professional_docx_generator = ProfessionalFinancialReportGenerator(self.config)
        self.data_processor = DataProcessor(self.config)
        
        # 宏观分析相关配置 - 扩展搜索查询以获取更丰富的信息
        self.macro_search_queries = [
            # 宏观经济基础数据
            "{topic} 宏观经济 GDP 经济增长 2023",
            "{topic} 通胀率 CPI PPI 物价水平",
            "{topic} 就业数据 失业率 就业市场",
            "{topic} 贸易数据 进出口 贸易平衡",

            # 货币政策分析
            "{topic} 货币政策 央行政策 利率水平",
            "{topic} 流动性 货币供应量 M1 M2",
            "{topic} 汇率政策 人民币汇率 外汇储备",
            "{topic} 金融市场 债券市场 股票市场",

            # 财政政策分析
            "{topic} 财政政策 政府支出 税收政策",
            "{topic} 财政赤字 政府债务 地方债务",
            "{topic} 基建投资 公共投资 财政刺激",
            "{topic} 减税降费 税制改革 财税政策",

            # 产业政策分析
            "{topic} 产业政策 结构调整 转型升级",
            "{topic} 新基建 数字经济 科技创新",
            "{topic} 双碳政策 绿色发展 能源转型",
            "{topic} 区域发展 城市化 乡村振兴",

            # 市场趋势分析
            "{topic} 市场趋势 投资环境 资本市场",
            "{topic} 资产价格 房地产 大宗商品",
            "{topic} 股市走势 债市表现 汇市波动",
            "{topic} 风险偏好 市场情绪 投资者行为",

            # 国际环境分析
            "{topic} 国际环境 全球经济 地缘政治",
            "{topic} 中美关系 贸易摩擦 国际合作",
            "{topic} 全球供应链 国际贸易 跨境投资",
            "{topic} 国际金融 美联储政策 全球流动性",

            # 风险评估
            "{topic} 经济风险 金融风险 系统性风险",
            "{topic} 政策风险 监管风险 合规风险",
            "{topic} 市场风险 流动性风险 信用风险",
            "{topic} 外部风险 输入性风险 传染风险",

            # 投资策略
            "{topic} 资产配置 投资策略 组合管理",
            "{topic} 大类资产 股债配置 另类投资",
            "{topic} 行业配置 主题投资 价值投资",
            "{topic} 风险管理 对冲策略 避险资产"
        ]
        
        # 宏观经济指标
        self.macro_indicators = [
            "GDP", "CPI", "PPI", "PMI", "失业率", "通胀率",
            "利率", "汇率", "外汇储备", "贸易逆差", "财政收支",
            "货币供应量", "社会融资规模", "固定资产投资"
        ]
        
        # 初始化工具函数映射
        self.tool_functions = {
            "web_search": self._tool_web_search,
            "get_url_content": self._tool_get_url_content,
            "search_knowledge_base": self._tool_search_knowledge_base,
            "add_to_knowledge_base": self._tool_add_to_knowledge_base,
            "analyze_economic_data": self._tool_analyze_economic_data
        }

        # 工具定义
        self.tools = [
            {
                "type": "function",
                "function": {
                    "name": "web_search",
                    "description": "搜索网络信息，获取宏观经济相关资料",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "搜索查询词"},
                            "num_results": {"type": "integer", "description": "返回结果数量，默认5", "default": 5}
                        },
                        "required": ["query"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "get_url_content",
                    "description": "获取指定URL的详细内容",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "url": {"type": "string", "description": "要获取内容的URL"}
                        },
                        "required": ["url"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "search_knowledge_base",
                    "description": "从知识库中搜索相关信息",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "搜索查询词"}
                        },
                        "required": ["query"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "analyze_economic_data",
                    "description": "分析宏观经济数据并生成洞察",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "data": {"type": "string", "description": "经济数据内容"},
                            "analysis_type": {"type": "string", "description": "分析类型", "enum": ["gdp", "inflation", "monetary", "fiscal", "comprehensive"]}
                        },
                        "required": ["data"]
                    }
                }
            }
        ]

        self.logger.info("MacroAgent 初始化完成")
    
    async def run(self, topic: str, time_range: str = "2023-2026", region: str = "全球") -> str:
        """
        生成宏观策略研究报告
        
        Args:
            topic: 研究主题
            time_range: 时间范围
            region: 地域范围
            
        Returns:
            生成的报告文件路径
        """
        self.start_task(f"生成宏观研报 - {topic}")

        # 创建进度条
        total_steps = 5
        progress_bar = tqdm(total=total_steps, desc=f"🌍 生成{topic}宏观研报",
                           bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}]')

        try:
            # 验证输入
            if not self.validate_input(topic):
                raise ValueError("研究主题不能为空")

            # 第一步：宏观信息收集
            progress_bar.set_description(f"🔍 收集{topic}宏观信息")
            self.logger.info(f"🔍 开始收集 {topic} 宏观信息")
            search_results = await self._collect_macro_information(topic, time_range, region)
            progress_bar.update(1)

            # 第二步：宏观数据处理
            progress_bar.set_description(f"📄 处理{topic}宏观数据")
            self.logger.info(f"📄 处理 {topic} 宏观数据")
            processed_data = await self._process_macro_content(search_results, topic)
            progress_bar.update(1)

            # 第三步：构建宏观知识库
            progress_bar.set_description(f"🧠 构建{topic}知识库")
            self.logger.info(f"🧠 构建 {topic} 宏观知识库")
            await self._build_macro_knowledge_base(processed_data, topic)
            progress_bar.update(1)

            # 第四步：宏观深度分析
            progress_bar.set_description(f"🔬 深度分析{topic}宏观趋势")
            self.logger.info(f"🔬 深度分析 {topic} 宏观趋势")
            analysis_result = await self._analyze_macro_data_with_stream(topic, time_range, region)
            progress_bar.update(1)

            # 第五步：生成宏观报告
            progress_bar.set_description(f"📝 生成{topic}报告")
            self.logger.info(f"📝 生成 {topic} 宏观报告")
            report_path = await self._generate_macro_report(analysis_result)
            progress_bar.update(1)

            progress_bar.set_description(f"✅ {topic}宏观研报完成")
            progress_bar.close()

            self.end_task(f"生成宏观研报 - {topic}")
            return report_path

        except Exception as e:
            progress_bar.close()
            self.logger.error(f"❌ 生成宏观研报失败: {str(e)}")
            raise e
    
    async def _collect_macro_information(self, topic: str, time_range: str, region: str) -> List[Dict]:
        """
        收集宏观相关信息
        """
        search_queries = []
        
        # 构建基础搜索查询
        for query_template in self.macro_search_queries:
            query = query_template.format(topic=topic)
            if time_range:
                query += f" {time_range}"
            if region and region != "全球":
                query += f" {region}"
            search_queries.append(query)
        
        # 添加政策相关查询
        policy_queries = [
            f"{topic} 国家政策 发展战略",
            f"{topic} 监管政策 行业规范",
            f"{topic} 财政政策 税收优惠",
            f"{topic} 货币政策 流动性影响"
        ]
        search_queries.extend(policy_queries)
        
        # 添加经济指标查询
        indicator_queries = [
            f"{topic} 经济指标 数据统计",
            f"{topic} 市场规模 增长数据",
            f"{topic} 投资规模 资金流向",
            f"{topic} 国际比较 全球排名"
        ]
        search_queries.extend(indicator_queries)
        
        # 改为串行搜索，避免速率限制问题
        all_results = []
        seen_urls = set()

        # 限制搜索查询数量，避免过多请求
        limited_queries = search_queries[:10]  # 最多10个查询

        for i, query in enumerate(limited_queries):
            try:
                self.logger.info(f"🔍 执行宏观搜索 {i+1}/{len(limited_queries)}: {query}")
                results = self.web_search_tool.search(query, num_results=4)  # 减少每次搜索的结果数

                if results and isinstance(results, list):
                    for result in results:
                        url = result.get("url", "")
                        if url and url not in seen_urls:
                            seen_urls.add(url)
                            all_results.append(result)
                    self.logger.info(f"✅ 宏观搜索 {i+1} 成功，获得 {len(results)} 条结果")
                else:
                    self.logger.warning(f"⚠️ 宏观搜索 {i+1} 无结果")

                # 添加延迟，避免速率限制
                if i < len(limited_queries) - 1:  # 最后一次不需要延迟
                    await asyncio.sleep(0.5)  # 500ms延迟

            except Exception as e:
                self.logger.error(f"❌ 宏观搜索 {i+1} 失败: {str(e)}")
                continue
        
        self.logger.info(f"收集到 {len(all_results)} 条宏观搜索结果")
        return all_results
    
    async def _process_macro_content(self, search_results: List[Dict], topic: str) -> List[Dict]:
        """
        处理宏观搜索结果
        """
        # 筛选权威来源
        authoritative_results = []
        for result in search_results:
            url = result.get("url", "")
            title = result.get("title", "")
            snippet = result.get("snippet", "")
            
            # 权威性评分
            authority_score = self._calculate_authority_score(url, title, snippet)
            
            if authority_score > 0.3:  # 权威性阈值
                authoritative_results.append({
                    "url": url,
                    "title": title,
                    "snippet": snippet,
                    "authority_score": authority_score
                })
        
        # 按权威性排序并限制数量
        authoritative_results.sort(key=lambda x: x["authority_score"], reverse=True)
        authoritative_results = authoritative_results[:30]  # 最多处理30个权威来源
        
        # 简化处理：直接使用搜索结果，放宽条件避免0结果
        processed_data = []
        for result in search_results[:20]:  # 限制处理数量
            title = result.get("title", "")
            snippet = result.get("snippet", "")
            url = result.get("url", "")

            # 放宽条件：只要有标题就可以使用，snippet可以为空
            if title and len(title) > 10:  # 标题至少10个字符
                # 如果snippet为空，使用标题作为内容
                content_text = snippet if snippet and len(snippet) > 20 else title

                processed_data.append({
                    "url": url,
                    "title": title,
                    "content": f"{title}\n\n{content_text}",
                    "topic": topic,
                    "content_type": self._classify_macro_content(title, content_text)
                })

                self.logger.debug(f"处理宏观搜索结果: {title[:50]}... (snippet长度: {len(snippet)})")
        
        self.logger.info(f"成功处理 {len(processed_data)} 条宏观内容")
        return processed_data
    
    def _calculate_authority_score(self, url: str, title: str, snippet: str) -> float:
        """
        计算来源权威性评分
        """
        score = 0.0
        
        # 政府官方网站
        gov_domains = [
            "gov.cn", "gov.com", ".gov", "ndrc.gov.cn", "mof.gov.cn", 
            "pbc.gov.cn", "stats.gov.cn", "miit.gov.cn"
        ]
        if any(domain in url for domain in gov_domains):
            score += 0.5
        
        # 权威媒体和机构
        authoritative_domains = [
            "xinhuanet.com", "people.com.cn", "cctv.com", "chinadaily.com.cn",
            "21jingji.com", "caixin.com", "ftchinese.com", "bloomberg.com",
            "reuters.com", "economist.com", "imf.org", "worldbank.org"
        ]
        if any(domain in url for domain in authoritative_domains):
            score += 0.4
        
        # 学术和研究机构
        academic_domains = [
            "edu.cn", ".edu", "cass.cn", "org.cn", "brookings.edu",
            "cfr.org", "csis.org", "chathamhouse.org"
        ]
        if any(domain in url for domain in academic_domains):
            score += 0.3
        
        # 专业财经媒体
        financial_domains = [
            "eastmoney.com", "cnstock.com", "sina.com.cn", "163.com",
            "qq.com", "sohu.com", "hexun.com", "jrj.com.cn"
        ]
        if any(domain in url for domain in financial_domains):
            score += 0.2
        
        # 标题关键词权重
        title_keywords = ["政策", "分析", "报告", "数据", "统计", "研究", "白皮书"]
        title_score = sum(0.1 for keyword in title_keywords if keyword in title)
        score += min(title_score, 0.3)
        
        return min(score, 1.0)
    
    def _classify_macro_content(self, title: str, content: str) -> str:
        """
        分类宏观内容
        """
        title_lower = title.lower()
        content_lower = content.lower()
        
        # 政策分析
        if any(keyword in title_lower or keyword in content_lower 
               for keyword in ["政策", "规划", "指导意见", "发展战略", "实施方案"]):
            return "policy"
        
        # 经济数据
        elif any(keyword in title_lower or keyword in content_lower 
                 for keyword in ["gdp", "cpi", "ppi", "数据", "统计", "指标"]):
            return "economic_data"
        
        # 市场分析
        elif any(keyword in title_lower or keyword in content_lower 
                 for keyword in ["市场", "投资", "资本", "融资", "股市", "债市"]):
            return "market_analysis"
        
        # 技术趋势
        elif any(keyword in title_lower or keyword in content_lower 
                 for keyword in ["技术", "创新", "数字化", "智能化", "科技"]):
            return "technology_trend"
        
        # 国际比较
        elif any(keyword in title_lower or keyword in content_lower 
                 for keyword in ["国际", "全球", "美国", "欧洲", "日本", "对比"]):
            return "international"
        
        # 风险分析
        elif any(keyword in title_lower or keyword in content_lower 
                 for keyword in ["风险", "挑战", "威胁", "危机", "不确定性"]):
            return "risk_analysis"
        
        else:
            return "general"
    
    async def _build_macro_knowledge_base(self, processed_data: List[Dict], topic: str):
        """
        构建宏观知识库
        """
        for data in processed_data:
            metadata = {
                "source": data["url"],
                "topic": topic,
                "content_type": data["content_type"],
                "title": data["title"],
                "authority_score": data["authority_score"]
            }
            
            # 添加到RAG系统
            self.rag_system.add_document(data["content"], metadata)
        
        self.logger.info(f"宏观知识库构建完成，共添加 {len(processed_data)} 个文档")
    
    async def _analyze_macro_data_with_stream(self, topic: str, time_range: str, region: str) -> Dict[str, Any]:
        """
        深度分析宏观数据 - 支持多轮对话和工具调用
        """
        self.logger.info(f"🤖 开始智能分析对话 - {topic}宏观分析")

        # 初始分析提示 - 企业级标准
        initial_prompt = f"""
        You are a senior macroeconomic strategist conducting a comprehensive analysis of {topic}
        (Time Frame: {time_range}, Region: {region}). This analysis must meet institutional-grade
        standards and professional macroeconomic research guidelines.

        ANALYSIS FRAMEWORK (follow this structure):
        1. **Economic Environment Assessment** - GDP, inflation, employment, monetary conditions
        2. **Monetary & Fiscal Policy Analysis** - Central bank policy, government spending, debt dynamics
        3. **Market Dynamics** - Asset prices, yield curves, currency movements, risk sentiment
        4. **Sectoral Analysis** - Industry performance, credit conditions, capital flows
        5. **International Factors** - Global trade, geopolitical risks, cross-border capital flows
        6. **Strategic Asset Allocation** - Portfolio construction, risk management, tactical positioning
        7. **Scenario Analysis** - Base case, upside/downside scenarios, tail risks

        TOOLS AVAILABLE:
        - web_search: Search for latest economic data, policy announcements, market reports
        - get_url_content: Extract detailed content from central bank reports, government publications
        - search_knowledge_base: Access historical economic data and research
        - analyze_economic_data: Perform quantitative macroeconomic analysis

        PROFESSIONAL STANDARDS:
        - Use specific economic indicators (GDP growth, inflation rates, unemployment, yield spreads)
        - Provide quantitative data to support all macroeconomic conclusions
        - Include historical context and cross-country comparisons
        - Follow professional macroeconomic research report format
        - Ensure all statements are data-driven and objective
        - Extract concrete economic metrics, policy rates, and market indicators

        Begin your analysis by searching for {topic}'s latest economic indicators,
        policy developments, and market conditions. Focus on obtaining concrete quantitative data
        including GDP growth rates, inflation metrics, policy rates, and key market indicators.
        """

        # 进行多轮对话分析
        messages = [
            {"role": "system", "content": "你是一位专业的宏观经济分析师，具有丰富的宏观研究经验。你可以使用各种工具来收集和分析信息。请进行深入、全面的分析，确保内容专业、详细，每个分析维度都要充分展开。"},
            {"role": "user", "content": initial_prompt}
        ]

        # 执行多轮对话，增加到8轮以获取更丰富的内容
        conversation_log = []
        for round_num in range(1, 9):
            self.logger.info(f"💬 执行第 {round_num} 轮分析对话")
            print(f"\n{'='*60}")
            print(f"🌍 第 {round_num} 轮宏观分析 - {topic}")
            print(f"{'='*60}")

            try:
                # 使用流式输出进行对话
                full_response = ""
                async for chunk in self.llm_client.async_chat_completion_with_tools_stream(
                    messages=messages,
                    tools=self.tools,
                    tool_functions=self.tool_functions,
                    max_iterations=6,
                    temperature=0.1,
                    show_thinking=False
                ):
                    print(chunk, end='', flush=True)
                    full_response += chunk

                conversation_log.append(f"第{round_num}轮: {full_response}")

                # 检查是否完成分析
                if self._is_analysis_complete(full_response):
                    self.logger.info(f"✅ 分析完成于第 {round_num} 轮")
                    print(f"\n\n✅ 宏观分析完成！")
                    break

                # 继续下一轮分析
                if round_num < 8:
                    follow_up_prompt = self._generate_follow_up_prompt(full_response, round_num)
                    messages.append({"role": "assistant", "content": full_response})
                    messages.append({"role": "user", "content": follow_up_prompt})

                    print(f"\n\n🔄 准备进入第 {round_num + 1} 轮分析...")

            except Exception as e:
                self.logger.error(f"第 {round_num} 轮对话异常: {str(e)}")
                print(f"\n❌ 第 {round_num} 轮对话异常: {str(e)}")
                break

        # 生成最终分析报告
        final_analysis = await self._generate_final_macro_analysis(topic, time_range, region, conversation_log)

        return final_analysis

    def _is_analysis_complete(self, content: str) -> bool:
        """判断分析是否完成"""
        completion_indicators = [
            "综合分析结论",
            "投资建议",
            "分析完成",
            "总结",
            "最终评估",
            "宏观展望",
            "政策建议"
        ]

        content_lower = content.lower()
        return any(indicator in content_lower for indicator in completion_indicators)

    def _generate_follow_up_prompt(self, previous_content: str, round_num: int) -> str:
        """生成后续分析提示"""
        follow_up_prompts = {
            1: "很好！现在请继续深入搜索宏观经济的详细基础数据，包括GDP、通胀、就业等核心指标。请确保获取准确的数据。",
            2: "请详细分析货币政策和财政政策的具体措施、实施效果和传导机制。重点关注政策对经济的影响路径。",
            3: "请深入分析产业政策和结构调整政策，包括新基建、双碳政策等对经济发展的推动作用。",
            4: "请分析当前的市场趋势、资产价格变化和投资环境。关注股市、债市、汇市的表现和风险偏好变化。",
            5: "请评估国际环境对宏观经济的影响，包括地缘政治、贸易关系和全球经济形势。",
            6: "请进行投资策略分析，包括大类资产配置建议、行业配置和风险管理策略。提供专业的投资观点。",
            7: "请全面评估宏观经济面临的各类风险因素，包括系统性风险、政策风险、外部风险等，并提供风险控制建议。"
        }

        return follow_up_prompts.get(round_num, "请基于以上所有收集的信息，提供综合的深度宏观分析结论和专业投资策略建议。")

    async def _generate_final_macro_analysis(self, topic: str, time_range: str, region: str, conversation_log: List[str]) -> Dict[str, Any]:
        """生成最终宏观分析报告 - 基于对话内容生成专业报告所需的所有章节"""
        self.logger.info(f"📋 生成最终宏观分析报告 - {topic}")

        # 合并所有对话内容
        full_conversation = "\n\n".join(conversation_log)

        # 生成专业报告所需的所有章节
        sections_to_generate = [
            "投资要点",
            "宏观环境分析",
            "政策环境分析",
            "市场趋势分析",
            "投资策略建议",
            "风险评估",
            "前景展望"
        ]

        report_data = {
            "topic": topic,
            "time_range": time_range,
            "region": region,
            "conversation_log": conversation_log,
            "data_sources": self._get_data_sources()
        }

        # 为每个章节生成详细内容
        for section in sections_to_generate:
            self.logger.info(f"📝 生成章节: {section}")
            section_content = await self._generate_section_content_from_conversation(
                topic, section, full_conversation
            )

            # 使用标准化的键名映射
            if section == "投资要点":
                report_data["executive_summary"] = section_content
                report_data["投资要点"] = section_content
            elif section == "宏观环境分析":
                report_data["macro_environment"] = section_content
                report_data["宏观环境分析"] = section_content
            elif section == "政策环境分析":
                report_data["policy_analysis"] = section_content
                report_data["政策环境分析"] = section_content
            elif section == "市场趋势分析":
                report_data["market_trends"] = section_content
                report_data["市场趋势分析"] = section_content
            elif section == "投资策略建议":
                report_data["investment_strategy"] = section_content
                report_data["投资策略建议"] = section_content
            elif section == "风险评估":
                report_data["risk_assessment"] = section_content
                report_data["风险评估"] = section_content
            elif section == "前景展望":
                report_data["outlook"] = section_content
                report_data["前景展望"] = section_content

        # 生成完整分析内容
        full_analysis = await self._generate_comprehensive_macro_analysis_from_conversation(
            topic, time_range, region, full_conversation
        )
        report_data["full_analysis"] = full_analysis

        return report_data

    async def _generate_section_content_from_conversation(self, topic: str, section_name: str, conversation_context: str) -> str:
        """基于对话内容为特定章节生成详细内容"""
        prompt = f"""
        基于以下分析对话内容，为{topic}撰写{section_name}：

        分析对话记录：
        {conversation_context}

        请基于对话中收集的信息，撰写专业的{section_name}内容。
        要求：内容专业、客观，严格基于对话中的真实数据。
        """

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位资深的宏观经济分析师，具有丰富的宏观研究经验。请严格基于提供的对话内容生成分析报告。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    async def _generate_comprehensive_macro_analysis_from_conversation(self, topic: str, time_range: str, region: str, conversation_context: str) -> str:
        """基于对话内容生成综合宏观分析"""
        prompt = f"""
        基于以下详细的分析对话内容，为{topic}生成一份综合的宏观分析报告（时间范围：{time_range}，区域：{region}）：

        分析对话记录：
        {conversation_context}

        请提供完整的分析内容，包括：
        1. 投资要点
        2. 宏观环境分析
        3. 政策环境分析
        4. 市场趋势分析
        5. 投资策略建议
        6. 风险评估
        7. 前景展望

        要求：
        - 严格基于对话中收集的真实数据和信息
        - 分析深入、专业、客观
        - 结论明确、有依据
        - 格式规范、逻辑清晰
        """

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位资深的宏观经济分析师，擅长撰写专业的宏观策略报告。请严格基于提供的对话内容进行分析。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    async def _analyze_macro_data(self, topic: str, time_range: str, region: str) -> Dict[str, Any]:
        """
        深度分析宏观数据
        """
        # 使用RAG检索不同类型的信息
        policy_context = self.rag_system.get_relevant_context(f"{topic} 政策 规划")
        economic_context = self.rag_system.get_relevant_context(f"{topic} 经济数据 指标")
        market_context = self.rag_system.get_relevant_context(f"{topic} 市场 投资")
        technology_context = self.rag_system.get_relevant_context(f"{topic} 技术 创新")
        international_context = self.rag_system.get_relevant_context(f"{topic} 国际 全球")
        risk_context = self.rag_system.get_relevant_context(f"{topic} 风险 挑战")
        
        # 构建宏观分析提示词
        analysis_prompt = f"""
        作为资深的宏观经济分析师，请基于以下信息对"{topic}"进行全面深入的宏观分析（时间范围：{time_range}，地域范围：{region}）：

        政策环境信息：
        {policy_context}

        经济数据信息：
        {economic_context}

        市场环境信息：
        {market_context}

        技术发展信息：
        {technology_context}

        国际环境信息：
        {international_context}

        风险因素信息：
        {risk_context}

        请提供以下完整的宏观分析内容：

        1. 执行摘要（概括宏观环境和主要观点，400字以内）
        
        2. 宏观环境分析
           - 全球经济形势
           - 主要经济体表现
           - 宏观经济指标分析
           - 经济周期判断
        
        3. 政策环境分析
           - 相关政策梳理
           - 政策目标与路径
           - 政策实施效果评估
           - 政策趋势预测
        
        4. 市场环境分析
           - 资本市场表现
           - 流动性环境
           - 投资趋势分析
           - 资金流向特征
        
        5. 技术发展趋势
           - 核心技术突破
           - 技术应用进展
           - 创新生态分析
           - 技术发展预测
        
        6. 国际环境影响
           - 全球格局变化
           - 国际合作与竞争
           - 地缘政治影响
           - 供应链重构
        
        7. 投资策略建议
           - 投资主线梳理
           - 资产配置建议
           - 重点关注领域
           - 投资时机把握
        
        8. 风险因素分析
           - 主要风险识别
           - 风险传导机制
           - 风险程度评估
           - 风险应对策略
        
        9. 前景展望
           - 短期发展预测（1年内）
           - 中期发展趋势（2-3年）
           - 长期发展方向（5年以上）
           - 关键节点与催化剂

        请确保分析具有前瞻性、专业性和可操作性，基于扎实的数据和逻辑分析，为投资决策提供有价值的参考。
        """
        
        # 调用LLM进行深度分析
        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位顶级的宏观经济研究专家，具有深厚的理论功底和丰富的实战经验。"},
            {"role": "user", "content": analysis_prompt}
        ], temperature=0.1)  # 使用较低温度确保分析的严谨性
        
        # 解析分析结果
        analysis_content = response.get("content", "")
        
        # 构造报告数据
        report_data = {
            "topic": topic,
            "time_range": time_range,
            "region": region,
            "executive_summary": self._extract_section(analysis_content, "执行摘要"),
            "macro_environment": self._extract_section(analysis_content, "宏观环境分析"),
            "policy_analysis": self._extract_section(analysis_content, "政策环境分析"),
            "market_analysis": self._extract_section(analysis_content, "市场环境分析"),
            "technology_trends": self._extract_section(analysis_content, "技术发展趋势"),
            "international_impact": self._extract_section(analysis_content, "国际环境影响"),
            "investment_strategy": self._extract_section(analysis_content, "投资策略建议"),
            "risk_analysis": self._extract_section(analysis_content, "风险因素分析"),
            "outlook": self._extract_section(analysis_content, "前景展望"),
            "full_analysis": analysis_content,
            "data_sources": self._get_macro_data_sources(),
            "key_indicators": await self._extract_key_indicators(economic_context),
            "policy_timeline": await self._create_policy_timeline(policy_context)
        }
        
        return report_data
    
    def _extract_section(self, content: str, section_name: str) -> str:
        """
        从分析内容中提取特定章节
        """
        lines = content.split('\n')
        section_content = []
        in_section = False
        
        for line in lines:
            line = line.strip()
            if section_name in line and any(char in line for char in ['1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.']):
                in_section = True
                continue
            elif in_section and any(num in line for num in ['1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.']):
                if section_content and not line.startswith('   '):  # 不是子项目
                    break
            
            if in_section and line:
                section_content.append(line)
        
        return '\n'.join(section_content).strip()
    
    def _get_macro_data_sources(self) -> List[str]:
        """
        获取宏观数据来源
        """
        return [
            "国家统计局宏观经济数据",
            "中国人民银行货币政策报告",
            "国家发改委政策文件",
            "财政部财政政策信息",
            "国际货币基金组织(IMF)报告",
            "世界银行发展报告",
            "经济合作与发展组织(OECD)数据",
            "主要央行政策声明",
            "权威经济研究机构报告"
        ]
    
    async def _extract_key_indicators(self, economic_context: str) -> Dict[str, str]:
        """
        提取关键经济指标
        """
        indicators = {}
        
        # 使用正则表达式提取经济指标
        import re
        
        # GDP相关
        gdp_patterns = [
            r'GDP.*?(\d+(?:\.\d+)?%)',
            r'经济增长.*?(\d+(?:\.\d+)?%)',
            r'增长率.*?(\d+(?:\.\d+)?%)'
        ]
        
        for pattern in gdp_patterns:
            matches = re.findall(pattern, economic_context)
            if matches:
                indicators["GDP增长率"] = matches[0]
                break
        
        # 通胀相关
        cpi_patterns = [
            r'CPI.*?(\d+(?:\.\d+)?%)',
            r'通胀.*?(\d+(?:\.\d+)?%)',
            r'消费者价格.*?(\d+(?:\.\d+)?%)'
        ]
        
        for pattern in cpi_patterns:
            matches = re.findall(pattern, economic_context)
            if matches:
                indicators["通胀率"] = matches[0]
                break
        
        # 失业率
        unemployment_patterns = [
            r'失业率.*?(\d+(?:\.\d+)?%)',
            r'就业.*?(\d+(?:\.\d+)?%)'
        ]
        
        for pattern in unemployment_patterns:
            matches = re.findall(pattern, economic_context)
            if matches:
                indicators["失业率"] = matches[0]
                break
        
        return indicators
    
    async def _create_policy_timeline(self, policy_context: str) -> List[Dict[str, str]]:
        """
        创建政策时间线
        """
        timeline = []
        
        # 简单的时间和政策提取
        import re
        
        # 匹配年份和政策
        year_policy_pattern = r'(\d{4})年.*?(政策|规划|方案|意见|办法)'
        matches = re.findall(year_policy_pattern, policy_context)
        
        for year, policy_type in matches[:5]:  # 最多取5个
            timeline.append({
                "year": year,
                "policy": f"{year}年相关{policy_type}出台",
                "impact": "推动行业发展"
            })
        
        return timeline
    
    async def _generate_macro_report(self, analysis_result: Dict[str, Any]) -> str:
        """
        生成最终的专业宏观策略报告 - 直接使用已生成的章节内容
        """
        print(f"\n📝 开始生成宏观DOCX报告...")
        print(f"📋 使用已生成的章节内容...")

        # 直接使用已生成的章节内容，避免重复生成
        required_sections = ["宏观环境分析", "政策分析", "市场趋势分析", "投资策略建议", "风险提示"]
        missing_sections = [section for section in required_sections if section not in analysis_result]

        if missing_sections:
            print(f"⚠️ 缺少章节: {missing_sections}，使用默认内容")
            for section in missing_sections:
                analysis_result[section] = f"{section}内容正在完善中。"

        # 直接使用analysis_result作为professional_data
        professional_data = analysis_result.copy()

        # 确保有基本的报告信息
        if "topic" not in professional_data:
            professional_data["topic"] = "宏观经济分析"

        print(f"📊 报告数据准备完成，开始生成文档...")
        print(f"⏱️  预计需要30-60秒，请耐心等待...")

        # 直接同步生成DOCX报告 - 更简单更可靠
        try:
            print(f"📝 开始生成DOCX报告...")
            print(f"⚡ 正在优化报告数据...")

            # 直接调用同步方法，避免异步复杂性
            report_path = self.professional_docx_generator.create_macro_report(professional_data)

            print(f"✅ 宏观DOCX报告生成完成！")
            self.logger.info(f"专业宏观研报生成完成: {report_path}")
            return report_path

        except Exception as e:
            print(f"⚠️  专业DOCX生成失败: {e}")
            print(f"🔄 尝试生成简化版本...")

            try:
                # 生成简化版本作为备选
                simplified_data = self._create_simplified_report_data(analysis_result)
                report_path = self.docx_generator.create_macro_report(simplified_data)
                print(f"✅ 简化版宏观DOCX报告生成完成！")
                self.logger.info(f"简化宏观研报生成完成: {report_path}")
                return report_path
            except Exception as fallback_error:
                print(f"❌ 简化版DOCX生成也失败: {fallback_error}")
                raise Exception(f"DOCX报告生成失败: {e}, 备选方案也失败: {fallback_error}")

    def _create_simplified_report_data(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """创建简化的报告数据"""
        return {
            "topic": analysis_result.get("topic", "宏观分析"),
            "time_range": analysis_result.get("time_range", "2023-2026"),
            "region": analysis_result.get("region", "全球"),
            "executive_summary": analysis_result.get("executive_summary", "执行摘要"),
            "macro_environment": analysis_result.get("macro_environment", "宏观环境分析"),
            "policy_analysis": analysis_result.get("policy_analysis", "政策分析"),
            "market_trends": analysis_result.get("market_trends", "市场趋势"),
            "investment_strategy": analysis_result.get("investment_strategy", "投资策略"),
            "risks": analysis_result.get("risks", ["宏观风险", "政策风险"])
        }

    async def _build_professional_report_data(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """构建专业报告数据 - 基于LLM深度生成"""
        topic = analysis_result.get('topic', '宏观经济分析')
        time_range = analysis_result.get('time_range', '2024-2026')
        region = analysis_result.get('region', '全球')

        # 获取知识库上下文
        macro_context = self.rag_system.get_relevant_context(f"{topic} 宏观经济 政策")
        market_context = self.rag_system.get_relevant_context(f"{topic} 市场 投资")

        # 使用LLM生成专业内容
        professional_data = await self._generate_professional_macro_content(
            topic, time_range, region, macro_context, market_context
        )

        return professional_data

    async def _generate_professional_macro_content(self, topic: str, time_range: str, region: str,
                                                 macro_context: str, market_context: str) -> Dict[str, Any]:
        """使用LLM生成专业宏观报告内容"""

        # 基础信息
        base_data = {
            "topic": topic,
            "time_range": time_range,
            "region": region,
            "analyst_name": "AI智能分析师",
            "analyst_license": "S1234567890123456",
            "analyst_phone": "010-12345678",
            "analyst_email": "<EMAIL>"
        }

        # 1. 生成投资要点
        print(f"📝 生成章节: 投资要点")
        investment_highlights = await self._generate_macro_investment_highlights(
            topic, time_range, region, macro_context
        )
        base_data["投资要点"] = investment_highlights

        # 2. 生成宏观环境分析
        print(f"📝 生成章节: 宏观环境分析")
        macro_environment = await self._generate_macro_environment_analysis(
            topic, region, macro_context
        )
        base_data["宏观环境分析"] = macro_environment

        # 3. 生成政策解读
        print(f"📝 生成章节: 政策解读")
        policy_analysis = await self._generate_macro_policy_analysis(
            topic, macro_context
        )
        base_data["政策解读"] = policy_analysis

        # 4. 生成市场趋势分析
        print(f"📝 生成章节: 市场趋势分析")
        market_trends = await self._generate_macro_market_trends(
            topic, market_context
        )
        base_data["市场趋势分析"] = market_trends

        # 5. 生成资产配置建议
        print(f"📝 生成章节: 资产配置建议")
        asset_allocation = await self._generate_asset_allocation_advice(
            topic, time_range, market_context
        )
        base_data["资产配置建议"] = asset_allocation

        # 6. 生成投资策略
        print(f"📝 生成章节: 投资策略")
        investment_strategy = await self._generate_macro_investment_strategy(
            topic, time_range, macro_context
        )
        base_data["投资策略"] = investment_strategy

        # 7. 生成风险评估
        print(f"📝 生成章节: 风险评估")
        risk_assessment = await self._generate_macro_risk_assessment(
            topic, macro_context
        )
        base_data["风险评估"] = risk_assessment

        # 8. 生成总结与展望
        print(f"📝 生成章节: 总结与展望")
        outlook = await self._generate_macro_outlook(
            topic, time_range, region, macro_context
        )
        base_data["总结与展望"] = outlook

        return base_data

    async def _generate_macro_investment_highlights(self, topic: str, time_range: str, region: str, macro_context: str) -> str:
        """生成宏观投资要点"""
        prompt = f"""
作为资深宏观策略分析师，请为"{topic}"（时间范围：{time_range}，地域：{region}）撰写专业的投资要点：

宏观信息：
{macro_context}

请按照以下格式生成投资要点：

## 核心观点
[用1-2句话概括核心投资观点]

## 主要投资逻辑：
1. **宏观环境支撑**：[宏观经济环境对投资的支撑作用]
2. **政策驱动因素**：[政策对投资的推动作用]
3. **市场机会识别**：[具体的市场投资机会]
4. **长期价值判断**：[长期投资价值评估]

## 关键宏观数据：
- GDP增长：[GDP增长率和趋势]
- 通胀水平：[CPI/PPI水平和预期]
- 货币政策：[利率水平和政策取向]
- 财政政策：[财政政策力度和方向]

## 投资主线：
- [列出3-4个主要投资主线]

## 配置建议：
- **核心配置**：[核心资产配置建议]
- **弹性配置**：[弹性资产配置建议]
- **风险对冲**：[风险对冲工具建议]

## 关键催化剂：
- [列出可能的催化剂事件]

要求：
1. 观点明确、逻辑清晰
2. 数据支撑、分析深入
3. 突出宏观视角
4. 具有前瞻性和指导性
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位资深的宏观策略分析师，具有丰富的宏观投资研究经验。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    async def _generate_macro_environment_analysis(self, topic: str, region: str, macro_context: str) -> str:
        """生成宏观环境分析"""
        prompt = f"""
作为专业宏观经济分析师，请为"{topic}"（地域：{region}）撰写详细的宏观环境分析：

宏观信息：
{macro_context}

请按照以下结构生成：

## 全球宏观环境
### 主要经济体表现
- **美国经济**：[美国经济现状和趋势]
- **欧洲经济**：[欧洲经济现状和趋势]
- **中国经济**：[中国经济现状和趋势]
- **新兴市场**：[新兴市场经济表现]

### 全球经济指标
请提供全球经济数据（JSON格式）：
```json
{{
  "经济体": ["美国", "欧元区", "中国", "日本", "新兴市场"],
  "GDP增长率": ["", "", "", "", ""],
  "通胀率": ["", "", "", "", ""],
  "失业率": ["", "", "", "", ""],
  "政策利率": ["", "", "", "", ""]
}}
```

## 国内宏观环境
### 经济增长
- **GDP表现**：[GDP增长情况和结构]
- **增长动力**：[经济增长的主要驱动因素]
- **增长质量**：[经济增长质量评估]

### 通胀环境
- **CPI走势**：[消费者价格指数变化]
- **PPI变化**：[生产者价格指数变化]
- **通胀预期**：[通胀预期和管理]

### 就业市场
- **就业状况**：[就业率和失业率情况]
- **劳动力市场**：[劳动力供需状况]
- **收入增长**：[居民收入增长情况]

## 货币政策环境
### 政策取向
- **政策基调**：[货币政策总体基调]
- **利率水平**：[基准利率和市场利率]
- **流动性状况**：[市场流动性充裕程度]

### 政策工具
- **公开市场操作**：[央行公开市场操作]
- **准备金率**：[存款准备金率调整]
- **结构性工具**：[定向降准等结构性工具]

## 财政政策环境
### 财政状况
- **财政收支**：[财政收入和支出情况]
- **债务水平**：[政府债务规模和结构]
- **财政空间**：[财政政策空间评估]

### 政策重点
- **支出结构**：[财政支出重点领域]
- **减税降费**：[减税降费政策措施]
- **专项债券**：[地方专项债券发行]

## 国际环境影响
### 地缘政治
- **国际关系**：[主要国际关系变化]
- **贸易环境**：[国际贸易环境]
- **供应链**：[全球供应链状况]

### 汇率环境
- **汇率走势**：[主要货币汇率变化]
- **汇率政策**：[汇率政策取向]
- **资本流动**：[跨境资本流动情况]

## 宏观环境评估
请提供环境评估（JSON格式）：
```json
{{
  "环境因素": ["经济增长", "通胀环境", "货币政策", "财政政策", "国际环境"],
  "当前状况": ["好/中/差", "好/中/差", "好/中/差", "好/中/差", "好/中/差"],
  "变化趋势": ["改善/稳定/恶化", "改善/稳定/恶化", "改善/稳定/恶化", "改善/稳定/恶化", "改善/稳定/恶化"],
  "影响程度": ["重大/中等/轻微", "重大/中等/轻微", "重大/中等/轻微", "重大/中等/轻微", "重大/中等/轻微"]
}}
```

要求：
1. 分析全面、深入
2. 数据准确、及时
3. 逻辑清晰、客观
4. 突出关键变化和趋势
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位专业的宏观经济分析师，具有丰富的宏观环境研究经验。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    async def _generate_macro_policy_analysis(self, topic: str, macro_context: str) -> str:
        """生成宏观政策解读"""
        prompt = f"""
作为专业政策分析师，请为"{topic}"撰写详细的政策解读：

宏观信息：
{macro_context}

请按照以下结构生成：

## 政策环境概述
[当前政策环境总体评价]

## 重要政策解读
### 货币政策
- **政策基调**：[货币政策总体基调和目标]
- **政策工具**：[主要政策工具使用情况]
- **政策效果**：[政策实施效果评估]
- **未来预期**：[货币政策未来走向]

### 财政政策
- **政策重点**：[财政政策重点方向]
- **支出结构**：[财政支出结构调整]
- **减税措施**：[减税降费政策措施]
- **债务管理**：[政府债务管理政策]

### 产业政策
- **支持重点**：[产业政策支持重点]
- **发展规划**：[相关产业发展规划]
- **扶持措施**：[具体扶持政策措施]
- **监管要求**：[行业监管政策要求]

## 政策影响分析
### 对经济增长的影响
- **短期影响**：[政策对短期经济增长的影响]
- **中长期影响**：[政策对中长期增长的影响]
- **结构性影响**：[政策对经济结构的影响]

### 对市场的影响
- **资本市场**：[政策对股市、债市的影响]
- **货币市场**：[政策对利率、汇率的影响]
- **商品市场**：[政策对大宗商品的影响]

### 对投资的影响
- **投资方向**：[政策引导的投资方向]
- **投资机会**：[政策创造的投资机会]
- **投资风险**：[政策变化带来的投资风险]

## 政策预期与展望
### 短期政策预期（6-12个月）
- **货币政策**：[短期货币政策预期]
- **财政政策**：[短期财政政策预期]
- **监管政策**：[短期监管政策预期]

### 中长期政策方向（1-3年）
- **政策目标**：[中长期政策目标]
- **政策重点**：[政策支持重点领域]
- **改革方向**：[重要改革方向]

要求：
1. 政策解读准确、深入
2. 影响分析全面、客观
3. 预期判断合理、有依据
4. 突出投资相关性
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位专业的宏观政策分析师，具有丰富的政策研究和解读经验。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    async def _generate_asset_allocation_advice(self, topic: str, time_range: str, market_context: str) -> str:
        """生成资产配置建议"""
        prompt = f"""
作为专业资产配置分析师，请为"{topic}"（时间范围：{time_range}）撰写详细的资产配置建议：

市场信息：
{market_context}

请按照以下结构生成：

## 配置策略概述
[总体配置策略和理念]

## 大类资产配置
### 权益类资产
- **配置比例**：[建议配置比例]
- **配置逻辑**：[权益配置的逻辑]
- **重点方向**：[重点配置方向]
- **风险收益**：[预期风险收益特征]

### 固定收益类
- **配置比例**：[建议配置比例]
- **品种选择**：[债券品种选择]
- **久期策略**：[久期配置策略]
- **信用策略**：[信用风险管理]

### 商品类资产
- **配置比例**：[建议配置比例]
- **品种选择**：[商品品种选择]
- **配置时机**：[配置时机选择]
- **对冲功能**：[通胀对冲功能]

### 另类投资
- **配置比例**：[建议配置比例]
- **投资方向**：[另类投资方向]
- **风险特征**：[风险收益特征]

## 行业配置建议
请提供行业配置表（JSON格式）：
```json
{{
  "行业": ["科技", "消费", "医疗", "金融", "制造", "能源"],
  "配置权重": ["", "", "", "", "", ""],
  "配置逻辑": ["", "", "", "", "", ""],
  "风险等级": ["高/中/低", "高/中/低", "高/中/低", "高/中/低", "高/中/低", "高/中/低"]
}}
```

## 区域配置建议
### 国内市场
- **配置比例**：[国内市场配置比例]
- **重点区域**：[重点关注区域]
- **配置理由**：[国内配置理由]

### 海外市场
- **配置比例**：[海外市场配置比例]
- **重点市场**：[重点海外市场]
- **汇率考虑**：[汇率风险考虑]

## 风险管理
### 风险控制
- **风险预算**：[风险预算分配]
- **止损策略**：[止损策略设置]
- **对冲工具**：[风险对冲工具]

### 流动性管理
- **流动性需求**：[流动性需求评估]
- **流动性配置**：[流动性资产配置]
- **应急预案**：[流动性应急预案]

## 动态调整策略
### 再平衡机制
- **再平衡频率**：[组合再平衡频率]
- **触发条件**：[再平衡触发条件]
- **调整幅度**：[调整幅度控制]

### 战术调整
- **市场时机**：[战术调整时机]
- **调整方向**：[可能的调整方向]
- **调整依据**：[调整决策依据]

要求：
1. 配置建议具体、可操作
2. 风险收益匹配合理
3. 考虑宏观环境变化
4. 提供动态调整机制
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位专业的资产配置分析师，具有丰富的投资组合管理经验。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    async def _generate_macro_market_trends(self, topic: str, market_context: str) -> str:
        """生成市场趋势分析"""
        prompt = f"""
请为"{topic}"撰写详细的市场趋势分析：

市场信息：
{market_context}

请按照以下结构生成：

## 市场趋势概述
[当前市场总体趋势判断]

## 资本市场趋势
### 股票市场
- **市场表现**：[股市整体表现和特征]
- **估值水平**：[市场估值水平评估]
- **资金流向**：[资金流入流出情况]
- **投资者情绪**：[投资者情绪指标]

### 债券市场
- **收益率曲线**：[债券收益率曲线变化]
- **信用利差**：[信用利差变化趋势]
- **发行情况**：[债券发行市场情况]

### 商品市场
- **价格走势**：[大宗商品价格趋势]
- **供需关系**：[供需基本面分析]
- **库存水平**：[库存变化情况]

## 行业轮动分析
请提供行业表现数据（JSON格式）：
```json
{{
  "行业": ["科技", "消费", "医疗", "金融", "周期", "公用"],
  "近期表现": ["", "", "", "", "", ""],
  "相对强弱": ["强/中/弱", "强/中/弱", "强/中/弱", "强/中/弱", "强/中/弱", "强/中/弱"],
  "趋势判断": ["上升/震荡/下降", "上升/震荡/下降", "上升/震荡/下降", "上升/震荡/下降", "上升/震荡/下降", "上升/震荡/下降"]
}}
```

## 资金流向分析
- **机构资金**：[机构投资者资金流向]
- **外资流向**：[外资流入流出情况]
- **散户资金**：[散户投资者行为]
- **新增资金**：[新增资金来源和规模]

## 市场风险偏好
- **风险偏好指标**：[市场风险偏好水平]
- **避险情绪**：[避险资产表现]
- **波动率水平**：[市场波动率变化]

要求：
1. 趋势判断准确、客观
2. 数据支撑充分
3. 分析逻辑清晰
4. 突出投资机会
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位专业的市场趋势分析师。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    async def _generate_macro_investment_strategy(self, topic: str, time_range: str, macro_context: str) -> str:
        """生成宏观投资策略"""
        prompt = f"""
请为"{topic}"（时间范围：{time_range}）制定详细的投资策略：

宏观信息：
{macro_context}

请按照以下结构生成：

## 投资策略概述
[总体投资策略和核心观点]

## 投资主线
### 主线一：[主线名称]
- **投资逻辑**：[该主线的投资逻辑]
- **受益标的**：[相关受益标的]
- **配置时机**：[最佳配置时机]
- **风险因素**：[主要风险因素]

### 主线二：[主线名称]
[同上结构]

### 主线三：[主线名称]
[同上结构]

## 阶段性策略
### 短期策略（3-6个月）
- **策略重点**：[短期策略重点]
- **配置方向**：[短期配置方向]
- **操作建议**：[具体操作建议]

### 中期策略（6-18个月）
- **策略重点**：[中期策略重点]
- **配置调整**：[中期配置调整]
- **关注要点**：[中期关注要点]

## 投资时机把握
- **买入时机**：[最佳买入时机判断]
- **加仓时机**：[加仓时机选择]
- **减仓时机**：[减仓时机判断]
- **止损策略**：[止损策略设置]

## 风险管理策略
- **风险识别**：[主要风险因素识别]
- **风险控制**：[风险控制措施]
- **对冲策略**：[风险对冲策略]

要求：
1. 策略明确、可操作
2. 逻辑清晰、有说服力
3. 考虑不同时间维度
4. 注重风险管理
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位专业的宏观投资策略分析师。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    async def _generate_macro_risk_assessment(self, topic: str, macro_context: str) -> str:
        """生成宏观风险评估"""
        prompt = f"""
请为"{topic}"进行全面的宏观风险评估：

宏观信息：
{macro_context}

请按照以下结构生成：

## 风险评级
**整体风险等级：高/中高/中等/中低/低**

## 主要风险因素
### 1. 政策风险
- **货币政策风险**：[货币政策变化风险]
- **财政政策风险**：[财政政策调整风险]
- **监管政策风险**：[监管政策变化风险]

### 2. 经济风险
- **增长放缓风险**：[经济增长放缓风险]
- **通胀风险**：[通胀上升或通缩风险]
- **就业风险**：[就业市场恶化风险]

### 3. 市场风险
- **流动性风险**：[市场流动性紧张风险]
- **波动性风险**：[市场波动加剧风险]
- **系统性风险**：[系统性金融风险]

### 4. 国际风险
- **地缘政治风险**：[地缘政治冲突风险]
- **贸易摩擦风险**：[国际贸易摩擦风险]
- **汇率风险**：[汇率大幅波动风险]

## 风险传导机制
[分析各类风险的传导路径和相互影响]

## 风险应对策略
- **政策风险应对**：[政策风险的应对措施]
- **市场风险应对**：[市场风险的管控方法]
- **流动性管理**：[流动性风险管理]
- **组合保护**：[投资组合保护策略]

要求：
1. 风险识别全面、准确
2. 风险评估客观、量化
3. 应对策略具体、可行
4. 突出系统性风险
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位专业的宏观风险分析师。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    async def _generate_macro_outlook(self, topic: str, time_range: str, region: str, macro_context: str) -> str:
        """生成宏观展望"""
        prompt = f"""
请为"{topic}"（时间范围：{time_range}，地域：{region}）撰写总结与展望：

宏观信息：
{macro_context}

请按照以下结构生成：

## 核心结论
[用2-3句话总结核心观点和结论]

## 关键判断
- **宏观环境**：[对宏观环境的总体判断]
- **政策取向**：[对政策方向的判断]
- **市场趋势**：[对市场趋势的判断]
- **投资机会**：[对投资机会的判断]

## 情景分析
### 基准情形（概率60%）
- **关键假设**：[基准情形的关键假设]
- **发展路径**：[预期的发展路径]
- **投资建议**：[相应的投资建议]

### 乐观情形（概率25%）
- **触发条件**：[乐观情形的触发条件]
- **发展前景**：[乐观情形下的发展前景]
- **投资策略**：[乐观情形下的投资策略]

### 悲观情形（概率15%）
- **风险因素**：[悲观情形的风险因素]
- **影响程度**：[悲观情形的影响程度]
- **应对策略**：[悲观情形下的应对策略]

## 关键节点
- **重要时间点**：[需要关注的重要时间节点]
- **关键事件**：[可能影响趋势的关键事件]
- **政策节点**：[重要的政策时间节点]

## 投资建议总结
- **配置建议**：[总体配置建议]
- **操作策略**：[具体操作策略]
- **风险提示**：[主要风险提示]

要求：
1. 结论明确、有说服力
2. 展望合理、有依据
3. 考虑多种可能性
4. 提供具体指导
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位资深的宏观策略分析师，具有丰富的前瞻性分析经验。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")
    
    async def generate_macro_charts(self, topic: str) -> List[str]:
        """
        生成宏观分析图表
        """
        try:
            import matplotlib.pyplot as plt
            import numpy as np
            
            chart_paths = []
            
            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei']
            plt.rcParams['axes.unicode_minus'] = False
            
            # 创建宏观分析综合图表
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
            
            # 1. 经济指标趋势图
            years = np.arange(2020, 2025)
            gdp_growth = [6.1, 2.3, 8.1, 5.2, 5.0]  # 示例数据
            cpi = [2.9, 0.9, 2.1, 2.0, 1.8]
            
            ax1.plot(years, gdp_growth, 'bo-', label='GDP增长率(%)', linewidth=2)
            ax1.plot(years, cpi, 'ro-', label='CPI(%)', linewidth=2)
            ax1.set_title(f'{topic} - 主要经济指标趋势')
            ax1.set_xlabel('年份')
            ax1.set_ylabel('百分比(%)')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 2. 投资规模分布
            investment_categories = ['基础设施', '技术研发', '人才培养', '产业应用', '其他']
            investment_amounts = [30, 25, 15, 20, 10]
            colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
            
            ax2.pie(investment_amounts, labels=investment_categories, colors=colors, 
                   autopct='%1.1f%%', startangle=90)
            ax2.set_title(f'{topic} - 投资规模分布')
            
            # 3. 政策支持力度评估
            policy_areas = ['财政政策', '货币政策', '产业政策', '监管政策', '国际合作']
            support_levels = [0.8, 0.7, 0.9, 0.6, 0.5]
            
            bars = ax3.bar(policy_areas, support_levels, color='lightblue', alpha=0.7)
            ax3.set_title(f'{topic} - 政策支持力度评估')
            ax3.set_ylabel('支持力度(0-1)')
            ax3.set_ylim(0, 1)
            
            # 在柱子上添加数值
            for bar, level in zip(bars, support_levels):
                height = bar.get_height()
                ax3.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                        f'{level:.1f}', ha='center', va='bottom')
            
            # 4. 风险因素雷达图
            risk_factors = ['技术风险', '市场风险', '政策风险', '竞争风险', '资金风险']
            risk_levels = [0.6, 0.4, 0.3, 0.7, 0.5]
            
            angles = np.linspace(0, 2 * np.pi, len(risk_factors), endpoint=False).tolist()
            risk_levels += risk_levels[:1]  # 闭合图形
            angles += angles[:1]
            
            ax4 = plt.subplot(2, 2, 4, projection='polar')
            ax4.plot(angles, risk_levels, 'o-', linewidth=2, color='red')
            ax4.fill(angles, risk_levels, alpha=0.25, color='red')
            ax4.set_xticks(angles[:-1])
            ax4.set_xticklabels(risk_factors)
            ax4.set_ylim(0, 1)
            ax4.set_title(f'{topic} - 风险因素评估')
            
            plt.tight_layout()
            
            chart_path = f"data/outputs/{topic}_macro_analysis.png"
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            chart_paths.append(chart_path)
            
            self.logger.info(f"生成宏观图表: {chart_paths}")
            return chart_paths
            
        except Exception as e:
            self.logger.error(f"生成宏观图表失败: {str(e)}")
            return []

    # 工具函数实现
    async def _tool_web_search(self, query: str, num_results: int = 5) -> str:
        """工具：网络搜索"""
        try:
            self.logger.info(f"🔍 执行网络搜索: {query}")

            # 验证参数
            if not query or not isinstance(query, str):
                return "❌ 搜索查询不能为空"

            if not isinstance(num_results, int) or num_results <= 0:
                num_results = 5

            results = await self.execute_with_retry(
                self.web_search_tool.search, query, num_results=num_results
            )

            if results and isinstance(results, list):
                formatted_results = []
                for i, result in enumerate(results[:num_results], 1):
                    if isinstance(result, dict):
                        title = result.get('title', 'N/A')
                        url = result.get('url', 'N/A')
                        snippet = result.get('snippet', 'N/A')
                        formatted_results.append(f"{i}. {title}\n   URL: {url}\n   摘要: {snippet}")

                if formatted_results:
                    return f"✅ 搜索到 {len(formatted_results)} 条结果：\n\n" + "\n\n".join(formatted_results)
                else:
                    return "❌ 搜索结果格式异常"
            else:
                return "❌ 未找到相关搜索结果"

        except Exception as e:
            self.logger.error(f"网络搜索失败: {str(e)}")
            return f"❌ 搜索失败: {str(e)}"

    async def _tool_get_url_content(self, url: str) -> str:
        """工具：获取URL内容"""
        try:
            self.logger.info(f"📄 获取URL内容: {url}")

            # 验证URL参数
            if not url or not isinstance(url, str):
                return "❌ URL不能为空"

            if not url.startswith(('http://', 'https://')):
                return "❌ URL格式无效，必须以http://或https://开头"

            content = await self.execute_with_retry(
                self.url_content_tool.get_content, url
            )

            if content and isinstance(content, str):
                # 限制内容长度
                if len(content) > 3000:
                    content = content[:3000] + "...[内容已截断]"
                return f"✅ 成功获取URL内容：\n\n{content}"
            else:
                return "❌ 无法获取URL内容或内容为空"

        except Exception as e:
            self.logger.error(f"获取URL内容失败: {str(e)}")
            return f"❌ 获取内容失败: {str(e)}"

    async def _tool_search_knowledge_base(self, query: str) -> str:
        """工具：搜索知识库"""
        try:
            self.logger.info(f"🧠 搜索知识库: {query}")

            # 验证查询参数
            if not query or not isinstance(query, str):
                return "❌ 搜索查询不能为空"

            context = await self.execute_with_retry(
                self.rag_system.get_relevant_context, query
            )

            if context and isinstance(context, str) and context.strip():
                # 限制内容长度
                if len(context) > 2000:
                    context = context[:2000] + "...[内容已截断]"
                return f"✅ 知识库搜索结果：\n\n{context}"
            else:
                return "❌ 知识库中未找到相关信息"

        except Exception as e:
            self.logger.error(f"知识库搜索失败: {str(e)}")
            return f"❌ 知识库搜索失败: {str(e)}"

    async def _tool_add_to_knowledge_base(self, content: str, metadata: dict = None) -> str:
        """工具：添加到知识库"""
        try:
            self.logger.info(f"📚 添加内容到知识库")

            # 验证内容参数
            if not content or not isinstance(content, str):
                return "❌ 内容不能为空"

            if len(content.strip()) < 10:
                return "❌ 内容太短，无法添加到知识库"

            if metadata is None:
                metadata = {"source": "manual_input", "type": "analysis"}
            elif not isinstance(metadata, dict):
                metadata = {"source": "manual_input", "type": "analysis"}

            await self.execute_with_retry(
                self.rag_system.add_document, content, metadata
            )
            return f"✅ 成功添加 {len(content)} 字符的内容到知识库"

        except Exception as e:
            self.logger.error(f"添加到知识库失败: {str(e)}")
            return f"❌ 添加到知识库失败: {str(e)}"

    async def _tool_analyze_economic_data(self, data: str, analysis_type: str = "comprehensive") -> str:
        """工具：分析宏观经济数据"""
        try:
            self.logger.info(f"📊 分析宏观经济数据: {analysis_type}")

            # 验证参数
            if not data or not isinstance(data, str):
                return "❌ 经济数据不能为空"

            if len(data.strip()) < 20:
                return "❌ 经济数据太少，无法进行有效分析"

            # 验证分析类型
            valid_types = ["gdp", "inflation", "monetary", "fiscal", "comprehensive"]
            if analysis_type not in valid_types:
                analysis_type = "comprehensive"

            analysis_prompt = f"""
            作为专业宏观经济分析师，请对以下经济数据进行{analysis_type}分析：

            经济数据：
            {data}

            分析类型：{analysis_type}

            请提供：
            1. 宏观经济指标解读
            2. 经济周期判断
            3. 政策影响分析
            4. 市场影响评估
            5. 投资策略建议

            请确保分析专业、客观、基于数据。
            """

            response = await self.llm_client.async_chat_completion([
                {"role": "system", "content": "你是一位资深的宏观经济分析师。"},
                {"role": "user", "content": analysis_prompt}
            ], temperature=0.1)

            content = response.get("content", "")
            if content:
                return f"✅ 宏观经济数据分析结果：\n\n{content}"
            else:
                return "❌ 宏观经济数据分析失败，未获得有效结果"

        except Exception as e:
            self.logger.error(f"宏观经济数据分析失败: {str(e)}")
            return f"❌ 宏观经济数据分析失败: {str(e)}"
