2025-06-19 00:35:36 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 00:35:36 - FinancialReportSystem - INFO - logger.py:111 - info - 启动智能模式生成报告: 生成商汤科技的公司研报
2025-06-19 00:36:36 - FinancialReportSystem - INFO - logger.py:111 - info - 智能模式报告生成完成
2025-06-19 00:36:50 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 00:36:50 - FinancialReportSystem - INFO - logger.py:111 - info - 启动智能模式生成报告: 生成商汤科技公司研报、智能风控行业研报和生成式AI宏观研报，符合比赛要求
2025-06-19 00:42:43 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 00:42:43 - FinancialReportSystem - INFO - logger.py:111 - info - 启动智能模式生成报告: 生成商汤科技的公司研报
2025-06-19 00:43:04 - FinancialReportSystem - INFO - logger.py:111 - info - 智能模式报告生成完成
2025-06-19 00:44:42 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 00:44:42 - FinancialReportSystem - INFO - logger.py:111 - info - 启动智能模式生成报告: 生成商汤科技的公司研报
2025-06-19 00:45:05 - FinancialReportSystem - INFO - logger.py:111 - info - 智能模式报告生成完成
2025-06-19 00:45:47 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 00:45:47 - FinancialReportSystem - INFO - logger.py:111 - info - 启动智能模式生成报告: 生成商汤科技的公司研报
2025-06-19 00:46:59 - FinancialReportSystem - INFO - logger.py:111 - info - 智能模式报告生成完成
2025-06-19 00:47:12 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 00:47:12 - FinancialReportSystem - INFO - logger.py:111 - info - 启动智能模式生成报告: 生成商汤科技公司研报、智能风控行业研报和生成式AI宏观研报，符合比赛要求
2025-06-19 01:10:10 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 01:10:10 - FinancialReportSystem - INFO - logger.py:111 - info - 启动智能模式生成报告: 生成商汤科技的专业公司研报
2025-06-19 01:11:22 - FinancialReportSystem - INFO - logger.py:111 - info - 智能模式报告生成完成
2025-06-19 01:34:44 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 01:34:44 - FinancialReportSystem - INFO - logger.py:111 - info - 启动智能模式生成报告: 生成商汤科技的专业公司研报
2025-06-19 01:36:27 - FinancialReportSystem - INFO - logger.py:111 - info - 智能模式报告生成完成
2025-06-19 01:36:56 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 01:36:56 - FinancialReportSystem - INFO - logger.py:111 - info - 启动智能模式生成报告: 生成完整的金融研报包括公司、行业和宏观分析
2025-06-19 01:54:47 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 01:54:47 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 01:55:42 - FinancialReportSystem - ERROR - logger.py:120 - error - 生成company报告失败: asyncio.run() cannot be called from a running event loop - Exception: asyncio.run() cannot be called from a running event loop
Traceback (most recent call last):
  File "/home/<USER>/agent/financial_report_agent/main.py", line 85, in generate_single_report
    report_path = await self.company_agent.run(company_name, stock_code)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/agent/financial_report_agent/src/agents/company_agent.py", line 88, in run
    raise e
  File "/home/<USER>/agent/financial_report_agent/src/agents/company_agent.py", line 81, in run
    report_path = await self._generate_company_report(analysis_result)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/agent/financial_report_agent/src/agents/company_agent.py", line 310, in _generate_company_report
    professional_data = self._build_professional_report_data(analysis_result)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/agent/financial_report_agent/src/agents/company_agent.py", line 333, in _build_professional_report_data
    professional_data = asyncio.run(self._generate_professional_content(
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/py312/lib/python3.12/asyncio/runners.py", line 191, in run
    raise RuntimeError(
RuntimeError: asyncio.run() cannot be called from a running event loop
2025-06-19 01:58:35 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 01:58:35 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 02:07:36 - FinancialReportSystem - INFO - logger.py:111 - info - company报告生成完成: data/outputs/Company_Report_20250619_020736.docx
2025-06-19 02:14:43 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 02:14:43 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 02:27:02 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 02:27:02 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 02:29:33 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 02:29:33 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 02:29:42 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 02:29:42 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 02:37:14 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 02:37:14 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 02:40:40 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 02:40:40 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 02:47:47 - FinancialReportSystem - ERROR - logger.py:120 - error - 生成company报告失败: name 're' is not defined - Exception: name 're' is not defined
Traceback (most recent call last):
  File "/home/<USER>/agent/financial_report_agent/main.py", line 85, in generate_single_report
    report_path = await self.company_agent.run(company_name, stock_code)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/agent/financial_report_agent/src/agents/company_agent.py", line 221, in run
    raise e
  File "/home/<USER>/agent/financial_report_agent/src/agents/company_agent.py", line 209, in run
    report_path = await self._generate_company_report(analysis_result)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/agent/financial_report_agent/src/agents/company_agent.py", line 537, in _generate_company_report
    report_path = await loop.run_in_executor(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/py312/lib/python3.12/concurrent/futures/thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/agent/financial_report_agent/src/tools/professional_docx_generator.py", line 116, in create_company_report
    return self._create_report("company", data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/agent/financial_report_agent/src/tools/professional_docx_generator.py", line 149, in _create_report
    self._add_report_sections(doc, data, structure['sections'])
  File "/home/<USER>/agent/financial_report_agent/src/tools/professional_docx_generator.py", line 311, in _add_report_sections
    self._add_formatted_text(doc, section_content)
  File "/home/<USER>/agent/financial_report_agent/src/tools/professional_docx_generator.py", line 379, in _add_formatted_text
    content = self._convert_json_to_tables(doc, content)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/agent/financial_report_agent/src/tools/professional_docx_generator.py", line 415, in _convert_json_to_tables
    matches = re.findall(json_pattern, content, re.DOTALL)
              ^^
NameError: name 're' is not defined. Did you forget to import 're'
2025-06-19 03:02:12 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 03:02:12 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 03:14:43 - FinancialReportSystem - INFO - logger.py:111 - info - company报告生成完成: data/outputs/Company_Report_20250619_031443.docx
2025-06-19 03:30:59 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 03:30:59 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 03:31:20 - FinancialReportSystem - INFO - logger.py:111 - info - company报告生成完成: data/outputs/Company_Report_20250619_033120.docx
2025-06-19 03:33:44 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 03:33:44 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 03:48:42 - FinancialReportSystem - INFO - logger.py:111 - info - company报告生成完成: data/outputs/Company_Report_20250619_034842.docx
2025-06-19 03:53:52 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 03:53:52 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 03:56:49 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 03:56:49 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 04:07:56 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 04:07:56 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 04:20:57 - FinancialReportSystem - INFO - logger.py:111 - info - company报告生成完成: data/outputs/Company_Report_20250619_042057.docx
2025-06-19 10:40:00 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 10:40:00 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 10:42:24 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 10:42:24 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 10:59:22 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 10:59:22 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成所有报告: {'company': '商汤科技（00020.HK）', 'industry': '智能风控&大数据征信服务', 'macro': '生成式AI基建与算力投资趋势（2023-2026）'}
2025-06-19 11:07:04 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 11:07:04 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 11:08:20 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 11:08:20 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 11:10:35 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 11:10:35 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 11:13:43 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 11:13:43 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 11:27:51 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 11:27:51 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 11:30:45 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 11:30:45 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 11:48:23 - FinancialReportSystem - INFO - logger.py:111 - info - company报告生成完成: data/outputs/Company_Report_20250619_114823.docx
2025-06-19 11:50:28 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 11:50:28 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 比亚迪
2025-06-19 12:04:37 - FinancialReportSystem - INFO - logger.py:111 - info - company报告生成完成: data/outputs/Company_Report_20250619_120436.docx
2025-06-19 12:53:46 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 12:53:46 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 12:57:08 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 12:57:08 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 12:58:23 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 12:58:23 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 13:02:57 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 13:02:57 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 13:25:35 - FinancialReportSystem - INFO - logger.py:111 - info - company报告生成完成: data/outputs/Company_Report_20250619_132534.docx
2025-06-19 13:27:16 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 13:27:16 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 13:52:25 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 13:52:25 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 13:54:17 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 13:54:17 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 13:55:48 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 13:55:48 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 13:56:47 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 13:56:47 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 14:07:37 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 14:07:37 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 14:27:38 - FinancialReportSystem - INFO - logger.py:111 - info - company报告生成完成: data/outputs/Enterprise_Company_Report_商汤科技_20250619_142738.txt
2025-06-19 14:54:20 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 14:54:20 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 15:09:39 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 15:09:39 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 15:10:04 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 15:10:04 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 15:17:06 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 15:17:06 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
2025-06-19 15:18:08 - FinancialReportSystem - INFO - logger.py:111 - info - 金融研报生成系统初始化完成
2025-06-19 15:18:08 - FinancialReportSystem - INFO - logger.py:111 - info - 开始生成company报告: 商汤科技
