# config/settings.py
import os
from typing import Dict, Any

class Config:
    """系统配置类 - 完整版本"""
    
    # OpenAI配置（兼容阿里云百炼）
    OPENAI_API_KEY = os.getenv("DASHSCOPE_API_KEY", "sk-64d52e2c0efe474ab9a2020121d1d645")
    OPENAI_BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    OPENAI_MODEL = "qwen3-235b-a22b"
    MULTIMODAL_MODEL = "qvq-72b-preview"
    
    # 搜索配置
    SEARCH_API_URL = "https://api.bochaai.com/v1/web-search"
    SEARCH_API_KEY = "sk-d35c7df07e60438d9a15e6e39c3806a0"
    SEARCH_MODE = "cache_and_search"  # cache_first, search_only, cache_and_search, cache_only
    
    # 缓存配置
    CACHE_EXPIRY_HOURS = 2400
    MAX_CACHE_FILES = 10000
    CACHE_DIR = "data/cache"
    
    # 系统配置
    MAX_CONCURRENT_TASKS = 5
    REPORT_OUTPUT_DIR = "data/outputs"
    ENABLE_CACHING = True
    LOG_LEVEL = "INFO"
    
    # 文件路径
    SEARCH_CACHE_DIR = "data/cache/search_cache"
    URL_CACHE_DIR = "data/cache/url_cache"
    VECTOR_DB_DIR = "data/cache/vector_db"
    
    # 报告配置
    COMPANY_REPORT_TEMPLATE = "Company_Research_Report.docx"
    INDUSTRY_REPORT_TEMPLATE = "Industry_Research_Report.docx"
    MACRO_REPORT_TEMPLATE = "Macro_Research_Report.docx"
    
    # 数据处理配置
    MAX_TEXT_LENGTH = 10000
    MIN_TEXT_LENGTH = 100
    BATCH_SIZE = 50
    TIMEOUT_SECONDS = 60  # 增加到60秒，避免URL内容抓取超时
    
    # 风险控制配置
    MAX_RETRY_ATTEMPTS = 3
    RETRY_DELAY_SECONDS = 2
    ERROR_LOG_FILE = "data/logs/error.log"
    
    @classmethod
    def to_dict(cls) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            key: value for key, value in cls.__dict__.items()
            if not key.startswith('_') and not callable(value)
        }
    
    @classmethod
    def create_directories(cls):
        """创建必要的目录"""
        directories = [
            cls.CACHE_DIR,
            cls.SEARCH_CACHE_DIR,
            cls.URL_CACHE_DIR,
            cls.VECTOR_DB_DIR,
            cls.REPORT_OUTPUT_DIR,
            "data/logs"
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
