# src/utils/cache_manager.py
import os
import json
import time
import hashlib
import sqlite3
from typing import Any, Dict, Optional, List
from datetime import datetime, timedelta
from config.settings import Config

class CacheManager:
    """
    缓存管理器 - 统一管理系统缓存
    """
    
    def __init__(self, config: Config = None):
        self.config = config or Config()
        self.cache_dir = self.config.CACHE_DIR
        self.db_path = os.path.join(self.cache_dir, "cache.db")
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # 初始化数据库
        self._init_database()
        
        # 缓存统计
        self.stats = {
            "hits": 0,
            "misses": 0,
            "writes": 0,
            "deletes": 0
        }
    
    def _init_database(self):
        """初始化缓存数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS cache_entries (
                        key TEXT PRIMARY KEY,
                        value TEXT NOT NULL,
                        created_at REAL NOT NULL,
                        expires_at REAL,
                        access_count INTEGER DEFAULT 0,
                        last_accessed REAL NOT NULL
                    )
                """)
                
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_expires_at ON cache_entries(expires_at)
                """)
                
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_last_accessed ON cache_entries(last_accessed)
                """)
                
                conn.commit()
        except Exception as e:
            print(f"初始化缓存数据库失败: {e}")
    
    def _generate_key(self, key: str) -> str:
        """生成缓存键"""
        if isinstance(key, str):
            return hashlib.md5(key.encode('utf-8')).hexdigest()
        else:
            return hashlib.md5(str(key).encode('utf-8')).hexdigest()
    
    def set(self, key: str, value: Any, ttl: int = None) -> bool:
        """
        设置缓存
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间（秒），None表示永不过期
            
        Returns:
            是否设置成功
        """
        try:
            cache_key = self._generate_key(key)
            
            # 序列化值
            if isinstance(value, (dict, list)):
                serialized_value = json.dumps(value, ensure_ascii=False)
            else:
                serialized_value = str(value)
            
            current_time = time.time()
            expires_at = current_time + ttl if ttl else None
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT OR REPLACE INTO cache_entries 
                    (key, value, created_at, expires_at, access_count, last_accessed)
                    VALUES (?, ?, ?, ?, 0, ?)
                """, (cache_key, serialized_value, current_time, expires_at, current_time))
                
                conn.commit()
            
            self.stats["writes"] += 1
            return True
            
        except Exception as e:
            print(f"设置缓存失败: {e}")
            return False
    
    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值，不存在或过期返回None
        """
        try:
            cache_key = self._generate_key(key)
            current_time = time.time()
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT value, expires_at, access_count 
                    FROM cache_entries 
                    WHERE key = ?
                """, (cache_key,))
                
                result = cursor.fetchone()
                
                if not result:
                    self.stats["misses"] += 1
                    return None
                
                value, expires_at, access_count = result
                
                # 检查是否过期
                if expires_at and current_time > expires_at:
                    # 删除过期缓存
                    cursor.execute("DELETE FROM cache_entries WHERE key = ?", (cache_key,))
                    conn.commit()
                    self.stats["misses"] += 1
                    return None
                
                # 更新访问统计
                cursor.execute("""
                    UPDATE cache_entries 
                    SET access_count = ?, last_accessed = ?
                    WHERE key = ?
                """, (access_count + 1, current_time, cache_key))
                
                conn.commit()
            
            # 尝试反序列化
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
            
            self.stats["hits"] += 1
            
        except Exception as e:
            print(f"获取缓存失败: {e}")
            self.stats["misses"] += 1
            return None
    
    def delete(self, key: str) -> bool:
        """
        删除缓存
        
        Args:
            key: 缓存键
            
        Returns:
            是否删除成功
        """
        try:
            cache_key = self._generate_key(key)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM cache_entries WHERE key = ?", (cache_key,))
                conn.commit()
                
                if cursor.rowcount > 0:
                    self.stats["deletes"] += 1
                    return True
                    
            return False
            
        except Exception as e:
            print(f"删除缓存失败: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """
        检查缓存是否存在
        
        Args:
            key: 缓存键
            
        Returns:
            是否存在
        """
        cache_key = self._generate_key(key)
        current_time = time.time()
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT expires_at FROM cache_entries WHERE key = ?
                """, (cache_key,))
                
                result = cursor.fetchone()
                
                if not result:
                    return False
                
                expires_at = result[0]
                if expires_at and current_time > expires_at:
                    # 删除过期缓存
                    cursor.execute("DELETE FROM cache_entries WHERE key = ?", (cache_key,))
                    conn.commit()
                    return False
                
                return True
                
        except Exception as e:
            print(f"检查缓存存在性失败: {e}")
            return False
    
    def clear_expired(self) -> int:
        """
        清理过期缓存
        
        Returns:
            清理的缓存数量
        """
        try:
            current_time = time.time()
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    DELETE FROM cache_entries 
                    WHERE expires_at IS NOT NULL AND expires_at < ?
                """, (current_time,))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                self.stats["deletes"] += deleted_count
                return deleted_count
                
        except Exception as e:
            print(f"清理过期缓存失败: {e}")
            return 0
    
    def clear_all(self) -> bool:
        """
        清理所有缓存
        
        Returns:
            是否清理成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM cache_entries")
                count = cursor.fetchone()[0]
                
                cursor.execute("DELETE FROM cache_entries")
                conn.commit()
                
                self.stats["deletes"] += count
                return True
                
        except Exception as e:
            print(f"清理所有缓存失败: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            统计信息字典
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 总缓存数量
                cursor.execute("SELECT COUNT(*) FROM cache_entries")
                total_entries = cursor.fetchone()[0]
                
                # 过期缓存数量
                current_time = time.time()
                cursor.execute("""
                    SELECT COUNT(*) FROM cache_entries 
                    WHERE expires_at IS NOT NULL AND expires_at < ?
                """, (current_time,))
                expired_entries = cursor.fetchone()[0]
                
                # 缓存大小统计
                cursor.execute("SELECT SUM(LENGTH(value)) FROM cache_entries")
                total_size = cursor.fetchone()[0] or 0
                
                # 命中率计算
                total_requests = self.stats["hits"] + self.stats["misses"]
                hit_rate = self.stats["hits"] / total_requests if total_requests > 0 else 0
                
                return {
                    "total_entries": total_entries,
                    "expired_entries": expired_entries,
                    "valid_entries": total_entries - expired_entries,
                    "total_size_bytes": total_size,
                    "hit_rate": round(hit_rate * 100, 2),
                    "stats": self.stats.copy()
                }
                
        except Exception as e:
            print(f"获取缓存统计失败: {e}")
            return {"error": str(e)}
    
    def cleanup(self, max_entries: int = None, max_age_hours: int = None):
        """
        清理缓存
        
        Args:
            max_entries: 最大缓存条目数
            max_age_hours: 最大缓存年龄（小时）
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 清理过期缓存
                self.clear_expired()
                
                # 按年龄清理
                if max_age_hours:
                    cutoff_time = time.time() - (max_age_hours * 3600)
                    cursor.execute("""
                        DELETE FROM cache_entries 
                        WHERE created_at < ?
                    """, (cutoff_time,))
                    conn.commit()
                
                # 按数量清理（保留最近访问的）
                if max_entries:
                    cursor.execute("""
                        DELETE FROM cache_entries 
                        WHERE key NOT IN (
                            SELECT key FROM cache_entries 
                            ORDER BY last_accessed DESC 
                            LIMIT ?
                        )
                    """, (max_entries,))
                    conn.commit()
                    
        except Exception as e:
            print(f"缓存清理失败: {e}")
    
    def set_with_tags(self, key: str, value: Any, tags: List[str], ttl: int = None) -> bool:
        """
        设置带标签的缓存
        
        Args:
            key: 缓存键
            value: 缓存值
            tags: 标签列表
            ttl: 过期时间
            
        Returns:
            是否设置成功
        """
        # 为简化实现，将标签存储为JSON字符串
        tagged_value = {
            "data": value,
            "tags": tags,
            "created_at": time.time()
        }
        
        return self.set(key, tagged_value, ttl)
    
    def get_by_tag(self, tag: str) -> List[Dict[str, Any]]:
        """
        根据标签获取缓存
        
        Args:
            tag: 标签
            
        Returns:
            匹配的缓存列表
        """
        try:
            results = []
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT key, value FROM cache_entries")
                
                for row in cursor.fetchall():
                    key, value_str = row
                    try:
                        value = json.loads(value_str)
                        if isinstance(value, dict) and "tags" in value:
                            if tag in value["tags"]:
                                results.append({
                                    "key": key,
                                    "data": value["data"],
                                    "tags": value["tags"]
                                })
                    except (json.JSONDecodeError, KeyError):
                        continue
            
            return results
            
        except Exception as e:
            print(f"根据标签获取缓存失败: {e}")
            return []
    
    def delete_by_tag(self, tag: str) -> int:
        """
        根据标签删除缓存
        
        Args:
            tag: 标签
            
        Returns:
            删除的缓存数量
        """
        try:
            deleted_count = 0
            keys_to_delete = []
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT key, value FROM cache_entries")
                
                for row in cursor.fetchall():
                    key, value_str = row
                    try:
                        value = json.loads(value_str)
                        if isinstance(value, dict) and "tags" in value:
                            if tag in value["tags"]:
                                keys_to_delete.append(key)
                    except (json.JSONDecodeError, KeyError):
                        continue
                
                # 删除匹配的缓存
                for key in keys_to_delete:
                    cursor.execute("DELETE FROM cache_entries WHERE key = ?", (key,))
                    deleted_count += 1
                
                conn.commit()
            
            self.stats["deletes"] += deleted_count
            return deleted_count
            
        except Exception as e:
            print(f"根据标签删除缓存失败: {e}")
            return 0
