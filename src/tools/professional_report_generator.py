"""
专业企业级金融研报生成器
支持封面、目录、详细章节、图片等企业级功能
"""

import os
import re
import json
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional, Generator
from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from io import BytesIO
import base64
from src.tools.chart_generator import ChartGenerator

class ProfessionalReportGenerator:
    """专业企业级报告生成器"""
    
    def __init__(self, config=None):
        self.config = config
        self.output_dir = "data/outputs"
        os.makedirs(self.output_dir, exist_ok=True)

        # 初始化图表生成器
        self.chart_generator = ChartGenerator()

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
    def generate_company_report(self, data: Dict[str, Any]) -> str:
        """生成专业公司研报"""
        company_name = data.get('company_name', '目标公司')
        
        # 创建Word文档
        doc = Document()
        
        # 1. 生成封面
        self._create_cover_page(doc, company_name, data)
        
        # 2. 生成目录
        self._create_table_of_contents(doc)
        
        # 3. 生成主要章节
        self._create_executive_summary(doc, data)
        self._create_company_overview(doc, data)
        self._create_financial_analysis(doc, data)
        self._create_valuation_analysis(doc, data)
        self._create_risk_assessment(doc, data)
        self._create_investment_recommendation(doc, data)
        
        # 4. 生成附录和声明
        self._create_appendix(doc, data)
        self._create_disclaimer(doc)
        
        # 保存文档
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        safe_name = re.sub(r'[^\w\s-]', '', company_name).strip()
        safe_name = re.sub(r'[-\s]+', '_', safe_name)
        
        filename = f"专业研报_{safe_name}_{timestamp}.docx"
        filepath = os.path.join(self.output_dir, filename)
        
        doc.save(filepath)
        print(f"✅ 专业研报生成完成: {filepath}")
        return filepath
    
    def _create_cover_page(self, doc: Document, company_name: str, data: Dict[str, Any]):
        """创建专业封面"""
        # 添加封面标题
        title = doc.add_heading(f'{company_name}', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        subtitle = doc.add_heading('投资研究报告', level=1)
        subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加副标题
        subtitle2 = doc.add_paragraph('EQUITY RESEARCH REPORT')
        subtitle2.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加空行
        for _ in range(5):
            doc.add_paragraph()
        
        # 添加报告信息表格
        table = doc.add_table(rows=6, cols=2)
        table.style = 'Table Grid'
        
        info_data = [
            ('研究标的', company_name),
            ('报告类型', '股票投资研究报告'),
            ('发布日期', datetime.now().strftime('%Y年%m月%d日')),
            ('分析师', 'AI研究团队'),
            ('适用对象', '专业投资者'),
            ('合规标准', 'CFA协会标准')
        ]
        
        for i, (key, value) in enumerate(info_data):
            table.cell(i, 0).text = key
            table.cell(i, 1).text = value
        
        # 添加分页符
        doc.add_page_break()
    
    def _create_table_of_contents(self, doc: Document):
        """创建目录"""
        doc.add_heading('目录', level=1)
        
        toc_items = [
            ('1. 投资要点', '3'),
            ('2. 公司概况', '5'),
            ('3. 财务分析', '8'),
            ('4. 估值分析', '12'),
            ('5. 风险提示', '15'),
            ('6. 投资建议', '17'),
            ('附录A: 财务数据详表', '19'),
            ('重要声明', '21')
        ]
        
        for item, page in toc_items:
            p = doc.add_paragraph()
            p.add_run(item).bold = True
            p.add_run(f' {"." * (50 - len(item))} {page}')
        
        doc.add_page_break()
    
    def _create_executive_summary(self, doc: Document, data: Dict[str, Any]):
        """创建投资要点章节"""
        doc.add_heading('1. 投资要点', level=1)
        
        # 核心观点
        doc.add_heading('1.1 核心观点', level=2)
        summary = data.get('executive_summary', '基于深度分析，我们对该公司给出专业投资建议。')
        doc.add_paragraph(summary)
        
        # 投资评级
        doc.add_heading('1.2 投资评级', level=2)
        rating_data = data.get('investment_rating_data', {})

        if rating_data:
            # 创建投资评级雷达图 - 仅基于真实数据
            try:
                # 从实际分析数据中提取投资要素评分
                radar_data = self._extract_investment_radar_data(data)

                if radar_data and len(radar_data) >= 3:  # 至少需要3个维度
                    radar_path = self.chart_generator.create_radar_chart(
                        radar_data,
                        f"{data.get('company_name', '目标公司')} 投资要素雷达图"
                    )

                    if radar_path and os.path.exists(radar_path):
                        doc.add_picture(radar_path, width=Inches(5))
                        doc.add_paragraph()
                else:
                    print("📊 投资雷达图需要更多真实数据支持")

            except Exception as e:
                print(f"⚠️ 投资雷达图生成失败: {e}")

            # 添加评级表格
            table = doc.add_table(rows=4, cols=2)
            table.style = 'Table Grid'

            rating_info = [
                ('投资评级', rating_data.get('rating', '待评估')),
                ('推荐度', rating_data.get('recommendation', 'HOLD')),
                ('置信水平', rating_data.get('confidence', '中等')),
                ('综合评分', f"{rating_data.get('score', 50)}/{rating_data.get('max_score', 100)}")
            ]

            for i, (key, value) in enumerate(rating_info):
                table.cell(i, 0).text = key
                table.cell(i, 1).text = str(value)
        
        doc.add_page_break()
    
    def _create_company_overview(self, doc: Document, data: Dict[str, Any]):
        """创建公司概况章节"""
        doc.add_heading('2. 公司概况', level=1)
        
        doc.add_heading('2.1 基本信息', level=2)
        overview = data.get('company_overview', '公司基本信息分析。')
        doc.add_paragraph(overview)
        
        doc.add_heading('2.2 主营业务', level=2)
        business = data.get('business_analysis', '主营业务分析。')
        doc.add_paragraph(business)

        # 添加业务构成图表 - 仅基于真实数据
        try:
            # 从业务分析中提取真实的业务构成数据
            business_segments = self._extract_business_segments(data)

            if business_segments and len(business_segments) >= 2:  # 至少需要2个业务板块
                pie_path = self.chart_generator.create_pie_chart(
                    business_segments,
                    f"{data.get('company_name', '目标公司')} 业务构成分析"
                )

                if pie_path and os.path.exists(pie_path):
                    doc.add_paragraph()
                    doc.add_picture(pie_path, width=Inches(5))
                    doc.add_paragraph()
            else:
                print("📊 业务构成图表需要更多真实数据支持")

        except Exception as e:
            print(f"⚠️ 业务构成图表生成失败: {e}")
        
        doc.add_page_break()
    
    def _create_financial_analysis(self, doc: Document, data: Dict[str, Any]):
        """创建财务分析章节"""
        doc.add_heading('3. 财务分析', level=1)
        
        doc.add_heading('3.1 财务概况', level=2)
        financial = data.get('financial_analysis', '财务分析内容。')
        doc.add_paragraph(financial)
        
        # 如果有财务数据，生成图表
        if 'financial_data_structured' in data:
            self._add_financial_charts(doc, data['financial_data_structured'])
        
        doc.add_page_break()
    
    def _create_valuation_analysis(self, doc: Document, data: Dict[str, Any]):
        """创建估值分析章节"""
        doc.add_heading('4. 估值分析', level=1)
        
        valuation = data.get('valuation_analysis', '估值分析内容。')
        doc.add_paragraph(valuation)

        # 添加估值对比图表 - 仅基于真实数据
        try:
            # 从估值分析中提取真实的估值指标
            valuation_metrics = self._extract_valuation_metrics(data)

            if valuation_metrics and len(valuation_metrics) >= 2:  # 至少需要2个估值指标
                bar_path = self.chart_generator.create_bar_chart(
                    valuation_metrics,
                    f"{data.get('company_name', '目标公司')} 估值指标分析",
                    "倍数"
                )

                if bar_path and os.path.exists(bar_path):
                    doc.add_paragraph()
                    doc.add_picture(bar_path, width=Inches(6))
                    doc.add_paragraph()
            else:
                print("📊 估值分析图表需要更多真实数据支持")

        except Exception as e:
            print(f"⚠️ 估值分析图表生成失败: {e}")
        
        doc.add_page_break()
    
    def _create_risk_assessment(self, doc: Document, data: Dict[str, Any]):
        """创建风险提示章节"""
        doc.add_heading('5. 风险提示', level=1)
        
        risks = data.get('risk_warning', '主要风险因素分析。')
        doc.add_paragraph(risks)
        
        doc.add_page_break()
    
    def _create_investment_recommendation(self, doc: Document, data: Dict[str, Any]):
        """创建投资建议章节"""
        doc.add_heading('6. 投资建议', level=1)
        
        recommendation = data.get('investment_recommendation', '投资建议内容。')
        doc.add_paragraph(recommendation)
        
        doc.add_page_break()
    
    def _create_appendix(self, doc: Document, data: Dict[str, Any]):
        """创建附录"""
        doc.add_heading('附录A: 财务数据详表', level=1)
        
        doc.add_paragraph('详细财务数据表格将在此处展示。')
        
        doc.add_page_break()
    
    def _create_disclaimer(self, doc: Document):
        """创建重要声明"""
        doc.add_heading('重要声明', level=1)
        
        disclaimer_text = """
本研究报告仅供专业投资者参考，不构成对个人投资者的投资建议。
报告中的信息来源于公开资料，我们力求准确可靠，但不保证信息的完整性和准确性。
过往业绩不代表未来表现，投资有风险，入市需谨慎。

投资者在做出投资决策前，应仔细考虑自身的投资目标、风险承受能力和财务状况。
本报告符合CFA协会职业行为标准和相关监管要求。

版权所有 © AI金融研究团队
        """
        
        doc.add_paragraph(disclaimer_text)
    
    def _add_financial_charts(self, doc: Document, financial_data: Dict[str, Any]):
        """添加财务图表"""
        try:
            # 从财务分析文本中提取数据
            financial_text = financial_data.get('financial_analysis', '')
            if financial_text:
                extracted_data = self.chart_generator.extract_financial_data_from_text(financial_text)

                # 生成财务仪表板图表
                company_name = financial_data.get('company_name', '目标公司')
                chart_paths = self.chart_generator.create_financial_dashboard(extracted_data, company_name)

                # 将图表插入到文档中
                for chart_path in chart_paths:
                    if os.path.exists(chart_path):
                        doc.add_paragraph()  # 添加空行
                        doc.add_picture(chart_path, width=Inches(6))
                        doc.add_paragraph()  # 添加空行

                if not chart_paths:
                    doc.add_paragraph('📊 财务图表将基于具体财务数据生成')
            else:
                doc.add_paragraph('📊 财务图表将基于具体财务数据生成')

        except Exception as e:
            print(f"⚠️ 添加财务图表失败: {e}")
            doc.add_paragraph('📊 财务图表生成中...')

    def learn_from_real_reports(self, web_search_tool):
        """学习真实的企业报告格式"""
        print("🎓 开始学习真实企业报告格式...")

        # 搜索真实的企业研报样本
        search_queries = [
            "中金公司 股票研究报告 PDF 格式",
            "中信证券 投资研究报告 模板",
            "华泰证券 行业研究报告 样本",
            "招商证券 公司研报 格式标准",
            "国泰君安 投资分析报告 模板",
            "申万宏源 研究报告 专业格式"
        ]

        report_formats = []

        for query in search_queries:
            try:
                print(f"🔍 搜索: {query}")
                results = web_search_tool.search(query, num_results=3)

                for result in results:
                    if any(keyword in result.get('title', '').lower() for keyword in ['研报', '研究报告', '投资分析']):
                        report_formats.append({
                            'title': result.get('title', ''),
                            'url': result.get('url', ''),
                            'snippet': result.get('snippet', ''),
                            'source': query
                        })

            except Exception as e:
                print(f"⚠️ 搜索失败: {e}")

        # 分析学习到的格式
        self._analyze_report_formats(report_formats)

        return report_formats

    def _analyze_report_formats(self, formats: List[Dict]):
        """分析学习到的报告格式"""
        print(f"📊 分析了 {len(formats)} 个报告样本")

        # 提取常见的章节结构
        common_sections = []
        for format_info in formats:
            title = format_info.get('title', '')
            snippet = format_info.get('snippet', '')

            # 分析标题和摘要中的关键词
            text = f"{title} {snippet}".lower()

            if '投资要点' in text or '核心观点' in text:
                common_sections.append('投资要点')
            if '公司概况' in text or '基本面' in text:
                common_sections.append('公司概况')
            if '财务分析' in text or '盈利能力' in text:
                common_sections.append('财务分析')
            if '估值分析' in text or '目标价' in text:
                common_sections.append('估值分析')
            if '风险提示' in text or '风险因素' in text:
                common_sections.append('风险提示')

        # 统计最常见的章节
        from collections import Counter
        section_counts = Counter(common_sections)

        print("📋 常见章节结构:")
        for section, count in section_counts.most_common(10):
            print(f"  - {section}: {count}次")

        return section_counts

    def _extract_investment_radar_data(self, data: Dict[str, Any]) -> Dict[str, float]:
        """从真实分析数据中提取投资要素评分"""
        radar_data = {}

        try:
            # 从各个分析章节中提取评分信息
            all_text = ""
            for key in ['executive_summary', 'financial_analysis', 'valuation_analysis', 'company_overview']:
                all_text += data.get(key, '') + " "

            # 使用正则表达式提取评分信息
            import re

            # 技术优势评分
            tech_match = re.search(r'技术.*?([1-5]\.?\d*)分', all_text)
            if tech_match:
                radar_data['技术优势'] = float(tech_match.group(1))

            # 市场地位评分
            market_match = re.search(r'市场.*?([1-5]\.?\d*)分', all_text)
            if market_match:
                radar_data['市场地位'] = float(market_match.group(1))

            # 财务表现评分
            finance_match = re.search(r'财务.*?([1-5]\.?\d*)分', all_text)
            if finance_match:
                radar_data['财务表现'] = float(finance_match.group(1))

            # 如果没有明确评分，尝试从描述性文字推断
            if not radar_data:
                # 基于关键词推断评分（保守估计）
                if '优秀' in all_text or '领先' in all_text:
                    radar_data['综合评价'] = 4.0
                elif '良好' in all_text or '稳定' in all_text:
                    radar_data['综合评价'] = 3.5
                elif '一般' in all_text or '平均' in all_text:
                    radar_data['综合评价'] = 3.0

        except Exception as e:
            print(f"⚠️ 投资要素数据提取失败: {e}")

        return radar_data

    def _extract_business_segments(self, data: Dict[str, Any]) -> Dict[str, float]:
        """从业务分析中提取真实的业务构成数据"""
        business_segments = {}

        try:
            business_text = data.get('business_analysis', '') + data.get('company_overview', '')

            import re

            # 提取业务板块占比 - 改进正则表达式
            segment_matches = re.findall(r'([^，。；\n]{2,10})(?:业务|板块|收入|营收).*?占比?.*?([0-9.]+)%', business_text)

            # 如果没找到，尝试另一种模式
            if not segment_matches:
                segment_matches = re.findall(r'([^，。；\n]{2,10})占比([0-9.]+)%', business_text)

            for segment, percentage in segment_matches:
                segment_name = segment.strip()
                if len(segment_name) > 1 and len(segment_name) < 20:  # 合理的业务名称长度
                    business_segments[segment_name] = float(percentage)

            # 如果没有找到占比数据，尝试提取业务名称（不生成图表）
            if not business_segments:
                business_matches = re.findall(r'主要业务包括([^。；\n]+)', business_text)
                if business_matches:
                    print("📊 找到业务信息但缺少具体占比数据，无法生成饼图")

        except Exception as e:
            print(f"⚠️ 业务构成数据提取失败: {e}")

        return business_segments

    def _extract_valuation_metrics(self, data: Dict[str, Any]) -> Dict[str, float]:
        """从估值分析中提取真实的估值指标"""
        valuation_metrics = {}

        try:
            valuation_text = data.get('valuation_analysis', '') + data.get('financial_analysis', '')

            import re

            # 提取常见估值指标
            pe_match = re.search(r'P/E.*?([0-9.]+)', valuation_text)
            if pe_match:
                valuation_metrics['P/E'] = float(pe_match.group(1))

            pb_match = re.search(r'P/B.*?([0-9.]+)', valuation_text)
            if pb_match:
                valuation_metrics['P/B'] = float(pb_match.group(1))

            ev_ebitda_match = re.search(r'EV/EBITDA.*?([0-9.]+)', valuation_text)
            if ev_ebitda_match:
                valuation_metrics['EV/EBITDA'] = float(ev_ebitda_match.group(1))

            peg_match = re.search(r'PEG.*?([0-9.]+)', valuation_text)
            if peg_match:
                valuation_metrics['PEG'] = float(peg_match.group(1))

            # 提取市盈率的其他表达方式
            pe_alt_match = re.search(r'市盈率.*?([0-9.]+)', valuation_text)
            if pe_alt_match and 'P/E' not in valuation_metrics:
                valuation_metrics['市盈率'] = float(pe_alt_match.group(1))

        except Exception as e:
            print(f"⚠️ 估值指标数据提取失败: {e}")

        return valuation_metrics
