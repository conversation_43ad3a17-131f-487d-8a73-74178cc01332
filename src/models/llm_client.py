import asyncio
import json
import sys
import time
import logging
from typing import Dict, List, Any, Optional, AsyncGenerator
from openai import OpenAI, AsyncOpenAI
from config.settings import Config

class LLMClient:
    """OpenAI v1.0+ 兼容的LLM客户端"""
    
    def __init__(self, config: Config = None):
        self.config = config or Config()

        # 设置日志
        self.logger = logging.getLogger(self.__class__.__name__)

        # 重试配置 - 修复超时问题
        self.max_retries = 3   # 减少重试次数，避免过度重试
        self.base_delay = 2    # 增加基础延迟
        self.timeout = 120     # 大幅增加超时时间到2分钟

        # 初始化同步客户端
        self.client = OpenAI(
            api_key=self.config.OPENAI_API_KEY,
            base_url=self.config.OPENAI_BASE_URL,
            timeout=self.timeout
        )

        # 初始化异步客户端 - 简化版
        self.async_client = AsyncOpenAI(
            api_key=self.config.OPENAI_API_KEY,
            base_url=self.config.OPENAI_BASE_URL,
            timeout=self.timeout
        )
    
    def chat_completion(self, messages: List[Dict[str, str]],
                       model: str = None, tools: List[Dict] = None, **kwargs) -> Dict[str, Any]:
        """同步聊天完成，支持function calling，带重试机制"""
        model_name = model or self.config.OPENAI_MODEL

        for attempt in range(self.max_retries):
            try:
                self.logger.info(f"🤖 LLM调用开始 (尝试 {attempt + 1}/{self.max_retries}) - 模型: {model_name}")
                start_time = time.time()

                # 构建请求参数
                request_params = {
                    "model": model_name,
                    "messages": messages,
                    "temperature": kwargs.get('temperature', 0.5),
                    "max_tokens": min(kwargs.get('max_tokens', 4096), 8192),
                }

                # 添加tools参数（如果提供）
                if tools:
                    request_params["tools"] = tools
                    request_params["tool_choice"] = kwargs.get('tool_choice', 'auto')
                    self.logger.info(f"🔧 启用工具调用，工具数量: {len(tools)}")

                # 对于qwen模型，添加特殊配置
                if "qwen" in model_name.lower():
                    request_params["extra_body"] = {"enable_thinking": False}

                self.logger.info(f"📤 发送请求到LLM服务器...")
                response = self.client.chat.completions.create(**request_params)

                elapsed_time = time.time() - start_time
                self.logger.info(f"✅ LLM响应成功 (耗时: {elapsed_time:.2f}秒)")

                # 解析响应
                message = response.choices[0].message
                result = {
                    "content": message.content,
                    "usage": response.usage.model_dump() if response.usage else None,
                    "model": response.model,
                    "success": True,
                    "finish_reason": response.choices[0].finish_reason
                }

                # 如果有工具调用，添加到结果中
                if hasattr(message, 'tool_calls') and message.tool_calls:
                    result["tool_calls"] = []
                    for tool_call in message.tool_calls:
                        result["tool_calls"].append({
                            "id": tool_call.id,
                            "type": tool_call.type,
                            "function": {
                                "name": tool_call.function.name,
                                "arguments": tool_call.function.arguments
                            }
                        })
                    self.logger.info(f"🔧 检测到工具调用: {len(result['tool_calls'])}个")

                # 记录token使用情况
                if result.get("usage"):
                    usage = result["usage"]
                    self.logger.info(f"📊 Token使用: 输入={usage.get('prompt_tokens', 0)}, 输出={usage.get('completion_tokens', 0)}, 总计={usage.get('total_tokens', 0)}")

                return result

            except Exception as e:
                error_msg = str(e)
                self.logger.error(f"❌ LLM调用失败 (尝试 {attempt + 1}/{self.max_retries}): {error_msg}")

                # 如果是最后一次尝试，返回错误
                if attempt == self.max_retries - 1:
                    return {
                        "content": f"LLM调用失败: {error_msg}",
                        "error": error_msg,
                        "success": False
                    }

                # 计算重试延迟（指数退避）
                delay = self.base_delay * (2 ** attempt)
                self.logger.warning(f"⏳ {delay}秒后重试...")
                time.sleep(delay)
    
    async def async_chat_completion(self, messages: List[Dict[str, str]],
                                   model: str = None, tools: List[Dict] = None, **kwargs) -> Dict[str, Any]:
        """异步聊天完成 - 使用同步调用包装，避免异步问题"""
        import asyncio
        import concurrent.futures

        # 使用线程池执行同步调用，这样更稳定
        loop = asyncio.get_event_loop()
        with concurrent.futures.ThreadPoolExecutor() as executor:
            result = await loop.run_in_executor(
                executor,
                self.chat_completion,
                messages,
                model,
                tools,
                **kwargs
            )

        return result

    async def async_chat_completion_with_tools(self, messages: List[Dict[str, str]],
                                             tools: List[Dict], tool_functions: Dict[str, callable],
                                             model: str = None, max_iterations: int = 5, **kwargs) -> Dict[str, Any]:
        """异步聊天完成，自动处理工具调用循环"""
        current_messages = messages.copy()
        iteration = 0

        while iteration < max_iterations:
            # 调用LLM
            response = await self.async_chat_completion(
                messages=current_messages,
                model=model,
                tools=tools,
                **kwargs
            )

            if not response.get("success"):
                return response

            # 检查是否有工具调用
            if not response.get("tool_calls"):
                # 没有工具调用，返回最终结果
                return response

            # 添加assistant消息到对话历史
            assistant_message = {
                "role": "assistant",
                "content": response.get("content", ""),
            }

            # 如果有工具调用，添加tool_calls
            if response.get("tool_calls"):
                assistant_message["tool_calls"] = response["tool_calls"]

            current_messages.append(assistant_message)

            # 执行工具调用
            for tool_call in response["tool_calls"]:
                function_name = tool_call["function"]["name"]
                function_args = tool_call["function"]["arguments"]

                if function_name in tool_functions:
                    try:
                        # 解析参数
                        import json
                        args = json.loads(function_args)

                        # 调用工具函数
                        if asyncio.iscoroutinefunction(tool_functions[function_name]):
                            tool_result = await tool_functions[function_name](**args)
                        else:
                            tool_result = tool_functions[function_name](**args)

                        # 添加工具结果到对话历史
                        tool_message = {
                            "role": "tool",
                            "content": str(tool_result),
                            "tool_call_id": tool_call["id"]
                        }
                        current_messages.append(tool_message)

                    except Exception as e:
                        # 工具调用失败
                        error_message = {
                            "role": "tool",
                            "content": f"工具调用失败: {str(e)}",
                            "tool_call_id": tool_call["id"]
                        }
                        current_messages.append(error_message)
                else:
                    # 未知工具
                    error_message = {
                        "role": "tool",
                        "content": f"未知工具: {function_name}",
                        "tool_call_id": tool_call["id"]
                    }
                    current_messages.append(error_message)

            iteration += 1

        # 达到最大迭代次数
        return {
            "content": "达到最大工具调用迭代次数",
            "success": False,
            "error": "max_iterations_reached",
            "messages": current_messages
        }

    async def async_chat_completion_stream(self, messages: List[Dict[str, str]],
                                         model: str = None, tools: List[Dict] = None,
                                         show_thinking: bool = False, **kwargs) -> AsyncGenerator[str, None]:
        """异步流式聊天完成"""
        try:
            # 构建请求参数
            request_params = {
                "model": model or self.config.OPENAI_MODEL,
                "messages": messages,
                "temperature": kwargs.get('temperature', 0.5),
                "max_tokens": min(kwargs.get('max_tokens', 4096), 8192),
                "stream": True,
            }

            # 添加tools参数（如果提供）
            if tools:
                request_params["tools"] = tools
                request_params["tool_choice"] = kwargs.get('tool_choice', 'auto')

            # 对于qwen模型，添加特殊配置
            if "qwen" in (model or self.config.OPENAI_MODEL).lower():
                request_params["extra_body"] = {"enable_thinking": show_thinking}

            # 创建流式响应
            stream = await self.async_client.chat.completions.create(**request_params)

            # 处理流式响应
            full_content = ""
            async for chunk in stream:
                if chunk.choices and len(chunk.choices) > 0:
                    delta = chunk.choices[0].delta

                    if hasattr(delta, 'content') and delta.content:
                        content_chunk = delta.content
                        full_content += content_chunk
                        yield content_chunk

                    # 处理工具调用（如果有）
                    if hasattr(delta, 'tool_calls') and delta.tool_calls:
                        for tool_call in delta.tool_calls:
                            if tool_call.function and tool_call.function.name:
                                yield f"\n🔧 调用工具: {tool_call.function.name}\n"
                            if tool_call.function and tool_call.function.arguments:
                                yield f"参数: {tool_call.function.arguments}\n"

        except Exception as e:
            yield f"\n❌ 流式输出错误: {str(e)}\n"

    async def async_chat_completion_with_tools_stream(self, messages: List[Dict[str, str]],
                                                    tools: List[Dict], tool_functions: Dict[str, callable],
                                                    model: str = None, max_iterations: int = 5,
                                                    show_thinking: bool = False, **kwargs) -> AsyncGenerator[str, None]:
        """异步流式聊天完成，自动处理工具调用循环"""
        current_messages = messages.copy()
        iteration = 0

        while iteration < max_iterations:
            yield f"\n💭 第 {iteration + 1} 轮对话开始...\n"

            # 使用非流式调用来避免工具调用解析问题
            try:
                self.logger.info(f"💭 开始第 {iteration + 1} 轮对话...")
                response = await self.async_chat_completion(
                    messages=current_messages,
                    model=model,
                    tools=tools,
                    **kwargs
                )

                if not response.get("success"):
                    error_msg = response.get('error', 'Unknown error')
                    self.logger.error(f"❌ 第 {iteration + 1} 轮LLM调用失败: {error_msg}")
                    yield f"\n❌ LLM调用失败: {error_msg}\n"
                    return

                # 输出响应内容
                content = response.get("content", "")
                if content:
                    yield content

                # 检查是否有工具调用
                tool_calls = response.get("tool_calls", [])
                if not tool_calls:
                    self.logger.info(f"✅ 第 {iteration + 1} 轮对话完成，无工具调用")
                    yield f"\n\n✅ 对话完成！\n"
                    return

                self.logger.info(f"🔧 第 {iteration + 1} 轮检测到 {len(tool_calls)} 个工具调用")

                # 添加assistant消息到对话历史
                assistant_message = {
                    "role": "assistant",
                    "content": content,
                }

                if tool_calls:
                    assistant_message["tool_calls"] = tool_calls

                current_messages.append(assistant_message)

                # 执行工具调用
                for tool_call in tool_calls:
                    function_name = tool_call["function"]["name"]
                    function_args = tool_call["function"]["arguments"]

                    self.logger.info(f"🔧 执行工具: {function_name}")
                    yield f"\n\n🔧 执行工具: {function_name}\n"

                    if function_name in tool_functions:
                        try:
                            # 解析参数
                            if isinstance(function_args, str):
                                args = json.loads(function_args)
                            else:
                                args = function_args

                            yield f"📝 参数: {args}\n"
                            self.logger.info(f"📝 工具参数: {args}")

                            # 调用工具函数
                            tool_start_time = time.time()
                            if asyncio.iscoroutinefunction(tool_functions[function_name]):
                                tool_result = await tool_functions[function_name](**args)
                            else:
                                tool_result = tool_functions[function_name](**args)

                            tool_elapsed = time.time() - tool_start_time
                            self.logger.info(f"✅ 工具 {function_name} 执行完成 (耗时: {tool_elapsed:.2f}秒)")
                            yield f"✅ 工具执行结果:\n{tool_result}\n"

                            # 添加工具结果到对话历史
                            tool_message = {
                                "role": "tool",
                                "content": str(tool_result),
                                "tool_call_id": tool_call["id"]
                            }
                            current_messages.append(tool_message)

                        except json.JSONDecodeError as e:
                            error_msg = f"❌ 参数解析失败: {str(e)}"
                            yield f"{error_msg}\n"

                            error_message = {
                                "role": "tool",
                                "content": error_msg,
                                "tool_call_id": tool_call["id"]
                            }
                            current_messages.append(error_message)

                        except Exception as e:
                            error_msg = f"❌ 工具调用失败: {str(e)}"
                            yield f"{error_msg}\n"

                            error_message = {
                                "role": "tool",
                                "content": error_msg,
                                "tool_call_id": tool_call["id"]
                            }
                            current_messages.append(error_message)
                    else:
                        error_msg = f"❌ 未知工具: {function_name}"
                        yield f"{error_msg}\n"

                        error_message = {
                            "role": "tool",
                            "content": error_msg,
                            "tool_call_id": tool_call["id"]
                        }
                        current_messages.append(error_message)

            except Exception as e:
                error_msg = str(e)
                self.logger.error(f"❌ 第 {iteration + 1} 轮对话异常: {error_msg}")
                yield f"\n❌ LLM调用失败: {error_msg}\n"
                return

            iteration += 1

        self.logger.warning(f"⚠️ 达到最大迭代次数 ({max_iterations})，对话结束")
        yield f"\n⚠️ 达到最大迭代次数 ({max_iterations})，对话结束\n"
