"""
专业图表生成器
支持雷达图、柱状图、饼图、折线图等多种图表类型
基于真实数据生成高质量的金融分析图表
"""

import os
import re
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Circle
import seaborn as sns
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ChartGenerator:
    """专业图表生成器"""
    
    def __init__(self, output_dir: str = "data/outputs/charts"):
        self.output_dir = output_dir
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 设置专业配色方案
        self.colors = {
            'primary': '#1f77b4',
            'secondary': '#ff7f0e', 
            'success': '#2ca02c',
            'danger': '#d62728',
            'warning': '#ff7f0e',
            'info': '#17a2b8',
            'light': '#f8f9fa',
            'dark': '#343a40'
        }
        
        # 专业色彩调色板
        self.palette = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
                       '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
    
    def create_radar_chart(self, data: Dict[str, float], title: str = "投资要素雷达图", 
                          max_score: float = 5.0) -> str:
        """创建雷达图"""
        try:
            # 准备数据
            categories = list(data.keys())
            values = list(data.values())
            
            # 确保数据完整性
            if len(categories) < 3:
                return None
            
            # 创建角度
            angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
            values += values[:1]  # 闭合图形
            angles += angles[:1]
            
            # 创建图表
            fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(projection='polar'))
            
            # 绘制雷达图
            ax.plot(angles, values, 'o-', linewidth=2, color=self.colors['primary'])
            ax.fill(angles, values, alpha=0.25, color=self.colors['primary'])
            
            # 设置标签
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(categories, fontsize=12)
            
            # 设置刻度
            ax.set_ylim(0, max_score)
            ax.set_yticks(np.arange(0, max_score + 1, 1))
            ax.set_yticklabels([f'{i}' for i in range(int(max_score) + 1)], fontsize=10)
            
            # 添加网格
            ax.grid(True, alpha=0.3)
            
            # 设置标题
            plt.title(title, size=16, fontweight='bold', pad=20)
            
            # 保存图表
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"radar_chart_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)
            
            plt.tight_layout()
            plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            print(f"✅ 雷达图生成完成: {filepath}")
            return filepath
            
        except Exception as e:
            print(f"❌ 雷达图生成失败: {e}")
            return None
    
    def create_bar_chart(self, data: Dict[str, float], title: str = "财务指标对比", 
                        ylabel: str = "数值") -> str:
        """创建柱状图"""
        try:
            # 准备数据
            categories = list(data.keys())
            values = list(data.values())
            
            if len(categories) == 0:
                return None
            
            # 创建图表
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # 创建柱状图
            bars = ax.bar(categories, values, color=self.palette[:len(categories)], 
                         alpha=0.8, edgecolor='black', linewidth=0.5)
            
            # 添加数值标签
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + max(values)*0.01,
                       f'{value:.2f}', ha='center', va='bottom', fontsize=10)
            
            # 设置标题和标签
            ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
            ax.set_ylabel(ylabel, fontsize=12)
            ax.set_xlabel('指标', fontsize=12)
            
            # 美化图表
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.grid(axis='y', alpha=0.3)
            
            # 旋转x轴标签
            plt.xticks(rotation=45, ha='right')
            
            # 保存图表
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"bar_chart_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)
            
            plt.tight_layout()
            plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            print(f"✅ 柱状图生成完成: {filepath}")
            return filepath
            
        except Exception as e:
            print(f"❌ 柱状图生成失败: {e}")
            return None
    
    def create_pie_chart(self, data: Dict[str, float], title: str = "业务构成分析") -> str:
        """创建饼图"""
        try:
            # 准备数据
            labels = list(data.keys())
            sizes = list(data.values())
            
            if len(labels) == 0:
                return None
            
            # 创建图表
            fig, ax = plt.subplots(figsize=(10, 8))
            
            # 创建饼图
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, autopct='%1.1f%%',
                                             colors=self.palette[:len(labels)],
                                             startangle=90, explode=[0.05]*len(labels))
            
            # 美化文本
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
            
            # 设置标题
            ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
            
            # 保存图表
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"pie_chart_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)
            
            plt.tight_layout()
            plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            print(f"✅ 饼图生成完成: {filepath}")
            return filepath
            
        except Exception as e:
            print(f"❌ 饼图生成失败: {e}")
            return None
    
    def create_line_chart(self, data: Dict[str, List[float]], title: str = "趋势分析", 
                         xlabel: str = "时间", ylabel: str = "数值") -> str:
        """创建折线图"""
        try:
            if not data:
                return None
            
            # 创建图表
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # 绘制多条线
            for i, (label, values) in enumerate(data.items()):
                x = range(len(values))
                ax.plot(x, values, marker='o', linewidth=2, 
                       color=self.palette[i % len(self.palette)], label=label)
            
            # 设置标题和标签
            ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
            ax.set_xlabel(xlabel, fontsize=12)
            ax.set_ylabel(ylabel, fontsize=12)
            
            # 添加图例
            ax.legend(loc='best')
            
            # 美化图表
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.grid(True, alpha=0.3)
            
            # 保存图表
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"line_chart_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)
            
            plt.tight_layout()
            plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            print(f"✅ 折线图生成完成: {filepath}")
            return filepath
            
        except Exception as e:
            print(f"❌ 折线图生成失败: {e}")
            return None
    
    def create_financial_dashboard(self, financial_data: Dict[str, Any], 
                                  company_name: str) -> List[str]:
        """创建财务分析仪表板（多个图表）"""
        chart_paths = []
        
        try:
            # 1. 创建财务指标雷达图
            if 'ratios' in financial_data:
                ratios = financial_data['ratios']
                radar_data = {}
                
                # 标准化财务比率到0-5分
                if 'roe' in ratios:
                    radar_data['ROE'] = min(float(ratios['roe']) / 5, 5.0)  # ROE/5作为评分
                if 'roa' in ratios:
                    radar_data['ROA'] = min(float(ratios['roa']) / 3, 5.0)  # ROA/3作为评分
                if 'gross_margin' in ratios:
                    radar_data['毛利率'] = min(float(ratios['gross_margin']) / 20, 5.0)
                if 'net_margin' in ratios:
                    radar_data['净利率'] = min(float(ratios['net_margin']) / 10, 5.0)
                if 'debt_ratio' in ratios:
                    radar_data['资产负债率'] = max(5 - float(ratios['debt_ratio']) / 20, 0)  # 负债率越低越好
                
                if radar_data:
                    radar_path = self.create_radar_chart(radar_data, f"{company_name} 财务健康度雷达图")
                    if radar_path:
                        chart_paths.append(radar_path)
            
            # 2. 创建收入趋势图
            if 'revenue_trend' in financial_data:
                trend_data = financial_data['revenue_trend']
                if isinstance(trend_data, dict):
                    line_path = self.create_line_chart(trend_data, f"{company_name} 收入趋势分析", "年份", "收入(亿元)")
                    if line_path:
                        chart_paths.append(line_path)
            
            # 3. 创建业务构成饼图
            if 'business_segments' in financial_data:
                segments = financial_data['business_segments']
                if isinstance(segments, dict):
                    pie_path = self.create_pie_chart(segments, f"{company_name} 业务构成分析")
                    if pie_path:
                        chart_paths.append(pie_path)
            
            # 4. 创建关键指标柱状图
            if 'key_metrics' in financial_data:
                metrics = financial_data['key_metrics']
                if isinstance(metrics, dict):
                    bar_path = self.create_bar_chart(metrics, f"{company_name} 关键财务指标", "数值")
                    if bar_path:
                        chart_paths.append(bar_path)
            
        except Exception as e:
            print(f"❌ 财务仪表板生成失败: {e}")
        
        return chart_paths
    
    def extract_financial_data_from_text(self, text: str) -> Dict[str, Any]:
        """从文本中提取财务数据用于图表生成"""
        financial_data = {}

        try:
            # 提取财务比率
            ratios = {}

            # 使用正则表达式提取常见财务指标
            roe_match = re.search(r'ROE[：:]\s*([0-9.]+)%?', text)
            if roe_match:
                ratios['roe'] = float(roe_match.group(1))

            roa_match = re.search(r'ROA[：:]\s*([0-9.]+)%?', text)
            if roa_match:
                ratios['roa'] = float(roa_match.group(1))

            margin_match = re.search(r'毛利率[：:]\s*([0-9.]+)%?', text)
            if margin_match:
                ratios['gross_margin'] = float(margin_match.group(1))

            net_margin_match = re.search(r'净利率[：:]\s*([0-9.]+)%?', text)
            if net_margin_match:
                ratios['net_margin'] = float(net_margin_match.group(1))

            # 提取更多财务指标
            debt_match = re.search(r'资产负债率[：:]\s*([0-9.]+)%?', text)
            if debt_match:
                ratios['debt_ratio'] = float(debt_match.group(1))

            current_ratio_match = re.search(r'流动比率[：:]\s*([0-9.]+)', text)
            if current_ratio_match:
                ratios['current_ratio'] = float(current_ratio_match.group(1))

            if ratios:
                financial_data['ratios'] = ratios

            # 提取收入数据
            revenue_matches = re.findall(r'(\d{4})年.*?收入.*?([0-9.]+)亿', text)
            if revenue_matches:
                revenue_trend = {}
                for year, amount in revenue_matches:
                    revenue_trend[year] = [float(amount)]
                financial_data['revenue_trend'] = revenue_trend

            # 提取业务构成数据
            business_matches = re.findall(r'([^，。；]+)业务.*?占比.*?([0-9.]+)%', text)
            if business_matches:
                business_segments = {}
                for business, percentage in business_matches:
                    business_segments[business.strip()] = float(percentage)
                financial_data['business_segments'] = business_segments

            # 提取关键指标
            key_metrics = {}

            # 营收增长率
            growth_match = re.search(r'营收增长率[：:]\s*([0-9.-]+)%?', text)
            if growth_match:
                key_metrics['营收增长率'] = float(growth_match.group(1))

            # 净利润增长率
            profit_growth_match = re.search(r'净利润增长率[：:]\s*([0-9.-]+)%?', text)
            if profit_growth_match:
                key_metrics['净利润增长率'] = float(profit_growth_match.group(1))

            # 市盈率
            pe_match = re.search(r'市盈率[：:]\s*([0-9.]+)', text)
            if pe_match:
                key_metrics['市盈率'] = float(pe_match.group(1))

            if key_metrics:
                financial_data['key_metrics'] = key_metrics

        except Exception as e:
            print(f"⚠️ 财务数据提取失败: {e}")

        return financial_data
