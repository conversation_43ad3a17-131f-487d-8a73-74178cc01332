#!/usr/bin/env python3
"""
全面修复脚本：彻底清理所有asyncio问题
"""

import os
import re
import glob
import shutil

def backup_file(file_path):
    """备份文件"""
    backup_path = f"{file_path}.backup"
    shutil.copy2(file_path, backup_path)
    return backup_path

def comprehensive_fix_file(file_path):
    """全面修复单个文件"""
    print(f"🔧 全面修复文件: {file_path}")
    
    # 备份文件
    backup_path = backup_file(file_path)
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 1. 删除所有asyncio相关的import
    content = re.sub(r'import asyncio\n?', '', content)
    content = re.sub(r'from asyncio import.*?\n', '', content)
    content = re.sub(r', AsyncGenerator', '', content)
    content = re.sub(r'AsyncGenerator, ', '', content)
    content = re.sub(r'AsyncGenerator', 'Generator', content)
    content = re.sub(r'from openai import OpenAI, AsyncOpenAI', 'from openai import OpenAI', content)
    
    # 2. 修复async def -> def
    content = re.sub(r'async def ', 'def ', content)
    
    # 3. 修复await调用
    content = re.sub(r'await ', '', content)
    
    # 4. 修复async for -> for
    content = re.sub(r'async for ', 'for ', content)
    
    # 5. 修复asyncio.sleep -> time.sleep
    content = re.sub(r'asyncio\.sleep\(', 'time.sleep(', content)
    
    # 6. 修复AsyncOpenAI相关
    content = re.sub(r'AsyncOpenAI\(', 'OpenAI(', content)
    content = re.sub(r'self\.async_client', 'self.client', content)
    
    # 7. 修复async_chat_completion调用
    content = re.sub(r'self\.llm_client\.async_chat_completion', 'self.llm_client.chat_completion', content)
    content = re.sub(r'async_chat_completion_with_tools_stream', 'chat_completion_with_tools_stream', content)
    
    # 8. 修复复杂的asyncio.iscoroutinefunction模式
    patterns_to_fix = [
        (r'if asyncio\.iscoroutinefunction\(tool_functions\[function_name\]\):\s*tool_result = await tool_functions\[function_name\]\(\*\*args\)\s*else:\s*tool_result = tool_functions\[function_name\]\(\*\*args\)', 
         'tool_result = tool_functions[function_name](**args)'),
        
        (r'if asyncio\.iscoroutinefunction\([^)]+\):\s*[^=]+ = await [^(]+\([^)]*\)\s*else:\s*[^=]+ = [^(]+\([^)]*\)', 
         lambda m: m.group(0).split('else:')[-1].strip() if 'else:' in m.group(0) else m.group(0)),
    ]
    
    for pattern, replacement in patterns_to_fix:
        if callable(replacement):
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
        else:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
    
    # 9. 修复Generator类型注解
    content = re.sub(r'-> AsyncGenerator\[str, None\]', '-> Generator[str, None, None]', content)
    content = re.sub(r': AsyncGenerator\[str, None\]', ': Generator[str, None, None]', content)
    
    # 10. 修复yield from async
    content = re.sub(r'yield from await ', 'yield from ', content)
    
    # 11. 修复async with
    content = re.sub(r'async with ', 'with ', content)
    
    # 12. 删除不必要的import
    content = re.sub(r'import concurrent\.futures\n?', '', content)
    
    # 13. 修复run_in_executor模式
    content = re.sub(r'loop = asyncio\.get_event_loop\(\)\s*with concurrent\.futures\.ThreadPoolExecutor\(\) as executor:\s*result = await loop\.run_in_executor\(\s*executor,\s*([^,]+),\s*([^)]+)\s*\)', 
                    r'result = \1(\2)', content, flags=re.MULTILINE | re.DOTALL)
    
    # 14. 确保有必要的import
    if 'time.sleep(' in content and 'import time' not in content:
        # 在其他import后添加time import
        import_match = re.search(r'(import [^\n]+\n)', content)
        if import_match:
            content = content.replace(import_match.group(1), import_match.group(1) + 'import time\n')
    
    if 'Generator' in content and 'from typing import' in content:
        typing_line = re.search(r'from typing import ([^\n]+)', content)
        if typing_line and 'Generator' not in typing_line.group(1):
            content = re.sub(r'from typing import ([^\n]+)', r'from typing import \1, Generator', content)
    
    # 15. 特殊修复：LLM客户端中的重复方法定义
    if 'llm_client.py' in file_path:
        # 删除重复的chat_completion方法定义
        lines = content.split('\n')
        new_lines = []
        skip_until_next_def = False
        
        for i, line in enumerate(lines):
            if skip_until_next_def:
                if line.strip().startswith('def ') and 'chat_completion(' not in line:
                    skip_until_next_def = False
                    new_lines.append(line)
                elif line.strip().startswith('def ') and 'chat_completion(' in line:
                    # 检查是否是重复定义
                    if any('def chat_completion(' in prev_line for prev_line in lines[:i]):
                        continue  # 跳过重复定义
                    else:
                        skip_until_next_def = False
                        new_lines.append(line)
                else:
                    continue
            else:
                new_lines.append(line)
                # 检查是否遇到重复的chat_completion定义
                if (line.strip().startswith('def chat_completion(') and 
                    any('def chat_completion(' in prev_line for prev_line in lines[:i])):
                    skip_until_next_def = True
                    new_lines.pop()  # 移除刚添加的重复行
        
        content = '\n'.join(new_lines)
    
    # 16. 修复main.py中的await调用
    if 'main.py' in file_path:
        content = re.sub(r'report_path = await self\.company_agent\.run', 'report_path = self.company_agent.run', content)
        content = re.sub(r'report_path = await self\.industry_agent\.run', 'report_path = self.industry_agent.run', content)
        content = re.sub(r'report_path = await self\.macro_agent\.run', 'report_path = self.macro_agent.run', content)
        content = re.sub(r'report_path = await system\.generate_single_report', 'report_path = system.generate_single_report', content)
        content = re.sub(r'async def main\(\):', 'def main():', content)
        content = re.sub(r'asyncio\.run\(main\(\)\)', 'main()', content)
    
    # 检查是否有修改
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 全面修复完成: {file_path}")
        return True
    else:
        print(f"⏭️  无需修改: {file_path}")
        os.remove(backup_path)  # 删除不必要的备份
        return False

def main():
    """主函数"""
    print("🚀 开始全面修复所有asyncio问题...")
    
    # 要修复的文件模式
    patterns = [
        'src/agents/*.py',
        'src/models/*.py',
        'src/tools/*.py',
        'main.py'
    ]
    
    fixed_count = 0
    total_count = 0
    
    for pattern in patterns:
        files = glob.glob(pattern)
        for file_path in files:
            if os.path.isfile(file_path):
                total_count += 1
                if comprehensive_fix_file(file_path):
                    fixed_count += 1
    
    print(f"\n📊 全面修复完成统计:")
    print(f"总文件数: {total_count}")
    print(f"修复文件数: {fixed_count}")
    print(f"无需修改: {total_count - fixed_count}")
    
    print("\n🎉 全面修复完成！所有asyncio问题已彻底清理！")
    print("💾 原文件已备份为 .backup 文件")

if __name__ == "__main__":
    main()
