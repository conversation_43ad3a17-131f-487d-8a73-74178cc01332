from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT
import matplotlib.pyplot as plt
import pandas as pd
import io
import base64
from datetime import datetime
import os
from typing import Dict, Any
from config.settings import Config

class EnterpriseDocxGenerator:
    """企业级DOCX文档生成工具"""
    
    def __init__(self, config: Config = None):
        self.config = config or Config()
        self.output_dir = self.config.REPORT_OUTPUT_DIR
        os.makedirs(self.output_dir, exist_ok=True)
    
    def create_company_report(self, data: Dict[str, Any]) -> str:
        """生成公司研究报告 - 使用Markdown转DOCX的快速方式"""
        return self._create_report_via_markdown("company", data)

    
    def create_industry_report(self, data: Dict[str, Any]) -> str:
        """生成行业研究报告 - 使用Markdown转DOCX的快速方式"""
        return self._create_report_via_markdown("industry", data)

    def create_macro_report(self, data: Dict[str, Any]) -> str:
        """生成宏观策略报告 - 使用Markdown转DOCX的快速方式"""
        return self._create_report_via_markdown("macro", data)

    def _create_report_via_markdown(self, report_type: str, data: Dict[str, Any]) -> str:
        """使用Markdown转DOCX的快速方式生成报告"""
        try:
            import pypandoc
        except ImportError:
            # 如果没有pypandoc，回退到原始方法
            return self._create_report_fallback(report_type, data)

        # 1. 生成Markdown内容
        markdown_content = self._generate_markdown_report(report_type, data)

        # 2. 保存Markdown文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        md_filename = f"{report_type.title()}_Report_{timestamp}.md"
        md_filepath = os.path.join(self.output_dir, md_filename)

        with open(md_filepath, 'w', encoding='utf-8') as f:
            f.write(markdown_content)

        # 3. 转换为DOCX
        docx_filename = f"{report_type.title()}_Report_{timestamp}.docx"
        docx_filepath = os.path.join(self.output_dir, docx_filename)

        try:
            # 使用pypandoc转换
            pypandoc.convert_file(
                md_filepath,
                'docx',
                outputfile=docx_filepath,
                extra_args=['--reference-doc=template.docx'] if os.path.exists('template.docx') else []
            )

            # 删除临时Markdown文件
            os.remove(md_filepath)

            return docx_filepath

        except Exception as e:
            print(f"Pandoc转换失败: {e}")
            # 回退到原始方法
            return self._create_report_fallback(report_type, data)

    def _generate_markdown_report(self, report_type: str, data: Dict[str, Any]) -> str:
        """生成Markdown格式的报告内容，包含图片和表格"""
        subject_name = data.get('company_name') or data.get('industry_name') or data.get('topic', '研究对象')

        # 报告标题映射
        title_map = {
            'company': '公司研究报告',
            'industry': '行业研究报告',
            'macro': '宏观策略报告'
        }

        report_title = title_map.get(report_type, '研究报告')

        # 生成Markdown内容
        markdown = f"""# {subject_name}{report_title}

## 报告信息
- **报告日期**: {datetime.now().strftime('%Y年%m月%d日')}
- **分析师**: AI智能分析系统
- **报告类型**: {report_title}

---

## 免责声明
本报告仅供参考，不构成投资建议。投资有风险，入市需谨慎。本报告中的信息均来源于公开资料，本机构对这些信息的准确性、完整性或可靠性不作任何保证。

---

"""

        # 根据报告类型添加相应章节
        if report_type == 'company':
            markdown += self._generate_company_sections(data)
        elif report_type == 'industry':
            markdown += self._generate_industry_sections(data)
        elif report_type == 'macro':
            markdown += self._generate_macro_sections(data)

        return markdown

    def _generate_company_sections(self, data: Dict[str, Any]) -> str:
        """生成公司报告的Markdown章节，包含图片和表格"""
        sections = []

        # 投资要点
        investment_points = data.get('投资要点', '基于深入分析，该公司具有良好的投资价值。')
        sections.append(f"""## 投资要点

{investment_points}

{self._add_section_images(data, '投资要点')}

""")

        # 公司概况
        company_overview = data.get('公司概况', '公司在行业中具有重要地位，发展前景良好。')
        sections.append(f"""## 公司概况

{company_overview}

{self._convert_json_to_markdown_table(data, '公司概况')}

{self._add_section_images(data, '公司概况')}

""")

        # 主营业务分析
        business_analysis = data.get('主营业务分析', '公司主营业务发展稳健，盈利能力良好。')
        sections.append(f"""## 主营业务分析

{business_analysis}

{self._convert_json_to_markdown_table(data, '主营业务分析')}

{self._add_section_images(data, '主营业务分析')}

""")

        # 财务分析
        financial_analysis = data.get('财务分析', '公司财务状况良好，各项指标表现稳定。')
        sections.append(f"""## 财务分析

{financial_analysis}

{self._convert_json_to_markdown_table(data, '财务分析')}

{self._add_section_images(data, '财务分析')}

""")

        # 估值分析
        valuation_analysis = data.get('估值分析', '基于当前估值水平，公司具有投资价值。')
        sections.append(f"""## 估值分析

{valuation_analysis}

{self._convert_json_to_markdown_table(data, '估值分析')}

{self._add_section_images(data, '估值分析')}

""")

        # 盈利预测
        profit_forecast = data.get('盈利预测', '预计公司未来盈利将保持稳定增长。')
        sections.append(f"""## 盈利预测

{profit_forecast}

{self._convert_json_to_markdown_table(data, '盈利预测')}

{self._add_section_images(data, '盈利预测')}

""")

        # 投资建议
        investment_advice = data.get('投资建议', '综合考虑各项因素，给予增持评级。')
        sections.append(f"""## 投资建议

{investment_advice}

{self._add_section_images(data, '投资建议')}

""")

        # 风险提示
        risk_warning = data.get('风险提示', '投资需关注相关风险因素。')
        sections.append(f"""## 风险提示

{risk_warning}

""")

        return '\n'.join(sections)

    def _generate_industry_sections(self, data: Dict[str, Any]) -> str:
        """生成行业报告的Markdown章节，包含图片和表格"""
        sections = []

        sections.append(f"""## 行业概况

{data.get('行业概况', '行业发展态势良好，前景广阔。')}

{self._convert_json_to_markdown_table(data, '行业概况')}

{self._add_section_images(data, '行业概况')}

""")

        sections.append(f"""## 市场规模分析

{data.get('市场规模分析', '行业市场规模持续增长。')}

{self._convert_json_to_markdown_table(data, '市场规模分析')}

{self._add_section_images(data, '市场规模分析')}

""")

        sections.append(f"""## 竞争格局分析

{data.get('竞争格局分析', '行业竞争格局相对稳定。')}

{self._convert_json_to_markdown_table(data, '竞争格局分析')}

{self._add_section_images(data, '竞争格局分析')}

""")

        sections.append(f"""## 技术发展趋势

{data.get('技术发展趋势', '行业技术发展迅速。')}

{self._convert_json_to_markdown_table(data, '技术发展趋势')}

{self._add_section_images(data, '技术发展趋势')}

""")

        sections.append(f"""## 投资机会分析

{data.get('投资机会分析', '行业存在良好投资机会。')}

{self._convert_json_to_markdown_table(data, '投资机会分析')}

{self._add_section_images(data, '投资机会分析')}

""")

        sections.append(f"""## 风险提示

{data.get('风险提示', '需关注行业相关风险。')}

""")

        return '\n'.join(sections)

    def _generate_macro_sections(self, data: Dict[str, Any]) -> str:
        """生成宏观报告的Markdown章节，包含图片和表格"""
        sections = []

        sections.append(f"""## 宏观环境分析

{data.get('宏观环境分析', '当前宏观环境整体稳定。')}

{self._convert_json_to_markdown_table(data, '宏观环境分析')}

{self._add_section_images(data, '宏观环境分析')}

""")

        sections.append(f"""## 政策分析

{data.get('政策分析', '相关政策支持发展。')}

{self._convert_json_to_markdown_table(data, '政策分析')}

{self._add_section_images(data, '政策分析')}

""")

        sections.append(f"""## 市场趋势分析

{data.get('市场趋势分析', '市场趋势向好。')}

{self._convert_json_to_markdown_table(data, '市场趋势分析')}

{self._add_section_images(data, '市场趋势分析')}

""")

        sections.append(f"""## 投资策略建议

{data.get('投资策略建议', '建议采取积极投资策略。')}

{self._convert_json_to_markdown_table(data, '投资策略建议')}

{self._add_section_images(data, '投资策略建议')}

""")

        sections.append(f"""## 风险提示

{data.get('风险提示', '需关注相关风险因素。')}

""")

        return '\n'.join(sections)

    def _create_report_fallback(self, report_type: str, data: Dict[str, Any]) -> str:
        """回退方法：如果Markdown转换失败，使用简化的DOCX生成"""
        from docx import Document

        doc = Document()
        subject_name = data.get('company_name') or data.get('industry_name') or data.get('topic', '研究对象')

        # 简单的标题
        title_map = {
            'company': '公司研究报告',
            'industry': '行业研究报告',
            'macro': '宏观策略报告'
        }

        report_title = title_map.get(report_type, '研究报告')
        doc.add_heading(f'{subject_name}{report_title}', 0)

        # 添加简化内容
        for section_name in ['投资要点', '概况分析', '财务分析', '投资建议', '风险提示']:
            doc.add_heading(section_name, level=1)
            content = data.get(section_name, f'{section_name}相关分析内容。')
            doc.add_paragraph(content)

        # 保存
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{report_type.title()}_Report_{timestamp}.docx"
        filepath = os.path.join(self.output_dir, filename)
        doc.save(filepath)

        return filepath

    def _convert_json_to_markdown_table(self, data: Dict[str, Any], section_name: str) -> str:
        """将JSON数据转换为Markdown表格"""
        import json
        import re

        # 查找该章节的内容
        section_content = data.get(section_name, '')
        if not section_content:
            return ''

        # 查找JSON代码块
        json_pattern = r'```json\s*\n(.*?)\n```'
        matches = re.findall(json_pattern, str(section_content), re.DOTALL)

        markdown_tables = []

        for json_str in matches:
            try:
                json_data = json.loads(json_str.strip())

                if isinstance(json_data, dict):
                    # 转换为Markdown表格
                    table_md = self._dict_to_markdown_table(json_data)
                    if table_md:
                        markdown_tables.append(table_md)

            except json.JSONDecodeError:
                continue

        return '\n\n'.join(markdown_tables)

    def _dict_to_markdown_table(self, data: Dict) -> str:
        """将字典转换为Markdown表格"""
        if not data:
            return ''

        # 检查数据结构
        first_key = list(data.keys())[0]
        first_value = data[first_key]

        if isinstance(first_value, list):
            # 数据表格格式
            headers = list(data.keys())
            rows = []

            # 获取最大行数
            max_rows = max(len(data[key]) if isinstance(data[key], list) else 1 for key in headers)

            # 构建表格
            table_lines = []

            # 表头
            table_lines.append('| ' + ' | '.join(headers) + ' |')
            table_lines.append('| ' + ' | '.join(['---'] * len(headers)) + ' |')

            # 数据行
            for i in range(min(max_rows, 20)):  # 限制最大行数
                row = []
                for header in headers:
                    value = data[header]
                    if isinstance(value, list) and i < len(value):
                        row.append(str(value[i]))
                    else:
                        row.append(str(value) if not isinstance(value, list) else '')
                table_lines.append('| ' + ' | '.join(row) + ' |')

            return '\n'.join(table_lines)

        else:
            # 键值对表格格式
            table_lines = []
            table_lines.append('| 项目 | 内容 |')
            table_lines.append('| --- | --- |')

            for key, value in list(data.items())[:20]:  # 限制最大行数
                table_lines.append(f'| {key} | {str(value)} |')

            return '\n'.join(table_lines)

    def _add_section_images(self, data: Dict[str, Any], section_name: str) -> str:
        """为章节添加图片"""
        images_md = []

        # 查找该章节相关的图片
        section_images = data.get(f'{section_name}_images', [])

        # 也查找通用图片字段
        if not section_images:
            all_images = data.get('images', [])
            # 根据章节名称过滤相关图片
            section_images = [img for img in all_images if section_name in img.get('section', '')]

        for img in section_images:
            if isinstance(img, dict):
                img_path = img.get('path', '')
                img_title = img.get('title', '图表')
                img_caption = img.get('caption', '')

                if img_path:
                    # 检查图片文件是否存在
                    if os.path.exists(img_path):
                        images_md.append(f'![{img_title}]({img_path})')
                        if img_caption:
                            images_md.append(f'*{img_caption}*')
                        images_md.append('')  # 空行
            elif isinstance(img, str) and os.path.exists(img):
                # 简单的图片路径
                images_md.append(f'![图表]({img})')
                images_md.append('')

        # 查找data目录下的相关图片
        charts_dir = os.path.join('data', 'charts')
        if os.path.exists(charts_dir):
            for filename in os.listdir(charts_dir):
                if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.svg')):
                    # 根据文件名判断是否与当前章节相关
                    if any(keyword in filename.lower() for keyword in [
                        section_name.lower(),
                        'chart', 'graph', 'plot', '图表', '图片'
                    ]):
                        img_path = os.path.join(charts_dir, filename)
                        images_md.append(f'![图表]({img_path})')
                        images_md.append('')

        return '\n'.join(images_md)



