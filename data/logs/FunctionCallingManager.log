2025-06-19 00:29:35 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 00:29:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 00:29:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 00:29:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 00:29:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 00:29:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 00:29:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 00:29:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 00:29:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 00:29:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 00:29:36 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 web_search 执行成功
2025-06-19 00:29:36 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 add_to_knowledge_base 执行成功
2025-06-19 00:29:36 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 search_knowledge_base 执行成功
2025-06-19 00:29:36 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 00:29:36 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 00:29:36 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 00:29:36 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 00:29:36 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 00:29:36 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 00:29:36 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 00:29:36 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 00:29:36 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 00:29:36 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 00:29:36 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 1 轮对话
2025-06-19 00:29:38 - FunctionCallingManager - INFO - logger.py:111 - info - 执行工具: create_task_plan
2025-06-19 00:29:38 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 create_task_plan 执行成功
2025-06-19 00:29:38 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 2 轮对话
2025-06-19 00:29:55 - FunctionCallingManager - INFO - logger.py:111 - info - 执行工具: web_search
2025-06-19 00:29:56 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 web_search 执行成功
2025-06-19 00:29:56 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 3 轮对话
2025-06-19 00:29:58 - FunctionCallingManager - INFO - logger.py:111 - info - 执行工具: search_knowledge_base
2025-06-19 00:29:58 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 search_knowledge_base 执行成功
2025-06-19 00:29:58 - FunctionCallingManager - WARNING - logger.py:115 - warning - 达到最大迭代次数 3
2025-06-19 00:30:45 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 1 轮对话
2025-06-19 00:30:47 - FunctionCallingManager - INFO - logger.py:111 - info - 执行工具: create_task_plan
2025-06-19 00:30:47 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 create_task_plan 执行成功
2025-06-19 00:30:47 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 2 轮对话
2025-06-19 00:31:16 - FunctionCallingManager - INFO - logger.py:111 - info - 对话完成，无需工具调用
2025-06-19 00:33:39 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 00:33:39 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 00:33:39 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 00:33:39 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 00:33:39 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 00:33:39 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 00:33:39 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 00:33:39 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 00:33:39 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 00:33:39 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 00:33:39 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 web_search 执行成功
2025-06-19 00:33:39 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 add_to_knowledge_base 执行成功
2025-06-19 00:33:39 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 search_knowledge_base 执行成功
2025-06-19 00:33:39 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 00:33:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 00:33:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 00:33:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 00:33:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 00:33:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 00:33:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 00:33:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 00:33:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 00:33:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 00:33:40 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 1 轮对话
2025-06-19 00:33:41 - FunctionCallingManager - INFO - logger.py:111 - info - 执行工具: create_task_plan
2025-06-19 00:33:41 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 create_task_plan 执行成功
2025-06-19 00:33:41 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 2 轮对话
2025-06-19 00:33:59 - FunctionCallingManager - INFO - logger.py:111 - info - 执行工具: web_search
2025-06-19 00:33:59 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 web_search 执行成功
2025-06-19 00:33:59 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 3 轮对话
2025-06-19 00:34:03 - FunctionCallingManager - INFO - logger.py:111 - info - 执行工具: web_search
2025-06-19 00:34:03 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 web_search 执行成功
2025-06-19 00:34:03 - FunctionCallingManager - WARNING - logger.py:115 - warning - 达到最大迭代次数 3
2025-06-19 00:34:40 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 00:34:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 00:34:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 00:34:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 00:34:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 00:34:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 00:34:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 00:34:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 00:34:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 00:34:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 00:34:40 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 00:34:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 00:34:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 00:34:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 00:34:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 00:34:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 00:34:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 00:34:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 00:34:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 00:34:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 00:34:40 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 1 轮对话
2025-06-19 00:34:43 - FunctionCallingManager - INFO - logger.py:111 - info - 执行工具: create_task_plan
2025-06-19 00:34:43 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 create_task_plan 执行成功
2025-06-19 00:34:43 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 2 轮对话
2025-06-19 00:35:00 - FunctionCallingManager - INFO - logger.py:111 - info - 对话完成，无需工具调用
2025-06-19 00:35:36 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 00:35:36 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 00:35:36 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 00:35:36 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 00:35:36 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 00:35:36 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 00:35:36 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 00:35:36 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 00:35:36 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 00:35:36 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 00:35:36 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 1 轮对话
2025-06-19 00:35:38 - FunctionCallingManager - INFO - logger.py:111 - info - 执行工具: create_task_plan
2025-06-19 00:35:38 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 create_task_plan 执行成功
2025-06-19 00:35:38 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 2 轮对话
2025-06-19 00:35:55 - FunctionCallingManager - INFO - logger.py:111 - info - 对话完成，无需工具调用
2025-06-19 00:36:50 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 00:36:50 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 00:36:50 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 00:36:50 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 00:36:50 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 00:36:50 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 00:36:50 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 00:36:50 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 00:36:50 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 00:36:50 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 00:36:50 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 1 轮对话
2025-06-19 00:36:52 - FunctionCallingManager - INFO - logger.py:111 - info - 执行工具: create_task_plan
2025-06-19 00:36:52 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 create_task_plan 执行成功
2025-06-19 00:36:52 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 2 轮对话
2025-06-19 00:42:43 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 00:42:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 00:42:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 00:42:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 00:42:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 00:42:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 00:42:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 00:42:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 00:42:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 00:42:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 00:42:43 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 1 轮对话
2025-06-19 00:42:46 - FunctionCallingManager - INFO - logger.py:111 - info - 执行工具: create_task_plan
2025-06-19 00:42:46 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 create_task_plan 执行成功
2025-06-19 00:42:46 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 2 轮对话
2025-06-19 00:43:02 - FunctionCallingManager - INFO - logger.py:111 - info - 对话完成，无需工具调用
2025-06-19 00:44:42 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 00:44:42 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 00:44:42 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 00:44:42 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 00:44:42 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 00:44:42 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 00:44:42 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 00:44:42 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 00:44:42 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 00:44:42 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 00:44:42 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 1 轮对话
2025-06-19 00:44:45 - FunctionCallingManager - INFO - logger.py:111 - info - 执行工具: create_task_plan
2025-06-19 00:44:45 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 create_task_plan 执行成功
2025-06-19 00:44:45 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 2 轮对话
2025-06-19 00:45:00 - FunctionCallingManager - INFO - logger.py:111 - info - 对话完成，无需工具调用
2025-06-19 00:45:47 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 00:45:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 00:45:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 00:45:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 00:45:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 00:45:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 00:45:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 00:45:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 00:45:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 00:45:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 00:45:47 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 1 轮对话
2025-06-19 00:45:50 - FunctionCallingManager - INFO - logger.py:111 - info - 执行工具: create_task_plan
2025-06-19 00:45:50 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 create_task_plan 执行成功
2025-06-19 00:45:50 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 2 轮对话
2025-06-19 00:45:59 - FunctionCallingManager - INFO - logger.py:111 - info - 执行工具: web_search
2025-06-19 00:46:00 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 web_search 执行成功
2025-06-19 00:46:00 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 3 轮对话
2025-06-19 00:46:02 - FunctionCallingManager - INFO - logger.py:111 - info - 执行工具: get_url_content
2025-06-19 00:46:02 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 get_url_content 执行成功
2025-06-19 00:46:02 - FunctionCallingManager - WARNING - logger.py:115 - warning - 达到最大迭代次数 3
2025-06-19 00:47:12 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 00:47:12 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 00:47:12 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 00:47:12 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 00:47:12 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 00:47:12 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 00:47:12 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 00:47:12 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 00:47:12 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 00:47:12 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 00:47:12 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 1 轮对话
2025-06-19 00:47:16 - FunctionCallingManager - INFO - logger.py:111 - info - 执行工具: create_task_plan
2025-06-19 00:47:16 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 create_task_plan 执行成功
2025-06-19 00:47:16 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 2 轮对话
2025-06-19 01:10:10 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 01:10:10 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 01:10:10 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 01:10:10 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 01:10:10 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 01:10:10 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 01:10:10 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 01:10:10 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 01:10:10 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 01:10:10 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 01:10:10 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 1 轮对话
2025-06-19 01:10:13 - FunctionCallingManager - INFO - logger.py:111 - info - 执行工具: create_task_plan
2025-06-19 01:10:13 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 create_task_plan 执行成功
2025-06-19 01:10:13 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 2 轮对话
2025-06-19 01:10:32 - FunctionCallingManager - INFO - logger.py:111 - info - 对话完成，无需工具调用
2025-06-19 01:34:44 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 01:34:44 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 01:34:44 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 01:34:44 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 01:34:44 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 01:34:44 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 01:34:44 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 01:34:44 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 01:34:44 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 01:34:44 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 01:34:44 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 1 轮对话
2025-06-19 01:34:45 - FunctionCallingManager - INFO - logger.py:111 - info - 执行工具: create_task_plan
2025-06-19 01:34:45 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 create_task_plan 执行成功
2025-06-19 01:34:45 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 2 轮对话
2025-06-19 01:35:01 - FunctionCallingManager - INFO - logger.py:111 - info - 执行工具: web_search
2025-06-19 01:35:05 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 web_search 执行成功
2025-06-19 01:35:05 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 3 轮对话
2025-06-19 01:35:19 - FunctionCallingManager - INFO - logger.py:111 - info - 执行工具: web_search
2025-06-19 01:35:22 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 web_search 执行成功
2025-06-19 01:35:22 - FunctionCallingManager - WARNING - logger.py:115 - warning - 达到最大迭代次数 3
2025-06-19 01:36:56 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 01:36:56 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 01:36:56 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 01:36:56 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 01:36:56 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 01:36:56 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 01:36:56 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 01:36:56 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 01:36:56 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 01:36:56 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 01:36:56 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 1 轮对话
2025-06-19 01:36:59 - FunctionCallingManager - INFO - logger.py:111 - info - 执行工具: create_task_plan
2025-06-19 01:36:59 - FunctionCallingManager - INFO - logger.py:111 - info - 工具 create_task_plan 执行成功
2025-06-19 01:36:59 - FunctionCallingManager - INFO - logger.py:111 - info - 开始第 2 轮对话
2025-06-19 01:54:46 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 01:54:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 01:54:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 01:54:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 01:54:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 01:54:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 01:54:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 01:54:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 01:54:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 01:54:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 01:58:35 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 01:58:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 01:58:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 01:58:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 01:58:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 01:58:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 01:58:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 01:58:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 01:58:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 01:58:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 02:14:43 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 02:14:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 02:14:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 02:14:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 02:14:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 02:14:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 02:14:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 02:14:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 02:14:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 02:14:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 02:27:02 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 02:27:02 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 02:27:02 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 02:27:02 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 02:27:02 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 02:27:02 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 02:27:02 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 02:27:02 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 02:27:02 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 02:27:02 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 02:29:33 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 02:29:33 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 02:29:33 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 02:29:33 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 02:29:33 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 02:29:33 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 02:29:33 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 02:29:33 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 02:29:33 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 02:29:33 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 02:29:42 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 02:29:42 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 02:29:42 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 02:29:42 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 02:29:42 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 02:29:42 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 02:29:42 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 02:29:42 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 02:29:42 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 02:29:42 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 02:37:14 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 02:37:14 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 02:37:14 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 02:37:14 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 02:37:14 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 02:37:14 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 02:37:14 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 02:37:14 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 02:37:14 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 02:37:14 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 02:40:40 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 02:40:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 02:40:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 02:40:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 02:40:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 02:40:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 02:40:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 02:40:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 02:40:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 02:40:40 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 03:02:12 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 03:02:12 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 03:02:12 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 03:02:12 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 03:02:12 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 03:02:12 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 03:02:12 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 03:02:12 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 03:02:12 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 03:02:12 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 03:30:59 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 03:30:59 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 03:30:59 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 03:30:59 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 03:30:59 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 03:30:59 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 03:30:59 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 03:30:59 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 03:30:59 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 03:30:59 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 03:33:44 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 03:33:44 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 03:33:44 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 03:33:44 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 03:33:44 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 03:33:44 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 03:33:44 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 03:33:44 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 03:33:44 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 03:33:44 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 03:53:52 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 03:53:52 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 03:53:52 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 03:53:52 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 03:53:52 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 03:53:52 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 03:53:52 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 03:53:52 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 03:53:52 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 03:53:52 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 03:56:49 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 03:56:49 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 03:56:49 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 03:56:49 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 03:56:49 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 03:56:49 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 03:56:49 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 03:56:49 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 03:56:49 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 03:56:49 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 04:07:56 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 04:07:56 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 04:07:56 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 04:07:56 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 04:07:56 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 04:07:56 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 04:07:56 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 04:07:56 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 04:07:56 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 04:07:56 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 10:40:00 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 10:40:00 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 10:40:00 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 10:40:00 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 10:40:00 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 10:40:00 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 10:40:00 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 10:40:00 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 10:40:00 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 10:40:00 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 10:42:24 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 10:42:24 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 10:42:24 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 10:42:24 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 10:42:24 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 10:42:24 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 10:42:24 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 10:42:24 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 10:42:24 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 10:42:24 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 10:59:22 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 10:59:22 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 10:59:22 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 10:59:22 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 10:59:22 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 10:59:22 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 10:59:22 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 10:59:22 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 10:59:22 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 10:59:22 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 11:07:04 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 11:07:04 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 11:07:04 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 11:07:04 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 11:07:04 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 11:07:04 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 11:07:04 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 11:07:04 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 11:07:04 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 11:07:04 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 11:08:20 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 11:08:20 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 11:08:20 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 11:08:20 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 11:08:20 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 11:08:20 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 11:08:20 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 11:08:20 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 11:08:20 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 11:08:20 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 11:10:35 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 11:10:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 11:10:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 11:10:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 11:10:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 11:10:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 11:10:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 11:10:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 11:10:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 11:10:35 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 11:13:42 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 11:13:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 11:13:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 11:13:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 11:13:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 11:13:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 11:13:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 11:13:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 11:13:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 11:13:43 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 11:27:51 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 11:27:51 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 11:27:51 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 11:27:51 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 11:27:51 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 11:27:51 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 11:27:51 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 11:27:51 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 11:27:51 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 11:27:51 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 11:30:45 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 11:30:45 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 11:30:45 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 11:30:45 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 11:30:45 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 11:30:45 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 11:30:45 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 11:30:45 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 11:30:45 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 11:30:45 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 11:50:28 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 11:50:28 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 11:50:28 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 11:50:28 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 11:50:28 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 11:50:28 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 11:50:28 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 11:50:28 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 11:50:28 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 11:50:28 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 12:53:45 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 12:53:46 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 12:53:46 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 12:53:46 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 12:53:46 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 12:53:46 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 12:53:46 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 12:53:46 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 12:53:46 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 12:53:46 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 12:57:08 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 12:57:08 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 12:57:08 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 12:57:08 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 12:57:08 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 12:57:08 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 12:57:08 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 12:57:08 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 12:57:08 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 12:57:08 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 12:58:23 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 12:58:23 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 12:58:23 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 12:58:23 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 12:58:23 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 12:58:23 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 12:58:23 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 12:58:23 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 12:58:23 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 12:58:23 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 13:02:56 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 13:02:57 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 13:02:57 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 13:02:57 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 13:02:57 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 13:02:57 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 13:02:57 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 13:02:57 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 13:02:57 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 13:02:57 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 13:27:16 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 13:27:16 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 13:27:16 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 13:27:16 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 13:27:16 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 13:27:16 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 13:27:16 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 13:27:16 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 13:27:16 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 13:27:16 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 13:52:25 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 13:52:25 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 13:52:25 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 13:52:25 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 13:52:25 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 13:52:25 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 13:52:25 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 13:52:25 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 13:52:25 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 13:52:25 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 13:54:17 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 13:54:17 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 13:54:17 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 13:54:17 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 13:54:17 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 13:54:17 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 13:54:17 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 13:54:17 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 13:54:17 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 13:54:17 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 13:55:48 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 13:55:48 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 13:55:48 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 13:55:48 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 13:55:48 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 13:55:48 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 13:55:48 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 13:55:48 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 13:55:48 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 13:55:48 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 13:56:46 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 13:56:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 13:56:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 13:56:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 13:56:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 13:56:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 13:56:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 13:56:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 13:56:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 13:56:47 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 14:07:37 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 14:07:37 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 14:07:37 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 14:07:37 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 14:07:37 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 14:07:37 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 14:07:37 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 14:07:37 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 14:07:37 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 14:07:37 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
2025-06-19 14:54:19 - FunctionCallingManager - INFO - logger.py:111 - info - FunctionCallingManager 初始化完成
2025-06-19 14:54:20 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: web_search
2025-06-19 14:54:20 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: get_url_content
2025-06-19 14:54:20 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: search_knowledge_base
2025-06-19 14:54:20 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: add_to_knowledge_base
2025-06-19 14:54:20 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_company_report
2025-06-19 14:54:20 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_industry_report
2025-06-19 14:54:20 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: generate_macro_report
2025-06-19 14:54:20 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: analyze_data
2025-06-19 14:54:20 - FunctionCallingManager - INFO - logger.py:111 - info - 注册工具: create_task_plan
