#!/usr/bin/env python3
# simple_test.py
"""
简单测试脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_basic_import():
    """测试基本导入"""
    try:
        print("🔧 测试基本导入...")
        
        from config.settings import Config
        print("✅ Config 导入成功")
        
        from src.models.llm_client import LLMClient
        print("✅ LLMClient 导入成功")
        
        from src.tools.function_calling_manager import FunctionCallingManager
        print("✅ FunctionCallingManager 导入成功")
        
        from src.agents.intelligent_agent import IntelligentFinancialAgent
        print("✅ IntelligentFinancialAgent 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_basic_functionality():
    """测试基本功能"""
    try:
        print("\n🔧 测试基本功能...")
        
        # 初始化配置
        config = Config()
        config.create_directories()
        print("✅ 配置初始化成功")
        
        # 初始化LLM客户端
        llm_client = LLMClient(config)
        print("✅ LLM客户端初始化成功")
        
        # 测试简单的LLM调用
        response = await llm_client.async_chat_completion([
            {"role": "user", "content": "你好，请回复'测试成功'"}
        ])
        
        if response.get("success"):
            print("✅ LLM调用成功")
            print(f"   响应: {response.get('content', '')[:50]}...")
        else:
            print(f"❌ LLM调用失败: {response.get('error', '未知错误')}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_intelligent_agent_init():
    """测试智能Agent初始化"""
    try:
        print("\n🔧 测试智能Agent初始化...")
        
        from src.agents.intelligent_agent import IntelligentFinancialAgent
        from config.settings import Config
        
        config = Config()
        agent = IntelligentFinancialAgent(config)
        print("✅ 智能Agent初始化成功")
        
        # 检查工具注册
        tools = agent.function_manager.get_registered_tools()
        print(f"✅ 已注册 {len(tools)} 个工具")
        
        return True
        
    except Exception as e:
        print(f"❌ 智能Agent初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_simple_goal():
    """测试简单目标"""
    try:
        print("\n🔧 测试简单目标...")
        
        from src.agents.intelligent_agent import IntelligentFinancialAgent
        from config.settings import Config
        
        config = Config()
        agent = IntelligentFinancialAgent(config)
        
        # 测试一个非常简单的目标
        goal = "测试系统是否正常工作"
        print(f"🎯 目标: {goal}")
        
        result = await agent.run(goal)
        
        if result.get("success"):
            print("✅ 简单目标测试成功")
            print(f"   摘要: {result.get('summary', '')}")
        else:
            print(f"❌ 简单目标测试失败")
            print(f"   错误: {result.get('errors', [])}")
        
        return result.get("success", False)
        
    except Exception as e:
        print(f"❌ 简单目标测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🚀 简单测试开始")
    print("=" * 50)
    
    tests = [
        ("基本导入", test_basic_import),
        ("基本功能", test_basic_functionality),
        ("智能Agent初始化", test_intelligent_agent_init),
        ("简单目标", test_simple_goal)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 运行测试: {test_name}")
        print("-" * 30)
        
        try:
            success = await test_func()
            results.append((test_name, success))
            
            if success:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            print(f"💥 {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 打印总结
    print("\n" + "=" * 50)
    print("📊 测试总结")
    print("=" * 50)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    print(f"✅ 通过: {passed}/{total}")
    print(f"❌ 失败: {total - passed}/{total}")
    
    for test_name, success in results:
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")
    
    if passed == total:
        print("\n🎉 所有测试通过！系统基本功能正常。")
        return 0
    else:
        print(f"\n⚠️  {total - passed} 个测试失败，请检查问题。")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
