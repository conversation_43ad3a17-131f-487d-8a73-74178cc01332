# src/tools/professional_docx_generator.py
"""
通用化专业金融研报DOCX生成器
提供专业样式和结构模板，内容由LLM生成
"""

import os
import json
import io
import re
from datetime import datetime
from typing import Dict, Any, List, Optional, Generator
from docx import Document
from docx.shared import Inches, Pt, RGBColor, Cm
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT
import matplotlib.pyplot as plt
import numpy as np
from config.settings import Config
from src.utils.logger import get_logger

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ProfessionalFinancialReportGenerator:
    """通用化专业金融研报生成器"""
    
    def __init__(self, config: Config = None):
        self.config = config or Config()
        self.output_dir = self.config.REPORT_OUTPUT_DIR
        self.logger = get_logger("ProfessionalDocxGenerator")
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 定义标准报告结构模板
        self.report_structures = {
            "company": {
                "title": "公司研究报告",
                "sections": [
                    {"name": "投资要点", "level": 1, "required": True},
                    {"name": "公司概况", "level": 1, "required": True},
                    {"name": "主营业务分析", "level": 1, "required": True},
                    {"name": "财务分析", "level": 1, "required": True},
                    {"name": "估值分析", "level": 1, "required": True},
                    {"name": "盈利预测", "level": 1, "required": True},
                    {"name": "投资建议", "level": 1, "required": True},
                    {"name": "风险提示", "level": 1, "required": True}
                ]
            },
            "industry": {
                "title": "行业深度研究报告",
                "sections": [
                    {"name": "投资要点", "level": 1, "required": True},
                    {"name": "行业概况", "level": 1, "required": True},
                    {"name": "市场规模分析", "level": 1, "required": True},
                    {"name": "竞争格局分析", "level": 1, "required": True},
                    {"name": "技术发展趋势", "level": 1, "required": True},
                    {"name": "政策影响分析", "level": 1, "required": True},
                    {"name": "投资机会分析", "level": 1, "required": True},
                    {"name": "风险提示", "level": 1, "required": True}
                ]
            },
            "macro": {
                "title": "宏观策略研究报告",
                "sections": [
                    {"name": "投资要点", "level": 1, "required": True},
                    {"name": "宏观环境分析", "level": 1, "required": True},
                    {"name": "政策解读", "level": 1, "required": True},
                    {"name": "市场趋势分析", "level": 1, "required": True},
                    {"name": "资产配置建议", "level": 1, "required": True},
                    {"name": "投资策略", "level": 1, "required": True},
                    {"name": "风险评估", "level": 1, "required": True},
                    {"name": "总结与展望", "level": 1, "required": True}
                ]
            }
        }
    
    def get_report_structure_prompt(self, report_type: str) -> str:
        """获取报告结构提示，供LLM生成内容时参考"""
        if report_type not in self.report_structures:
            raise ValueError(f"不支持的报告类型: {report_type}")
        
        structure = self.report_structures[report_type]
        sections = structure["sections"]
        
        prompt = f"""
请按照以下专业金融研报结构生成{structure['title']}内容：

报告结构要求：
"""
        
        for i, section in enumerate(sections, 1):
            prompt += f"{i}. {section['name']}\n"
            
        prompt += """
每个章节要求：
- 内容专业、详实，符合金融研报标准
- 包含具体数据、图表说明和分析结论
- 投资建议要明确，风险提示要全面
- 语言规范，逻辑清晰

请为每个章节生成详细内容，包括：
- 章节标题
- 详细分析内容
- 相关数据表格（如适用）
- 图表描述（如适用）

数据格式要求：
- 文本内容：直接提供段落文本
- 表格数据：使用字典格式，键为列名，值为数据列表
- 图表数据：提供图表类型、数据和标题
"""
        return prompt
    
    def create_company_report(self, data: Dict[str, Any]) -> str:
        """生成专业公司研究报告 - 使用企业级生成器"""
        try:
            from src.tools.enterprise_docx_generator import EnterpriseDocxGenerator
            enterprise_generator = EnterpriseDocxGenerator(self.output_dir)
            return enterprise_generator.create_company_report(data)
        except Exception as e:
            print(f"⚠️ 企业级生成器失败，使用备用方法: {e}")
            return self._create_report_via_markdown("company", data)
    
    def create_industry_report(self, data: Dict[str, Any]) -> str:
        """生成专业行业研究报告 - 使用企业级生成器"""
        try:
            from src.tools.enterprise_docx_generator import EnterpriseDocxGenerator
            enterprise_generator = EnterpriseDocxGenerator(self.output_dir)
            return enterprise_generator.create_industry_report(data)
        except Exception as e:
            print(f"⚠️ 企业级生成器失败，使用备用方法: {e}")
            return self._create_report_via_markdown("industry", data)

    def create_macro_report(self, data: Dict[str, Any]) -> str:
        """生成专业宏观策略报告 - 使用企业级生成器"""
        try:
            from src.tools.enterprise_docx_generator import EnterpriseDocxGenerator
            enterprise_generator = EnterpriseDocxGenerator(self.output_dir)
            return enterprise_generator.create_macro_report(data)
        except Exception as e:
            print(f"⚠️ 企业级生成器失败，使用备用方法: {e}")
            return self._create_report_via_markdown("macro", data)

    def _create_report_via_markdown(self, report_type: str, data: Dict[str, Any]) -> str:
        """使用Markdown转DOCX的快速方式生成报告 - 直接使用已有数据，不重复生成"""
        try:
            import pypandoc
        except ImportError:
            # 如果没有pypandoc，回退到原始方法
            return self._create_report(report_type, data)

        # 1. 使用包含表格和图片功能的方法生成Markdown内容
        markdown_content = self._generate_markdown_report(report_type, data)

        # 2. 保存Markdown文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        md_filename = f"{report_type.title()}_Report_{timestamp}.md"
        md_filepath = os.path.join(self.output_dir, md_filename)

        with open(md_filepath, 'w', encoding='utf-8') as f:
            f.write(markdown_content)

        # 3. 转换为DOCX
        docx_filename = f"{report_type.title()}_Report_{timestamp}.docx"
        docx_filepath = os.path.join(self.output_dir, docx_filename)

        try:
            # 使用pypandoc转换
            pypandoc.convert_file(
                md_filepath,
                'docx',
                outputfile=docx_filepath,
                extra_args=['--reference-doc=template.docx'] if os.path.exists('template.docx') else []
            )

            # 删除临时Markdown文件
            os.remove(md_filepath)

            return docx_filepath

        except Exception as e:
            print(f"Pandoc转换失败: {e}")
            # 回退到原始方法
            return self._create_report(report_type, data)

    def _format_existing_data_to_markdown(self, report_type: str, data: Dict[str, Any]) -> str:
        """直接格式化已有数据为Markdown，不重复调用LLM"""
        subject_name = data.get('company_name') or data.get('industry_name') or data.get('topic', '研究对象')

        # 报告标题映射
        title_map = {
            'company': '公司研究报告',
            'industry': '行业研究报告',
            'macro': '宏观策略报告'
        }

        report_title = title_map.get(report_type, '研究报告')

        # 生成Markdown内容
        markdown = f"""# {subject_name}{report_title}

## 报告信息
- **报告日期**: {datetime.now().strftime('%Y年%m月%d日')}
- **分析师**: AI智能分析系统
- **报告类型**: {report_title}

---

## 免责声明
本报告仅供参考，不构成投资建议。投资有风险，入市需谨慎。本报告中的信息均来源于公开资料，本机构对这些信息的准确性、完整性或可靠性不作任何保证。

---

"""

        # 根据报告类型添加相应章节 - 直接使用已有数据
        if report_type == 'company':
            markdown += self._format_company_sections_md(data)
        elif report_type == 'industry':
            markdown += self._format_industry_sections_md(data)
        elif report_type == 'macro':
            markdown += self._format_macro_sections_md(data)

        return markdown

    def _format_company_sections_md(self, data: Dict[str, Any]) -> str:
        """格式化公司报告的Markdown章节 - 直接使用已有数据"""
        sections = []

        # 按顺序添加各个章节
        section_order = [
            '投资要点', '公司概况', '主营业务分析', '财务分析',
            '估值分析', '盈利预测', '投资建议', '风险提示'
        ]

        for section_name in section_order:
            content = data.get(section_name, f'{section_name}相关内容正在完善中。')
            sections.append(f"""## {section_name}

{content}

""")

        return '\n'.join(sections)

    def _format_industry_sections_md(self, data: Dict[str, Any]) -> str:
        """格式化行业报告的Markdown章节 - 直接使用已有数据"""
        sections = []

        section_order = [
            '投资要点', '行业概况', '市场规模分析', '竞争格局分析',
            '技术发展趋势', '政策影响分析', '投资机会分析', '风险提示'
        ]

        for section_name in section_order:
            content = data.get(section_name, f'{section_name}相关内容正在完善中。')
            sections.append(f"""## {section_name}

{content}

""")

        return '\n'.join(sections)

    def _format_macro_sections_md(self, data: Dict[str, Any]) -> str:
        """格式化宏观报告的Markdown章节 - 直接使用已有数据"""
        sections = []

        section_order = [
            '投资要点', '宏观环境分析', '政策解读', '市场趋势分析',
            '资产配置建议', '投资策略', '风险评估', '总结与展望'
        ]

        for section_name in section_order:
            content = data.get(section_name, f'{section_name}相关内容正在完善中。')
            sections.append(f"""## {section_name}

{content}

""")

        return '\n'.join(sections)

    def _generate_markdown_report(self, report_type: str, data: Dict[str, Any]) -> str:
        """生成Markdown格式的报告内容，包含图片和表格"""
        subject_name = data.get('company_name') or data.get('industry_name') or data.get('topic', '研究对象')

        # 报告标题映射
        title_map = {
            'company': '公司研究报告',
            'industry': '行业研究报告',
            'macro': '宏观策略报告'
        }

        report_title = title_map.get(report_type, '研究报告')

        # 生成Markdown内容
        markdown = f"""# {subject_name}{report_title}

## 报告信息
- **报告日期**: {datetime.now().strftime('%Y年%m月%d日')}
- **分析师**: AI智能分析系统
- **报告类型**: {report_title}

---

## 免责声明
本报告仅供参考，不构成投资建议。投资有风险，入市需谨慎。本报告中的信息均来源于公开资料，本机构对这些信息的准确性、完整性或可靠性不作任何保证。

---

"""

        # 根据报告类型添加相应章节
        if report_type == 'company':
            markdown += self._generate_company_sections_md(data)
        elif report_type == 'industry':
            markdown += self._generate_industry_sections_md(data)
        elif report_type == 'macro':
            markdown += self._generate_macro_sections_md(data)

        return markdown

    def _generate_company_sections_md(self, data: Dict[str, Any]) -> str:
        """生成公司报告的Markdown章节，包含图片和表格"""
        sections = []

        # 投资要点
        investment_points = data.get('投资要点', '基于深入分析，该公司具有良好的投资价值。')
        # 处理JSON并转换为表格
        clean_content = self._process_section_content(investment_points)
        table_content = self._convert_json_to_markdown_table(data, '投资要点')
        image_content = self._add_section_images_md(data, '投资要点')

        sections.append(f"""## 投资要点

{clean_content}

{table_content}

{image_content}

""")

        # 公司概况
        company_overview = data.get('公司概况', '公司在行业中具有重要地位，发展前景良好。')
        clean_content = self._process_section_content(company_overview)
        table_content = self._convert_json_to_markdown_table(data, '公司概况')
        image_content = self._add_section_images_md(data, '公司概况')

        sections.append(f"""## 公司概况

{clean_content}

{table_content}

{image_content}

""")

        # 主营业务分析
        business_analysis = data.get('主营业务分析', '公司主营业务发展稳健，盈利能力良好。')
        clean_content = self._process_section_content(business_analysis)
        table_content = self._convert_json_to_markdown_table(data, '主营业务分析')
        image_content = self._add_section_images_md(data, '主营业务分析')

        sections.append(f"""## 主营业务分析

{clean_content}

{table_content}

{image_content}

""")

        # 财务分析
        financial_analysis = data.get('财务分析', '公司财务状况良好，各项指标表现稳定。')
        clean_content = self._process_section_content(financial_analysis)
        table_content = self._convert_json_to_markdown_table(data, '财务分析')
        image_content = self._add_section_images_md(data, '财务分析')

        sections.append(f"""## 财务分析

{clean_content}

{table_content}

{image_content}

""")

        # 估值分析
        valuation_analysis = data.get('估值分析', '基于当前估值水平，公司具有投资价值。')
        clean_content = self._process_section_content(valuation_analysis)
        table_content = self._convert_json_to_markdown_table(data, '估值分析')
        image_content = self._add_section_images_md(data, '估值分析')

        sections.append(f"""## 估值分析

{clean_content}

{table_content}

{image_content}

""")

        # 盈利预测
        profit_forecast = data.get('盈利预测', '预计公司未来盈利将保持稳定增长。')
        clean_content = self._process_section_content(profit_forecast)
        table_content = self._convert_json_to_markdown_table(data, '盈利预测')
        image_content = self._add_section_images_md(data, '盈利预测')

        sections.append(f"""## 盈利预测

{clean_content}

{table_content}

{image_content}

""")

        # 投资建议
        investment_advice = data.get('投资建议', '综合考虑各项因素，给予增持评级。')
        clean_content = self._process_section_content(investment_advice)
        image_content = self._add_section_images_md(data, '投资建议')

        sections.append(f"""## 投资建议

{clean_content}

{image_content}

""")

        # 风险提示
        risk_warning = data.get('风险提示', '投资需关注相关风险因素。')
        clean_content = self._process_section_content(risk_warning)

        sections.append(f"""## 风险提示

{clean_content}

""")

        return '\n'.join(sections)

    def _process_section_content(self, content: str) -> str:
        """处理章节内容，移除JSON代码块并清理格式"""
        import re

        if not content:
            return content

        # 移除JSON代码块
        content = re.sub(r'```json\s*\n.*?\n```', '', content, flags=re.DOTALL)

        # 移除多余的空行
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)

        # 清理开头和结尾的空白
        content = content.strip()

        return content

    def _generate_industry_sections_md(self, data: Dict[str, Any]) -> str:
        """生成行业报告的Markdown章节，包含图片和表格"""
        sections = []

        # 行业概况
        content = self._process_section_content(data.get('行业概况', '行业发展态势良好，前景广阔。'))
        table_content = self._convert_json_to_markdown_table(data, '行业概况')
        image_content = self._add_section_images_md(data, '行业概况')

        sections.append(f"""## 行业概况

{content}

{table_content}

{image_content}

""")

        # 市场规模分析
        content = self._process_section_content(data.get('市场规模分析', '行业市场规模持续增长。'))
        table_content = self._convert_json_to_markdown_table(data, '市场规模分析')
        image_content = self._add_section_images_md(data, '市场规模分析')

        sections.append(f"""## 市场规模分析

{content}

{table_content}

{image_content}

""")

        # 竞争格局分析
        content = self._process_section_content(data.get('竞争格局分析', '行业竞争格局相对稳定。'))
        table_content = self._convert_json_to_markdown_table(data, '竞争格局分析')
        image_content = self._add_section_images_md(data, '竞争格局分析')

        sections.append(f"""## 竞争格局分析

{content}

{table_content}

{image_content}

""")

        # 技术发展趋势
        content = self._process_section_content(data.get('技术发展趋势', '行业技术发展迅速。'))
        table_content = self._convert_json_to_markdown_table(data, '技术发展趋势')
        image_content = self._add_section_images_md(data, '技术发展趋势')

        sections.append(f"""## 技术发展趋势

{content}

{table_content}

{image_content}

""")

        # 投资机会分析
        content = self._process_section_content(data.get('投资机会分析', '行业存在良好投资机会。'))
        table_content = self._convert_json_to_markdown_table(data, '投资机会分析')
        image_content = self._add_section_images_md(data, '投资机会分析')

        sections.append(f"""## 投资机会分析

{content}

{table_content}

{image_content}

""")

        # 风险提示
        content = self._process_section_content(data.get('风险提示', '需关注行业相关风险。'))

        sections.append(f"""## 风险提示

{content}

""")

        return '\n'.join(sections)

    def _generate_macro_sections_md(self, data: Dict[str, Any]) -> str:
        """生成宏观报告的Markdown章节，包含图片和表格"""
        sections = []

        # 宏观环境分析
        content = self._process_section_content(data.get('宏观环境分析', '当前宏观环境整体稳定。'))
        table_content = self._convert_json_to_markdown_table(data, '宏观环境分析')
        image_content = self._add_section_images_md(data, '宏观环境分析')

        sections.append(f"""## 宏观环境分析

{content}

{table_content}

{image_content}

""")

        # 政策分析
        content = self._process_section_content(data.get('政策分析', '相关政策支持发展。'))
        table_content = self._convert_json_to_markdown_table(data, '政策分析')
        image_content = self._add_section_images_md(data, '政策分析')

        sections.append(f"""## 政策分析

{content}

{table_content}

{image_content}

""")

        # 市场趋势分析
        content = self._process_section_content(data.get('市场趋势分析', '市场趋势向好。'))
        table_content = self._convert_json_to_markdown_table(data, '市场趋势分析')
        image_content = self._add_section_images_md(data, '市场趋势分析')

        sections.append(f"""## 市场趋势分析

{content}

{table_content}

{image_content}

""")

        # 投资策略建议
        content = self._process_section_content(data.get('投资策略建议', '建议采取积极投资策略。'))
        table_content = self._convert_json_to_markdown_table(data, '投资策略建议')
        image_content = self._add_section_images_md(data, '投资策略建议')

        sections.append(f"""## 投资策略建议

{content}

{table_content}

{image_content}

""")

        # 风险提示
        content = self._process_section_content(data.get('风险提示', '需关注相关风险因素。'))

        sections.append(f"""## 风险提示

{content}

""")

        return '\n'.join(sections)

    def _convert_json_to_markdown_table(self, data: Dict[str, Any], section_name: str) -> str:
        """将JSON数据转换为Markdown表格"""
        import json
        import re

        # 查找该章节的内容
        section_content = data.get(section_name, '')
        if not section_content:
            return ''

        # 查找JSON代码块
        json_pattern = r'```json\s*\n(.*?)\n```'
        matches = re.findall(json_pattern, str(section_content), re.DOTALL)

        markdown_tables = []

        for json_str in matches:
            try:
                json_data = json.loads(json_str.strip())

                if isinstance(json_data, dict):
                    # 转换为Markdown表格
                    table_md = self._dict_to_markdown_table(json_data)
                    if table_md:
                        markdown_tables.append(table_md)

            except json.JSONDecodeError:
                continue

        # 如果没有找到JSON，尝试查找其他格式的数据
        if not markdown_tables:
            # 查找可能的表格数据
            table_md = self._extract_table_from_text(section_content)
            if table_md:
                markdown_tables.append(table_md)

        return '\n\n'.join(markdown_tables)

    def _extract_table_from_text(self, text: str) -> str:
        """从文本中提取表格数据"""
        import re

        # 查找类似表格的数据模式
        lines = text.split('\n')
        table_data = []

        for line in lines:
            line = line.strip()
            # 查找包含冒号的键值对
            if '：' in line or ':' in line:
                # 分割键值对
                if '：' in line:
                    key, value = line.split('：', 1)
                else:
                    key, value = line.split(':', 1)

                key = key.strip('•-* ')
                value = value.strip()

                if key and value and len(key) < 50:  # 避免过长的键
                    table_data.append((key, value))

        # 如果找到足够的数据，生成表格
        if len(table_data) >= 3:
            table_lines = ['| 项目 | 内容 |', '| --- | --- |']
            for key, value in table_data[:15]:  # 限制行数
                # 清理值中的特殊字符
                clean_value = value.replace('|', '\\|').replace('\n', ' ')
                table_lines.append(f'| {key} | {clean_value} |')

            return '\n'.join(table_lines)

        return ''

    def _dict_to_markdown_table(self, data: Dict) -> str:
        """将字典转换为Markdown表格"""
        if not data:
            return ''

        # 检查数据结构
        first_key = list(data.keys())[0]
        first_value = data[first_key]

        if isinstance(first_value, list):
            # 数据表格格式
            headers = list(data.keys())

            # 获取最大行数
            max_rows = max(len(data[key]) if isinstance(data[key], list) else 1 for key in headers)

            # 构建表格
            table_lines = []

            # 表头
            table_lines.append('| ' + ' | '.join(headers) + ' |')
            table_lines.append('| ' + ' | '.join(['---'] * len(headers)) + ' |')

            # 数据行
            for i in range(min(max_rows, 20)):  # 限制最大行数
                row = []
                for header in headers:
                    value = data[header]
                    if isinstance(value, list) and i < len(value):
                        row.append(str(value[i]))
                    else:
                        row.append(str(value) if not isinstance(value, list) else '')
                table_lines.append('| ' + ' | '.join(row) + ' |')

            return '\n'.join(table_lines)

        else:
            # 键值对表格格式
            table_lines = []
            table_lines.append('| 项目 | 内容 |')
            table_lines.append('| --- | --- |')

            for key, value in list(data.items())[:20]:  # 限制最大行数
                table_lines.append(f'| {key} | {str(value)} |')

            return '\n'.join(table_lines)

    def _add_section_images_md(self, data: Dict[str, Any], section_name: str) -> str:
        """为章节添加图片"""
        images_md = []

        # 1. 查找该章节相关的图片
        section_images = data.get(f'{section_name}_images', [])

        # 2. 也查找通用图片字段
        if not section_images:
            all_images = data.get('images', [])
            # 根据章节名称过滤相关图片
            section_images = [img for img in all_images if section_name in img.get('section', '')]

        # 3. 处理已有的图片数据
        for img in section_images:
            if isinstance(img, dict):
                img_path = img.get('path', '')
                img_title = img.get('title', '图表')
                img_caption = img.get('caption', '')

                if img_path and os.path.exists(img_path):
                    images_md.append(f'![{img_title}]({img_path})')
                    if img_caption:
                        images_md.append(f'*{img_caption}*')
                    images_md.append('')  # 空行
            elif isinstance(img, str) and os.path.exists(img):
                images_md.append(f'![图表]({img})')
                images_md.append('')

        # 4. 生成示例图表（如果没有真实图片）
        if not images_md:
            # 为不同章节生成相应的示例图表
            chart_suggestions = self._generate_chart_suggestions(section_name, data)
            if chart_suggestions:
                images_md.extend(chart_suggestions)

        # 5. 查找data目录下的相关图片
        charts_dir = os.path.join('data', 'charts')
        if os.path.exists(charts_dir):
            for filename in os.listdir(charts_dir):
                if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.svg')):
                    # 根据文件名判断是否与当前章节相关
                    section_keywords = [
                        section_name.lower(),
                        'chart', 'graph', 'plot', '图表', '图片'
                    ]

                    # 添加更多关键词匹配
                    if section_name == '财务分析':
                        section_keywords.extend(['financial', 'revenue', 'profit', '营收', '利润'])
                    elif section_name == '投资要点':
                        section_keywords.extend(['investment', 'summary', '投资', '要点'])
                    elif section_name == '公司概况':
                        section_keywords.extend(['company', 'overview', '公司', '概况'])

                    if any(keyword in filename.lower() for keyword in section_keywords):
                        img_path = os.path.join(charts_dir, filename)
                        images_md.append(f'![{section_name}图表]({img_path})')
                        images_md.append('')

        return '\n'.join(images_md)

    def _generate_chart_suggestions(self, section_name: str, data: Dict[str, Any]) -> List[str]:
        """为章节生成图表建议"""
        suggestions = []

        # 根据章节类型生成相应的图表建议
        if section_name == '财务分析':
            suggestions.extend([
                '> 📊 **建议图表**: 营收增长趋势图、利润率变化图、现金流量图',
                '> 💡 **数据来源**: 公司年报、季报财务数据',
                ''
            ])
        elif section_name == '投资要点':
            suggestions.extend([
                '> 📈 **建议图表**: 投资评级雷达图、核心竞争力分析图',
                '> 💡 **数据来源**: 行业对比数据、公司核心指标',
                ''
            ])
        elif section_name == '公司概况':
            suggestions.extend([
                '> 🏢 **建议图表**: 组织架构图、业务布局图、发展历程时间轴',
                '> 💡 **数据来源**: 公司官网、招股说明书',
                ''
            ])
        elif section_name == '主营业务分析':
            suggestions.extend([
                '> 📊 **建议图表**: 业务收入结构饼图、各业务板块增长对比图',
                '> 💡 **数据来源**: 分业务收入数据、市场份额数据',
                ''
            ])
        elif section_name == '估值分析':
            suggestions.extend([
                '> 💰 **建议图表**: PE/PB估值对比图、DCF估值模型图',
                '> 💡 **数据来源**: 同行业公司估值数据、历史估值水平',
                ''
            ])
        elif section_name == '盈利预测':
            suggestions.extend([
                '> 📈 **建议图表**: 未来3年盈利预测图、关键指标预测表',
                '> 💡 **数据来源**: 历史财务数据、行业增长预期',
                ''
            ])

        return suggestions

    def _create_report(self, report_type: str, data: Dict[str, Any]) -> str:
        """通用报告生成方法"""
        if report_type not in self.report_structures:
            raise ValueError(f"不支持的报告类型: {report_type}")
        
        structure = self.report_structures[report_type]
        subject_name = data.get('company_name') or data.get('industry_name') or data.get('topic', '研究对象')
        
        self.logger.info(f"开始生成{structure['title']}: {subject_name}")
        
        doc = Document()
        self._setup_document_style(doc)
        
        # 1. 封面页
        self._add_cover_page(doc, data, structure['title'])
        
        # 2. 重要声明页
        self._add_disclaimer_page(doc)
        
        # 3. 目录页
        self._add_toc(doc, structure['sections'])
        
        # 4. 根据结构添加各章节内容
        self._add_report_sections(doc, data, structure['sections'])
        
        # 5. 附录
        self._add_appendix(doc, data)
        
        # 保存文档
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{report_type.title()}_Report_{timestamp}.docx"
        filepath = os.path.join(self.output_dir, filename)
        doc.save(filepath)
        
        self.logger.info(f"{structure['title']}生成完成: {filepath}")
        return filepath

    def _setup_document_style(self, doc):
        """设置专业文档样式"""
        # 设置默认字体
        style = doc.styles['Normal']
        font = style.font
        font.name = '宋体'
        font.size = Pt(10.5)

        # 设置页面布局
        sections = doc.sections
        for section in sections:
            section.top_margin = Cm(2.54)
            section.bottom_margin = Cm(2.54)
            section.left_margin = Cm(3.17)
            section.right_margin = Cm(3.17)

            # 设置页眉页脚
            header = section.header
            footer = section.footer

            # 页眉
            if header.paragraphs:
                header_para = header.paragraphs[0]
                header_para.text = "证券研究报告"
                header_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # 页脚
            if footer.paragraphs:
                footer_para = footer.paragraphs[0]
                footer_para.text = "第 页 共 页"
                footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

    def _add_cover_page(self, doc, data: Dict[str, Any], report_type: str):
        """添加专业封面页"""
        # 报告标题
        subject_name = data.get('company_name') or data.get('industry_name') or data.get('topic', '研究对象')

        title = doc.add_heading(level=0)
        title_run = title.add_run(f"{subject_name}\n{report_type}")
        title_run.font.name = '黑体'
        title_run.font.size = Pt(22)
        title_run.font.bold = True
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 添加空行
        for _ in range(3):
            doc.add_paragraph()

        # 评级信息表格（如果有评级信息）
        if any(key in data for key in ['investment_rating', 'target_price', 'current_price']):
            rating_table = doc.add_table(rows=4, cols=2)
            rating_table.style = 'Table Grid'
            rating_table.alignment = WD_TABLE_ALIGNMENT.CENTER

            rating_data = [
                ("投资评级", data.get('investment_rating', '待定')),
                ("目标价格", data.get('target_price', '待定')),
                ("当前价格", data.get('current_price', '待定')),
                ("预期收益", data.get('expected_return', '待定'))
            ]

            for i, (key, value) in enumerate(rating_data):
                rating_table.rows[i].cells[0].text = key
                rating_table.rows[i].cells[1].text = str(value)
                self._safe_set_cell_bold(rating_table.rows[i].cells[0])

        # 添加空行
        for _ in range(5):
            doc.add_paragraph()

        # 分析师信息
        analyst_info = doc.add_paragraph()
        analyst_info.add_run("分析师：").bold = True
        analyst_info.add_run(f" {data.get('analyst_name', 'AI智能分析师')}\n")
        analyst_info.add_run("执业证书编号：").bold = True
        analyst_info.add_run(f" {data.get('analyst_license', 'S1234567890123456')}\n")
        analyst_info.add_run("联系电话：").bold = True
        analyst_info.add_run(f" {data.get('analyst_phone', '010-12345678')}\n")
        analyst_info.add_run("电子邮箱：").bold = True
        analyst_info.add_run(f" {data.get('analyst_email', '<EMAIL>')}")
        analyst_info.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 报告日期
        date_para = doc.add_paragraph()
        date_para.add_run("报告日期：").bold = True
        date_para.add_run(datetime.now().strftime("%Y年%m月%d日"))
        date_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

        doc.add_page_break()

    def _add_disclaimer_page(self, doc):
        """添加重要声明页"""
        doc.add_heading('重要声明', level=1)

        disclaimer_text = """本报告由AI智能分析系统制作，仅供参考，不构成投资建议。

本报告中的信息均来源于公开资料，我们对这些信息的准确性、完整性或可靠性不作任何保证。本报告所载的资料、意见及推测仅反映我们于发布本报告当日的判断，在不同时期，我们可发出与本报告所载资料、意见及推测不一致的报告。

我们会及时更新我们的研究，但可能会因某些规定而无法做到。除了一些定期出版的报告之外，绝大多数报告是在分析师认为适当的时候不定期地发布。

在任何情况下，本报告中的信息或所表述的意见并不构成对任何人的投资建议，也没有考虑到个别客户特殊的投资目标、财务状况或需求。客户应考虑本报告中的任何意见或建议是否符合其特定状况，以及（若有必要）咨询独立投资顾问。

本报告所载信息的来源被认为是可靠的，但我们不保证其准确性或完整性，也不对使用该信息可能引起的任何损失承担任何责任。本报告用于此用途的适当性或者使用风险将完全由使用者承担。

本报告的版权归AI智能分析系统所有，未经书面许可，任何机构和个人不得以任何形式翻版、复制、发表或引用。"""

        doc.add_paragraph(disclaimer_text)
        doc.add_page_break()

    def _add_toc(self, doc, sections: List[Dict[str, Any]]):
        """添加目录"""
        doc.add_heading('目录', level=1)

        # 创建目录表格
        toc_table = doc.add_table(rows=len(sections) + 1, cols=2)
        toc_table.style = 'Table Grid'
        toc_table.alignment = WD_TABLE_ALIGNMENT.LEFT

        # 表头
        hdr_cells = toc_table.rows[0].cells
        hdr_cells[0].text = "章节"
        hdr_cells[1].text = "页码"
        self._safe_set_cell_bold(hdr_cells[0])
        self._safe_set_cell_bold(hdr_cells[1])

        # 添加章节
        for i, section in enumerate(sections, 1):
            row_cells = toc_table.rows[i].cells
            row_cells[0].text = f"{i}. {section['name']}"
            row_cells[1].text = str(i + 2)  # 假设页码

        doc.add_page_break()

    def _add_report_sections(self, doc, data: Dict[str, Any], sections: List[Dict[str, Any]]):
        """根据结构添加报告各章节内容"""
        for section in sections:
            section_name = section['name']
            section_level = section.get('level', 1)

            # 添加章节标题
            doc.add_heading(section_name, level=section_level)

            # 获取该章节的内容
            section_content = self._get_section_content(data, section_name)

            if section_content:
                # 处理文本内容
                if isinstance(section_content, str):
                    # 验证和补全内容
                    validated_content = self._validate_and_complete_content(section_content, section_name)
                    self._add_formatted_text(doc, validated_content)

                # 处理结构化内容
                elif isinstance(section_content, dict):
                    self._add_structured_content(doc, section_content)

                # 处理列表内容
                elif isinstance(section_content, list):
                    self._add_list_content(doc, section_content)
            else:
                # 如果没有内容，添加默认内容
                default_content = self._get_default_section_content(section_name, data)
                validated_content = self._validate_and_complete_content(default_content, section_name)
                self._add_formatted_text(doc, validated_content)

            # 添加相关图表（如果有）
            self._add_section_charts(doc, data, section_name)

    def _get_section_content(self, data: Dict[str, Any], section_name: str) -> Any:
        """获取指定章节的内容"""
        # 标准化章节名称作为键
        section_key = section_name.replace('分析', '').replace('建议', '').replace('提示', '').replace('要点', '')

        # 尝试多种可能的键名
        possible_keys = [
            section_name,
            section_key,
            section_name.lower(),
            section_key.lower(),
            section_name.replace(' ', '_'),
            section_key.replace(' ', '_')
        ]

        for key in possible_keys:
            if key in data:
                return data[key]

        # 如果没有找到具体内容，返回通用内容
        return self._get_default_section_content(section_name, data)

    def _get_default_section_content(self, section_name: str, data: Dict[str, Any] = None) -> str:
        """获取章节的默认内容"""
        if data:
            subject = data.get('company_name') or data.get('industry_name') or data.get('topic', '研究对象')
        else:
            subject = '研究对象'

        default_contents = {
            "投资要点": f"""
## 投资评级：增持

## 核心投资逻辑：
1. **技术领先优势**：{subject}在核心技术领域具有显著优势，技术壁垒较高
2. **市场地位稳固**：在细分市场占据领先地位，品牌影响力强
3. **财务表现良好**：营收和利润保持稳定增长，财务指标健康
4. **发展前景广阔**：受益于行业发展趋势，未来增长空间较大

## 关键财务数据：
- 营业收入：持续增长，增长率稳定
- 净利润：盈利能力良好，利润率维持较高水平
- 毛利率：保持在合理区间
- 研发投入：持续加大研发投入，创新能力强

## 催化剂：
- 新产品发布和技术突破
- 市场份额进一步提升
- 政策支持和行业发展
- 业务拓展和合作机会
            """,

            "公司概况": f"""
## 公司简介
{subject}是行业内的知名企业，专注于核心业务领域，具有较强的技术实力和市场竞争力。

## 基本信息
```json
{{
  "公司全称": "{subject}",
  "英文名称": "待补充",
  "股票代码": "待补充",
  "成立时间": "待补充",
  "上市时间": "待补充",
  "注册地": "待补充",
  "办公地址": "待补充",
  "员工人数": "待补充",
  "主营业务": "待补充",
  "所属行业": "待补充"
}}
```

## 发展历程
{subject}经历了创立、发展、壮大等重要阶段，在各个发展时期都取得了重要成就。
            """,

            "财务分析": f"""
## 财务概况
{subject}财务状况总体良好，盈利能力稳定，现金流充足。

## 主要财务数据
```json
{{
  "财务指标": ["营业收入(亿元)", "净利润(亿元)", "毛利率", "净利率", "ROE"],
  "最新年度": ["待补充", "待补充", "待补充", "待补充", "待补充"],
  "上年同期": ["待补充", "待补充", "待补充", "待补充", "待补充"],
  "同比变化": ["待补充", "待补充", "待补充", "待补充", "待补充"]
}}
```

## 财务分析
{subject}营收规模稳步增长，盈利能力保持稳定，财务结构合理。
            """
        }

        return default_contents.get(section_name, f"基于对{subject}的深入分析，我们认为该标的具有良好的投资价值。")

    def _add_formatted_text(self, doc, content: str):
        """添加格式化文本内容，清理Markdown语法"""
        # 先清理Markdown语法
        content = self._clean_markdown_syntax(content)

        # 处理JSON代码块，转换为表格
        content = self._convert_json_to_tables(doc, content)

        # 按段落分割内容
        paragraphs = content.split('\n\n')

        for para_text in paragraphs:
            para_text = para_text.strip()
            if not para_text:
                continue

            # 跳过已处理的JSON标记
            if para_text.startswith('```json') or para_text.startswith('```') or para_text == 'TABLE_INSERTED':
                continue

            # 检查是否是子标题
            if para_text.startswith('##'):
                doc.add_heading(para_text.replace('##', '').strip(), level=2)
            elif para_text.startswith('#'):
                doc.add_heading(para_text.replace('#', '').strip(), level=3)
            else:
                # 普通段落 - 不再处理markdown语法
                para = doc.add_paragraph()
                para.add_run(para_text)

    def _clean_markdown_syntax(self, content: str) -> str:
        """清理Markdown语法，转换为纯文本"""
        import re

        # 清理各种Markdown语法
        # 1. 清理粗体标记 **text** -> text
        content = re.sub(r'\*\*(.*?)\*\*', r'\1', content)

        # 2. 清理斜体标记 *text* -> text
        content = re.sub(r'\*(.*?)\*', r'\1', content)

        # 3. 清理代码块标记 ```code``` -> code
        content = re.sub(r'```[\w]*\n(.*?)\n```', r'\1', content, flags=re.DOTALL)

        # 4. 清理行内代码 `code` -> code
        content = re.sub(r'`(.*?)`', r'\1', content)

        # 5. 清理链接 [text](url) -> text
        content = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', content)

        # 6. 清理列表标记 - item -> item
        content = re.sub(r'^[\s]*[-\*\+]\s+', '', content, flags=re.MULTILINE)

        # 7. 清理数字列表 1. item -> item
        content = re.sub(r'^[\s]*\d+\.\s+', '', content, flags=re.MULTILINE)

        # 8. 清理标题标记但保留内容用于后续处理
        # 这里不清理#，因为后续还需要用它来识别标题

        # 9. 清理多余的空行
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)

        return content.strip()

    def _validate_and_complete_content(self, content: str, section_name: str) -> str:
        """验证内容完整性并补全缺失部分"""
        if not content or len(content.strip()) < 50:
            # 内容太短，生成默认内容
            return self._get_default_section_content(section_name)

        # 检查是否只有标题没有内容
        lines = content.strip().split('\n')
        content_lines = [line for line in lines if not line.strip().startswith('#') and line.strip()]

        if len(content_lines) < 3:
            # 内容行太少，补充默认内容
            return content + "\n\n" + self._get_default_section_content(section_name)

        return content



    def _convert_json_to_tables(self, doc: Document, content: str) -> str:
        """将JSON代码块转换为专业表格 - 优化性能版本"""
        # 限制处理的JSON数量，避免过多表格导致性能问题
        json_pattern = r'```json\s*\n(.*?)\n```'
        matches = re.findall(json_pattern, content, re.DOTALL)

        # 只处理前5个JSON，避免文档过于复杂
        processed_count = 0
        max_tables = 5

        for json_str in matches:
            if processed_count >= max_tables:
                # 超过限制，将剩余JSON转换为简单文本
                content = content.replace(f'```json\n{json_str}\n```',
                                        f'[数据表格] {json_str[:100]}...', 1)
                continue

            try:
                # 解析JSON
                data = json.loads(json_str.strip())

                # 检查数据复杂度，避免创建过大的表格
                if self._is_simple_table_data(data):
                    # 创建简化表格
                    self._create_simple_table(doc, data)
                    content = content.replace(f'```json\n{json_str}\n```', 'TABLE_INSERTED', 1)
                    processed_count += 1
                else:
                    # 复杂数据转换为简单文本描述
                    summary = self._summarize_complex_data(data)
                    content = content.replace(f'```json\n{json_str}\n```', summary, 1)

            except json.JSONDecodeError:
                # JSON解析失败，保持原样
                continue

        return content

    def _is_simple_table_data(self, data: Dict) -> bool:
        """判断是否是简单的表格数据"""
        if not isinstance(data, dict) or len(data) > 10:  # 限制列数
            return False

        # 检查是否所有值都是简单类型或简短列表
        for key, value in data.items():
            if isinstance(value, list):
                if len(value) > 20:  # 限制行数
                    return False
                # 检查列表元素是否都是简单类型
                if not all(isinstance(item, (str, int, float, bool)) for item in value):
                    return False
            elif not isinstance(value, (str, int, float, bool)):
                return False

        return True

    def _create_simple_table(self, doc: Document, data: Dict) -> None:
        """创建简化的表格，确保正确显示"""
        if not isinstance(data, dict) or not data:
            return

        keys = list(data.keys())
        first_value = data[keys[0]]

        if isinstance(first_value, list):
            # 数据表格格式 - 确保表头正确显示
            row_count = min(len(first_value), 20)  # 限制最大行数
            table = doc.add_table(rows=row_count + 1, cols=len(keys))
            table.style = 'Table Grid'
            table.alignment = WD_TABLE_ALIGNMENT.CENTER

            # 设置标题行 - 确保表头显示
            header_row = table.rows[0]
            for i, key in enumerate(keys):
                cell = header_row.cells[i]
                cell.text = str(key)
                # 设置表头样式
                paragraph = cell.paragraphs[0]
                paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                run = paragraph.runs[0] if paragraph.runs else paragraph.add_run(str(key))
                run.font.bold = True
                run.font.size = Pt(10)

            # 填充数据行
            for row_idx in range(row_count):
                table_row = table.rows[row_idx + 1]
                for col_idx, key in enumerate(keys):
                    cell = table_row.cells[col_idx]
                    value = data[key]
                    if isinstance(value, list) and row_idx < len(value):
                        cell.text = str(value[row_idx])
                    else:
                        cell.text = str(value)
                    # 设置数据行样式
                    paragraph = cell.paragraphs[0]
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
        else:
            # 键值对表格格式 - 两列表格
            table = doc.add_table(rows=len(data) + 1, cols=2)  # +1 for header
            table.style = 'Table Grid'
            table.alignment = WD_TABLE_ALIGNMENT.CENTER

            # 添加表头
            header_row = table.rows[0]
            header_row.cells[0].text = "项目"
            header_row.cells[1].text = "内容"

            # 设置表头样式
            for cell in header_row.cells:
                paragraph = cell.paragraphs[0]
                paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                run = paragraph.runs[0] if paragraph.runs else paragraph.add_run(cell.text)
                run.font.bold = True
                run.font.size = Pt(10)

            # 填充数据
            for i, (key, value) in enumerate(data.items()):
                row = table.rows[i + 1]  # +1 because of header
                row.cells[0].text = str(key)
                row.cells[1].text = str(value)

                # 设置数据行样式
                for cell in row.cells:
                    paragraph = cell.paragraphs[0]
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT

        # 添加表格后的空行
        doc.add_paragraph()

    def _summarize_complex_data(self, data: Dict) -> str:
        """将复杂数据总结为文本描述"""
        if isinstance(data, dict):
            keys = list(data.keys())[:5]  # 只显示前5个键
            summary = f"数据包含以下主要信息：{', '.join(keys)}"
            if len(data) > 5:
                summary += f"等共{len(data)}项数据"
            return f"[数据摘要] {summary}"
        else:
            return "[复杂数据] 详细数据已省略"

    def _create_professional_table(self, doc: Document, data: Dict) -> None:
        """创建专业格式的表格"""
        if not isinstance(data, dict):
            return

        # 获取表格数据
        keys = list(data.keys())
        if not keys:
            return

        # 确定表格结构
        first_value = data[keys[0]]
        if isinstance(first_value, list):
            # 数据表格格式：键作为列标题，值作为行数据
            self._create_data_table(doc, data)
        else:
            # 信息表格格式：键值对
            self._create_info_table(doc, data)

    def _create_data_table(self, doc: Document, data: Dict) -> None:
        """创建数据表格（列表数据）"""
        keys = list(data.keys())
        first_key = keys[0]
        row_count = len(data[first_key]) if isinstance(data[first_key], list) else 1

        # 创建表格：行数+1（标题行），列数为键的数量
        table = doc.add_table(rows=row_count + 1, cols=len(keys))
        table.style = 'Table Grid'
        table.alignment = WD_TABLE_ALIGNMENT.CENTER

        # 设置标题行
        header_row = table.rows[0]
        for i, key in enumerate(keys):
            cell = header_row.cells[i]
            cell.text = str(key)
            # 设置标题行格式
            self._format_table_cell(cell, is_header=True)

        # 填充数据行
        for row_idx in range(row_count):
            table_row = table.rows[row_idx + 1]
            for col_idx, key in enumerate(keys):
                cell = table_row.cells[col_idx]
                value = data[key]
                if isinstance(value, list) and row_idx < len(value):
                    cell.text = str(value[row_idx])
                else:
                    cell.text = str(value)
                # 设置数据行格式
                self._format_table_cell(cell, is_header=False)

        # 添加表格后的空行
        doc.add_paragraph()

    def _create_info_table(self, doc: Document, data: Dict) -> None:
        """创建信息表格（键值对）"""
        # 创建两列表格
        table = doc.add_table(rows=len(data), cols=2)
        table.style = 'Table Grid'
        table.alignment = WD_TABLE_ALIGNMENT.CENTER

        # 填充数据
        for i, (key, value) in enumerate(data.items()):
            row = table.rows[i]

            # 键列
            key_cell = row.cells[0]
            key_cell.text = str(key)
            self._format_table_cell(key_cell, is_header=True)

            # 值列
            value_cell = row.cells[1]
            if isinstance(value, (list, dict)):
                value_cell.text = str(value)
            else:
                value_cell.text = str(value)
            self._format_table_cell(value_cell, is_header=False)

        # 添加表格后的空行
        doc.add_paragraph()

    def _format_table_cell(self, cell, is_header: bool = False):
        """格式化表格单元格 - 简化版本提高性能"""
        try:
            # 简化格式设置，只设置基本样式
            paragraph = cell.paragraphs[0] if cell.paragraphs else cell.add_paragraph()

            if is_header:
                # 标题行：只设置粗体和居中
                paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                if paragraph.runs:
                    paragraph.runs[0].font.bold = True
                else:
                    run = paragraph.add_run(cell.text)
                    run.font.bold = True
            else:
                # 数据行：保持默认格式
                paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT

        except Exception as e:
            # 如果格式化失败，至少保证文本正确
            pass

    def _add_structured_content(self, doc, content: Dict[str, Any]):
        """添加结构化内容"""
        for key, value in content.items():
            # 添加子标题
            doc.add_heading(key, level=2)

            if isinstance(value, str):
                doc.add_paragraph(value)
            elif isinstance(value, list):
                self._add_list_content(doc, value)
            elif isinstance(value, dict):
                # 如果是表格数据
                if self._is_table_data(value):
                    self._add_table_from_dict(doc, value)
                else:
                    self._add_structured_content(doc, value)

    def _add_list_content(self, doc, content: List[Any]):
        """添加列表内容"""
        for item in content:
            if isinstance(item, str):
                para = doc.add_paragraph()
                para.style = 'List Bullet'
                para.add_run(item)
            elif isinstance(item, dict):
                # 如果是带标题的项目
                if 'title' in item and 'content' in item:
                    para = doc.add_paragraph()
                    para.add_run(f"{item['title']}：").bold = True
                    para.add_run(item['content'])
                else:
                    self._add_structured_content(doc, item)

    def _is_table_data(self, data: Dict[str, Any]) -> bool:
        """判断是否是表格数据"""
        # 简单判断：如果所有值都是列表且长度相同，认为是表格数据
        if not data:
            return False

        values = list(data.values())
        if not all(isinstance(v, list) for v in values):
            return False

        first_length = len(values[0])
        return all(len(v) == first_length for v in values)

    def _add_table_from_dict(self, doc, data: Dict[str, Any]):
        """从字典数据创建表格"""
        if not data:
            return

        headers = list(data.keys())
        rows_data = list(data.values())
        num_rows = len(rows_data[0]) if rows_data else 0

        if num_rows == 0:
            return

        # 创建表格
        table = doc.add_table(rows=num_rows + 1, cols=len(headers))
        table.style = 'Table Grid'
        table.alignment = WD_TABLE_ALIGNMENT.CENTER

        # 添加表头
        hdr_cells = table.rows[0].cells
        for i, header in enumerate(headers):
            hdr_cells[i].text = str(header)
            # 安全设置粗体
            self._safe_set_cell_bold(hdr_cells[i])

        # 添加数据行
        for row_idx in range(num_rows):
            row_cells = table.rows[row_idx + 1].cells
            for col_idx, col_data in enumerate(rows_data):
                if row_idx < len(col_data):
                    row_cells[col_idx].text = str(col_data[row_idx])

    def _add_section_charts(self, doc, data: Dict[str, Any], section_name: str):
        """为章节添加相关图表 - 优化性能版本"""
        # 跳过图表生成以提高性能，改为添加图表占位符
        if section_name in ["财务分析", "市场规模分析", "竞争格局分析"]:
            try:
                # 添加图表占位符而不是实际生成图表
                self._add_chart_placeholder(doc, section_name)
            except Exception as e:
                self.logger.warning(f"添加{section_name}图表占位符失败: {str(e)}")

    def _add_chart_placeholder(self, doc, section_name: str):
        """添加图表占位符，避免耗时的图表生成"""
        placeholder_text = {
            "财务分析": "[图表] 主要财务指标趋势图 - 显示营业收入、净利润等关键指标的历史趋势",
            "市场规模分析": "[图表] 市场规模增长趋势图 - 展示行业市场规模的历史和预测数据",
            "竞争格局分析": "[图表] 市场份额分布图 - 显示主要竞争对手的市场占有率"
        }

        placeholder = placeholder_text.get(section_name, f"[图表] {section_name}相关图表")

        # 添加图表占位符段落
        chart_para = doc.add_paragraph(placeholder)
        chart_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 设置占位符样式
        for run in chart_para.runs:
            run.font.size = Pt(10)
            run.font.italic = True
            run.font.color.rgb = RGBColor(128, 128, 128)  # 灰色

        # 添加空行
        doc.add_paragraph()

    def _generate_chart_from_data(self, chart_data: Dict[str, Any], section_name: str) -> Optional[io.BytesIO]:
        """根据数据生成图表"""
        chart_type = chart_data.get('type', 'line')

        fig, ax = plt.subplots(figsize=(10, 6))

        if chart_type == 'line':
            x_data = chart_data.get('x', [])
            y_data = chart_data.get('y', [])
            ax.plot(x_data, y_data, linewidth=2)

        elif chart_type == 'bar':
            x_data = chart_data.get('x', [])
            y_data = chart_data.get('y', [])
            ax.bar(x_data, y_data)

        elif chart_type == 'pie':
            labels = chart_data.get('labels', [])
            sizes = chart_data.get('sizes', [])
            ax.pie(sizes, labels=labels, autopct='%1.1f%%')

        ax.set_title(chart_data.get('title', section_name), fontsize=14, fontweight='bold')

        if 'xlabel' in chart_data:
            ax.set_xlabel(chart_data['xlabel'])
        if 'ylabel' in chart_data:
            ax.set_ylabel(chart_data['ylabel'])

        plt.tight_layout()

        # 保存到内存
        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format='png', dpi=300, bbox_inches='tight')
        img_buffer.seek(0)
        plt.close()

        return img_buffer

    def _generate_default_chart(self, section_name: str) -> Optional[io.BytesIO]:
        """生成默认示例图表"""
        fig, ax = plt.subplots(figsize=(10, 6))

        if section_name == "财务分析":
            # 财务指标趋势图
            years = ['2021', '2022', '2023', '2024E']
            revenue = [100, 120, 140, 160]
            profit = [10, 15, 20, 25]

            ax.plot(years, revenue, marker='o', label='营业收入', linewidth=2)
            ax.plot(years, profit, marker='s', label='净利润', linewidth=2)
            ax.set_title('主要财务指标趋势', fontsize=14, fontweight='bold')
            ax.set_ylabel('金额（亿元）')
            ax.legend()
            ax.grid(True, alpha=0.3)

        elif section_name == "市场规模分析":
            # 市场规模增长图
            years = np.arange(2020, 2026)
            market_size = [50, 65, 85, 110, 140, 180]

            ax.bar(years, market_size, color='skyblue', alpha=0.7)
            ax.set_title('市场规模增长趋势', fontsize=14, fontweight='bold')
            ax.set_xlabel('年份')
            ax.set_ylabel('市场规模（亿元）')
            ax.grid(True, alpha=0.3)

        elif section_name == "竞争格局分析":
            # 市场份额饼图
            labels = ['公司A', '公司B', '公司C', '其他']
            sizes = [30, 25, 20, 25]
            colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99']

            ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            ax.set_title('市场份额分布', fontsize=14, fontweight='bold')

        plt.tight_layout()

        # 保存到内存
        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format='png', dpi=300, bbox_inches='tight')
        img_buffer.seek(0)
        plt.close()

        return img_buffer

    def _safe_set_cell_bold(self, cell):
        """安全地设置单元格文本为粗体"""
        try:
            # 确保单元格有文本和段落
            if not cell.text:
                return

            para = cell.paragraphs[0] if cell.paragraphs else cell.add_paragraph()

            # 清除现有内容并添加粗体文本
            text = cell.text
            para.clear()
            run = para.add_run(text)
            run.font.bold = True

        except (IndexError, AttributeError):
            # 如果出错，至少确保文本存在
            pass

    def _add_chart_to_doc(self, doc, chart_buffer, caption: str):
        """将图表添加到文档中"""
        try:
            # 添加图表
            paragraph = doc.add_paragraph()
            run = paragraph.add_run()
            run.add_picture(chart_buffer, width=Inches(6))
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # 添加图表标题
            caption_para = doc.add_paragraph(f"图 {caption}")
            caption_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # 设置标题样式
            for run in caption_para.runs:
                run.font.size = Pt(9)
                run.font.bold = True

        except Exception as e:
            self.logger.error(f"添加图表失败: {str(e)}")
            # 添加错误提示
            error_para = doc.add_paragraph(f"图表生成失败: {caption}")
            error_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

    def _add_appendix(self, doc, data: Dict[str, Any]):
        """添加附录"""
        doc.add_heading('附录', level=1)

        # 数据来源说明
        doc.add_heading('数据来源', level=2)
        data_sources = data.get('data_sources', [
            "公司公告及财务报表",
            "行业研究报告",
            "公开市场数据",
            "政府统计数据",
            "第三方研究机构报告"
        ])

        for source in data_sources:
            para = doc.add_paragraph()
            para.style = 'List Bullet'
            para.add_run(source)

        # 分析师信息
        doc.add_heading('分析师信息', level=2)
        analyst_info = data.get('analyst_info',
            "本报告由AI智能分析系统生成，结合了多种数据源和分析模型。"
            "如有疑问，请联系相关投资顾问。"
        )
        doc.add_paragraph(analyst_info)

        # 免责声明
        doc.add_heading('免责声明', level=2)
        disclaimer = data.get('disclaimer',
            "本报告仅供参考，不构成投资建议。投资者应根据自身情况做出投资决策，"
            "并承担相应的投资风险。"
        )
        doc.add_paragraph(disclaimer)
