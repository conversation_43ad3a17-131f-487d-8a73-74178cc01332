#!/usr/bin/env python3
"""
修复语法错误脚本：恢复被破坏的文件
"""

import os
import re
import glob
import ast

def check_syntax(file_path):
    """检查Python文件语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        ast.parse(content)
        return True, None
    except SyntaxError as e:
        return False, str(e)
    except Exception as e:
        return False, str(e)

def fix_syntax_errors(file_path):
    """修复语法错误"""
    print(f"🔧 修复语法错误: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 1. 修复缩进错误
    lines = content.split('\n')
    fixed_lines = []
    
    for i, line in enumerate(lines):
        # 修复多余的缩进
        if '# 全部同步调用' in line and line.startswith('                '):
            # 减少缩进
            fixed_line = line.replace('                ', '            ')
            fixed_lines.append(fixed_line)
        elif 'result = func(*args, **kwargs)' in line and line.startswith('                    '):
            # 修复result行的缩进
            fixed_line = line.replace('                    ', '                ')
            fixed_lines.append(fixed_line)
        else:
            fixed_lines.append(line)
    
    content = '\n'.join(fixed_lines)
    
    # 2. 修复语法错误
    fixes = [
        # 修复except语句
        (r'except Exception  # 替换asyncio\.TimeoutError:', 'except Exception:'),
        
        # 修复None注释
        (r'None  # 移除[^,\n]*', 'None'),
        
        # 修复破损的函数调用
        (r'results = None[^,\n]*\n\s*return results', 'results = []\n            return results'),
        
        # 修复破损的import
        (r'import None[^,\n]*', ''),
        
        # 修复破损的赋值
        (r'= None  # 移除[^,\n]*', '= None'),
        
        # 修复多余的空行和注释
        (r'\n\s*# 移除[^\n]*\n', '\n'),
        
        # 修复破损的条件语句
        (r'if False  # 移除[^:]*:', 'if False:'),
        
        # 修复破损的循环
        (r'for [^:]*None  # 移除[^:]*:', 'for item in []:'),
    ]
    
    for pattern, replacement in fixes:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
    
    # 3. 特殊修复
    if 'base_agent.py' in file_path:
        # 修复parallel_execute方法
        content = re.sub(
            r'task_coroutines = \[[^\]]*\]',
            'results = []',
            content,
            flags=re.MULTILINE | re.DOTALL
        )
        
        # 修复execute_single_task
        content = re.sub(
            r'def execute_single_task\(func, args, kwargs\):\s*try:\s*# 全部同步调用\s*return func\(\*args, \*\*kwargs\)',
            '''def execute_single_task(func, args, kwargs):
            try:
                return func(*args, **kwargs)''',
            content,
            flags=re.MULTILINE | re.DOTALL
        )
    
    # 4. 检查修复后的语法
    try:
        ast.parse(content)
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 语法修复完成: {file_path}")
            return True
        else:
            print(f"⏭️  无需修复: {file_path}")
            return False
    except SyntaxError as e:
        print(f"❌ 语法修复失败: {file_path} - {e}")
        return False

def restore_from_backup(file_path):
    """从备份恢复文件"""
    backup_path = f"{file_path}.backup"
    if os.path.exists(backup_path):
        print(f"🔄 从备份恢复: {file_path}")
        with open(backup_path, 'r', encoding='utf-8') as f:
            backup_content = f.read()
        
        # 简单地移除asyncio相关内容
        content = backup_content
        content = re.sub(r'import asyncio\n?', '', content)
        content = re.sub(r'async def ', 'def ', content)
        content = re.sub(r'await ', '', content)
        content = re.sub(r'asyncio\.sleep\(', 'time.sleep(', content)
        
        # 确保有time import
        if 'time.sleep(' in content and 'import time' not in content:
            content = 'import time\n' + content
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    return False

def main():
    """主函数"""
    print("🚀 开始修复语法错误...")
    
    # 被修复的文件列表
    broken_files = [
        'src/agents/financial_agent.py',
        'src/agents/base_agent.py',
        'src/tools/url_content_tool.py',
        'src/tools/professional_docx_generator.py',
        'src/tools/web_search_tool.py',
        'src/tools/enterprise_docx_generator.py',
        'src/utils/logger.py',
        'src/utils/data_processor.py'
    ]
    
    fixed_count = 0
    
    for file_path in broken_files:
        if os.path.exists(file_path):
            # 检查语法
            is_valid, error = check_syntax(file_path)
            if not is_valid:
                print(f"❌ 语法错误: {file_path} - {error}")
                
                # 尝试修复
                if not fix_syntax_errors(file_path):
                    # 修复失败，尝试从备份恢复
                    if restore_from_backup(file_path):
                        print(f"✅ 从备份恢复成功: {file_path}")
                        fixed_count += 1
                    else:
                        print(f"💥 无法修复: {file_path}")
                else:
                    fixed_count += 1
            else:
                print(f"✅ 语法正确: {file_path}")
    
    print(f"\n📊 语法修复统计:")
    print(f"处理文件数: {len(broken_files)}")
    print(f"修复文件数: {fixed_count}")
    
    print("\n🎉 语法修复完成！")

if __name__ == "__main__":
    main()
