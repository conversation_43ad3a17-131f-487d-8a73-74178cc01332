# src/tools/function_calling_manager.py
import asyncio
import json
from typing import Dict, Any, List, Optional, Callable, Union
from src.models.llm_client import LLMClient
from src.utils.logger import get_logger
from config.settings import Config

class FunctionCallingManager:
    """
    Function Calling管理器 - 统一管理工具调用和LLM交互
    """
    
    def __init__(self, config: Config = None):
        self.config = config or Config()
        self.llm_client = LLMClient(self.config)
        self.logger = get_logger("FunctionCallingManager")
        
        # 注册的工具函数
        self.tool_functions: Dict[str, Callable] = {}
        
        # 工具定义
        self.tool_definitions: List[Dict[str, Any]] = []
        
        self.logger.info("FunctionCallingManager 初始化完成")
    
    def register_tool(self, name: str, function: Callable, description: str, 
                     parameters: Dict[str, Any] = None):
        """
        注册工具函数
        
        Args:
            name: 工具名称
            function: 工具函数
            description: 工具描述
            parameters: 参数定义（JSON Schema格式）
        """
        self.tool_functions[name] = function
        
        tool_def = {
            "type": "function",
            "function": {
                "name": name,
                "description": description,
                "parameters": parameters or {"type": "object", "properties": {}}
            }
        }
        
        # 检查是否已存在，如果存在则更新
        existing_index = None
        for i, tool in enumerate(self.tool_definitions):
            if tool["function"]["name"] == name:
                existing_index = i
                break
        
        if existing_index is not None:
            self.tool_definitions[existing_index] = tool_def
        else:
            self.tool_definitions.append(tool_def)
        
        self.logger.info(f"注册工具: {name}")
    
    def unregister_tool(self, name: str):
        """注销工具"""
        if name in self.tool_functions:
            del self.tool_functions[name]
        
        self.tool_definitions = [
            tool for tool in self.tool_definitions 
            if tool["function"]["name"] != name
        ]
        
        self.logger.info(f"注销工具: {name}")
    
    def get_tool_definitions(self) -> List[Dict[str, Any]]:
        """获取所有工具定义"""
        return self.tool_definitions.copy()
    
    def get_registered_tools(self) -> List[str]:
        """获取已注册的工具名称列表"""
        return list(self.tool_functions.keys())
    
    async def execute_tool(self, tool_name: str, arguments: Union[str, Dict[str, Any]]) -> Any:
        """
        执行工具函数
        
        Args:
            tool_name: 工具名称
            arguments: 工具参数（JSON字符串或字典）
            
        Returns:
            工具执行结果
        """
        if tool_name not in self.tool_functions:
            raise ValueError(f"未注册的工具: {tool_name}")
        
        # 解析参数
        if isinstance(arguments, str):
            try:
                args = json.loads(arguments)
            except json.JSONDecodeError as e:
                raise ValueError(f"参数解析失败: {e}")
        else:
            args = arguments
        
        # 执行工具函数
        try:
            function = self.tool_functions[tool_name]
            
            if asyncio.iscoroutinefunction(function):
                result = await function(**args)
            else:
                result = function(**args)
            
            self.logger.info(f"工具 {tool_name} 执行成功")
            return result
            
        except Exception as e:
            error_msg = f"工具 {tool_name} 执行失败: {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)
    
    async def chat_with_tools(self, messages: List[Dict[str, str]], 
                             max_iterations: int = 5, **kwargs) -> Dict[str, Any]:
        """
        与LLM对话并自动处理工具调用
        
        Args:
            messages: 对话消息列表
            max_iterations: 最大工具调用迭代次数
            **kwargs: 传递给LLM的其他参数
            
        Returns:
            最终的对话结果
        """
        if not self.tool_definitions:
            # 没有注册工具，直接调用LLM
            return await self.llm_client.async_chat_completion(messages, **kwargs)
        
        current_messages = messages.copy()
        iteration = 0
        tool_call_history = []
        
        while iteration < max_iterations:
            self.logger.info(f"开始第 {iteration + 1} 轮对话")
            
            # 调用LLM
            response = await self.llm_client.async_chat_completion(
                messages=current_messages,
                tools=self.tool_definitions,
                **kwargs
            )
            
            if not response.get("success"):
                return response
            
            # 检查是否有工具调用
            tool_calls = response.get("tool_calls", [])
            
            if not tool_calls:
                # 没有工具调用，返回最终结果
                self.logger.info("对话完成，无需工具调用")
                return {
                    **response,
                    "tool_call_history": tool_call_history,
                    "total_iterations": iteration + 1
                }
            
            # 添加assistant消息到对话历史
            assistant_message = {
                "role": "assistant",
                "content": response.get("content", "") or "",
                "tool_calls": tool_calls
            }
            current_messages.append(assistant_message)
            
            # 执行所有工具调用
            for tool_call in tool_calls:
                tool_name = tool_call["function"]["name"]
                tool_args = tool_call["function"]["arguments"]
                tool_id = tool_call["id"]
                
                self.logger.info(f"执行工具: {tool_name}")
                
                try:
                    # 执行工具
                    tool_result = await self.execute_tool(tool_name, tool_args)
                    
                    # 记录工具调用历史
                    tool_call_history.append({
                        "iteration": iteration + 1,
                        "tool_name": tool_name,
                        "arguments": tool_args,
                        "result": str(tool_result),
                        "success": True
                    })
                    
                    # 添加工具结果到对话历史
                    tool_message = {
                        "role": "tool",
                        "content": str(tool_result),
                        "tool_call_id": tool_id
                    }
                    current_messages.append(tool_message)
                    
                except Exception as e:
                    error_msg = f"工具调用失败: {str(e)}"
                    self.logger.error(error_msg)
                    
                    # 记录失败的工具调用
                    tool_call_history.append({
                        "iteration": iteration + 1,
                        "tool_name": tool_name,
                        "arguments": tool_args,
                        "error": str(e),
                        "success": False
                    })
                    
                    # 添加错误消息到对话历史
                    error_message = {
                        "role": "tool",
                        "content": error_msg,
                        "tool_call_id": tool_id
                    }
                    current_messages.append(error_message)
            
            iteration += 1
        
        # 达到最大迭代次数
        self.logger.warning(f"达到最大迭代次数 {max_iterations}")
        return {
            "content": "达到最大工具调用迭代次数，对话可能未完成",
            "success": False,
            "error": "max_iterations_reached",
            "tool_call_history": tool_call_history,
            "total_iterations": iteration,
            "messages": current_messages
        }
    
    async def simple_tool_call(self, tool_name: str, **kwargs) -> Any:
        """
        简单的工具调用（不通过LLM）
        
        Args:
            tool_name: 工具名称
            **kwargs: 工具参数
            
        Returns:
            工具执行结果
        """
        return await self.execute_tool(tool_name, kwargs)
    
    def create_tool_summary(self) -> str:
        """创建工具摘要信息"""
        if not self.tool_definitions:
            return "当前没有注册任何工具"
        
        summary = f"已注册 {len(self.tool_definitions)} 个工具:\n"
        for tool in self.tool_definitions:
            func_info = tool["function"]
            summary += f"- {func_info['name']}: {func_info['description']}\n"
        
        return summary
