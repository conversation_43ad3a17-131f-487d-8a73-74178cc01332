2025-06-19 01:55:42 - FinancialReportSystem - ERROR - logger.py:120 - error - 生成company报告失败: asyncio.run() cannot be called from a running event loop - Exception: asyncio.run() cannot be called from a running event loop
Traceback (most recent call last):
  File "/home/<USER>/agent/financial_report_agent/main.py", line 85, in generate_single_report
    report_path = await self.company_agent.run(company_name, stock_code)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/agent/financial_report_agent/src/agents/company_agent.py", line 88, in run
    raise e
  File "/home/<USER>/agent/financial_report_agent/src/agents/company_agent.py", line 81, in run
    report_path = await self._generate_company_report(analysis_result)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/agent/financial_report_agent/src/agents/company_agent.py", line 310, in _generate_company_report
    professional_data = self._build_professional_report_data(analysis_result)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/agent/financial_report_agent/src/agents/company_agent.py", line 333, in _build_professional_report_data
    professional_data = asyncio.run(self._generate_professional_content(
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/py312/lib/python3.12/asyncio/runners.py", line 191, in run
    raise RuntimeError(
RuntimeError: asyncio.run() cannot be called from a running event loop
2025-06-19 02:47:47 - FinancialReportSystem - ERROR - logger.py:120 - error - 生成company报告失败: name 're' is not defined - Exception: name 're' is not defined
Traceback (most recent call last):
  File "/home/<USER>/agent/financial_report_agent/main.py", line 85, in generate_single_report
    report_path = await self.company_agent.run(company_name, stock_code)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/agent/financial_report_agent/src/agents/company_agent.py", line 221, in run
    raise e
  File "/home/<USER>/agent/financial_report_agent/src/agents/company_agent.py", line 209, in run
    report_path = await self._generate_company_report(analysis_result)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/agent/financial_report_agent/src/agents/company_agent.py", line 537, in _generate_company_report
    report_path = await loop.run_in_executor(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/py312/lib/python3.12/concurrent/futures/thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/agent/financial_report_agent/src/tools/professional_docx_generator.py", line 116, in create_company_report
    return self._create_report("company", data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/agent/financial_report_agent/src/tools/professional_docx_generator.py", line 149, in _create_report
    self._add_report_sections(doc, data, structure['sections'])
  File "/home/<USER>/agent/financial_report_agent/src/tools/professional_docx_generator.py", line 311, in _add_report_sections
    self._add_formatted_text(doc, section_content)
  File "/home/<USER>/agent/financial_report_agent/src/tools/professional_docx_generator.py", line 379, in _add_formatted_text
    content = self._convert_json_to_tables(doc, content)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/agent/financial_report_agent/src/tools/professional_docx_generator.py", line 415, in _convert_json_to_tables
    matches = re.findall(json_pattern, content, re.DOTALL)
              ^^
NameError: name 're' is not defined. Did you forget to import 're'
2025-06-19 16:10:16 - FinancialReportSystem - ERROR - logger.py:120 - error - 生成company报告失败: maximum recursion depth exceeded - Exception: maximum recursion depth exceeded
Traceback (most recent call last):
  File "/home/<USER>/agent/financial_report_agent/main.py", line 85, in generate_single_report
    report_path = await self.company_agent.run(company_name, stock_code)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/agent/financial_report_agent/src/agents/company_agent.py", line 256, in run
    raise e
  File "/home/<USER>/agent/financial_report_agent/src/agents/company_agent.py", line 238, in run
    analysis_result = self._analyze_company_data(company_name, stock_code)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/agent/financial_report_agent/src/agents/company_agent.py", line 466, in _analyze_company_data
    final_analysis = self._generate_final_analysis(company_name, stock_code, conversation_log)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/agent/financial_report_agent/src/agents/company_agent.py", line 531, in _generate_final_analysis
    section_content = self._generate_section_content_from_conversation(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/agent/financial_report_agent/src/agents/company_agent.py", line 734, in _generate_section_content_from_conversation
    response = self.llm_client.chat_completion([
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/agent/financial_report_agent/src/models/llm_client.py", line 117, in chat_completion
    return self.chat_completion(messages, model, tools, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/agent/financial_report_agent/src/models/llm_client.py", line 117, in chat_completion
    return self.chat_completion(messages, model, tools, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/agent/financial_report_agent/src/models/llm_client.py", line 117, in chat_completion
    return self.chat_completion(messages, model, tools, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  [Previous line repeated 2984 more times]
RecursionError: maximum recursion depth exceeded
