#!/usr/bin/env python3
"""
运行脚本：生成竞赛要求的三份报告
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.agents.financial_agent import FinancialReportAgent
from config.settings import Config

async def generate_competition_reports():
    """生成比赛要求的报告"""
    
    # A榜目标
    targets = {
        "company": "商汤科技（00020.HK）",
        "industry": "智能风控&大数据征信服务",
        "macro": "生成式AI基建与算力投资趋势（2023-2026）"
    }
    
    print("=" * 60)
    print("金融研报AI Agent系统 - 比赛版本")
    print("=" * 60)
    print(f"目标公司: {targets['company']}")
    print(f"目标行业: {targets['industry']}")
    print(f"宏观主题: {targets['macro']}")
    print("=" * 60)
    
    # 创建Agent
    config = Config()
    agent = FinancialReportAgent(config)
    
    print("正在初始化系统...")
    print("正在搜集信息...")
    
    try:
        # 生成报告
        report_paths = await agent.generate_reports(targets)
        
        print("\n" + "=" * 60)
        print("报告生成完成！")
        print("=" * 60)
        
        success_count = 0
        total_count = len(targets)
        
        for report_type, path in report_paths.items():
            if isinstance(path, str) and os.path.exists(path):
                print(f"✅ {report_type.upper()}报告: {path}")
                success_count += 1
            else:
                print(f"❌ {report_type.upper()}报告: 生成失败")
        
        print("=" * 60)
        print(f"成功生成 {success_count}/{total_count} 份报告")
        
        if success_count == total_count:
            print("🎉 所有报告生成成功！可以提交到比赛平台了！")
        else:
            print("⚠️  部分报告生成失败，请检查日志")
            
        print("=" * 60)
        
        return report_paths
        
    except Exception as e:
        print(f"❌ 生成报告时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    asyncio.run(generate_competition_reports())
