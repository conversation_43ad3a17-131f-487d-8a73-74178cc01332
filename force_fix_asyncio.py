#!/usr/bin/env python3
"""
强制修复脚本：彻底清理所有asyncio
"""

import os
import re
import glob

def force_fix_file(file_path):
    """强制修复单个文件"""
    print(f"🔧 强制修复文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 1. 删除所有asyncio相关的import
    content = re.sub(r'import asyncio\n?', '', content)
    content = re.sub(r'from asyncio import.*?\n', '', content)
    
    # 2. 替换所有asyncio.xxx调用
    replacements = [
        (r'asyncio\.gather\([^)]+\)', 'None  # 移除asyncio.gather'),
        (r'asyncio\.get_event_loop\(\)', 'None  # 移除asyncio.get_event_loop'),
        (r'asyncio\.wait_for\([^)]+\)', 'None  # 移除asyncio.wait_for'),
        (r'asyncio\.TimeoutError', 'Exception  # 替换asyncio.TimeoutError'),
        (r'asyncio\.iscoroutinefunction\([^)]+\)', 'False  # 移除asyncio.iscoroutinefunction'),
        (r'loop\.run_in_executor\([^)]+\)', 'None  # 移除run_in_executor'),
        (r'loop = None  # 移除asyncio\.get_event_loop', '# 移除event loop'),
    ]
    
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
    
    # 3. 修复特定的问题模式
    # 修复financial_agent.py中的并发执行
    if 'financial_agent.py' in file_path:
        content = re.sub(
            r'# 并发执行\s*results = None  # 移除asyncio\.gather',
            '# 顺序执行\n        results = []',
            content
        )
        
        # 修复搜索任务
        content = re.sub(
            r'search_tasks = \[\s*None  # 移除asyncio\.get_event_loop[^]]+\]',
            'search_results = []',
            content,
            flags=re.MULTILINE | re.DOTALL
        )
        
        # 修复内容任务
        content = re.sub(
            r'content_tasks = \[\s*None  # 移除asyncio\.get_event_loop[^]]+\]',
            'contents = []',
            content,
            flags=re.MULTILINE | re.DOTALL
        )
    
    # 4. 修复base_agent.py中的问题
    if 'base_agent.py' in file_path:
        content = re.sub(
            r'if False  # 移除asyncio\.iscoroutinefunction:.*?else:',
            '# 全部同步调用',
            content,
            flags=re.MULTILINE | re.DOTALL
        )
    
    # 5. 修复function_calling_manager.py
    if 'function_calling_manager.py' in file_path:
        content = re.sub(
            r'if False  # 移除asyncio\.iscoroutinefunction:.*?else:',
            '# 全部同步调用',
            content,
            flags=re.MULTILINE | re.DOTALL
        )
    
    # 6. 修复logger.py
    if 'logger.py' in file_path:
        content = re.sub(
            r'import asyncio\s*if False  # 移除asyncio\.iscoroutinefunction.*?else:',
            '# 全部同步调用',
            content,
            flags=re.MULTILINE | re.DOTALL
        )
    
    # 7. 清理多余的注释和空行
    content = re.sub(r'\n\s*# 移除.*?\n', '\n', content)
    content = re.sub(r'\n\s*None  # 移除.*?\n', '\n', content)
    
    # 检查是否有修改
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 强制修复完成: {file_path}")
        return True
    else:
        print(f"⏭️  无需修改: {file_path}")
        return False

def main():
    """主函数"""
    print("🚀 开始强制修复所有asyncio问题...")
    
    # 要修复的文件模式
    patterns = [
        'src/agents/*.py',
        'src/models/*.py',
        'src/tools/*.py',
        'src/utils/*.py',
        'main.py'
    ]
    
    fixed_count = 0
    total_count = 0
    
    for pattern in patterns:
        files = glob.glob(pattern)
        for file_path in files:
            if os.path.isfile(file_path):
                total_count += 1
                if force_fix_file(file_path):
                    fixed_count += 1
    
    print(f"\n📊 强制修复完成统计:")
    print(f"总文件数: {total_count}")
    print(f"修复文件数: {fixed_count}")
    print(f"无需修改: {total_count - fixed_count}")
    
    print("\n🎉 强制修复完成！asyncio已被彻底清除！")

if __name__ == "__main__":
    main()
