#!/usr/bin/env python3
# test_search.py
"""
测试搜索功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.tools.web_search_tool import WebSearchTool
from config.settings import Config

def test_search():
    """测试搜索功能"""
    print("🔍 测试搜索功能")
    print("=" * 40)
    
    # 初始化配置和搜索工具
    config = Config()
    search_tool = WebSearchTool(config)
    
    # 测试查询
    test_queries = [
        "商汤科技 公司信息",
        "智能风控 行业分析",
        "生成式AI 投资趋势"
    ]
    
    for query in test_queries:
        print(f"\n🔍 搜索: {query}")
        print("-" * 30)
        
        try:
            results = search_tool.search(query, num_results=3)
            
            if results:
                print(f"✅ 找到 {len(results)} 个结果:")
                for i, result in enumerate(results, 1):
                    print(f"  {i}. {result.get('title', '无标题')}")
                    print(f"     URL: {result.get('url', '无URL')}")
                    if 'snippet' in result:
                        print(f"     摘要: {result['snippet'][:100]}...")
                    print()
            else:
                print("❌ 没有找到结果")
                
        except Exception as e:
            print(f"💥 搜索失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_search()
