# src/agents/industry_agent.py
from typing import Dict, Any, List
from tqdm import tqdm
import time
import asyncio
import asyncio
import asyncio
from src.agents.base_agent import BaseAgent
from src.tools.web_search_tool import WebSearchTool
from src.tools.url_content_tool import URLContentTool
from src.tools.rag_system import RAGSystem
from src.tools.enterprise_docx_generator import EnterpriseDocxGenerator
from src.tools.professional_docx_generator import ProfessionalFinancialReportGenerator
from src.models.llm_client import LLMClient
from src.utils.data_processor import DataProcessor
from config.settings import Config

class IndustryAgent(BaseAgent):
    """
    行业研报生成Agent - 专门负责生成行业研究报告
    """
    
    def __init__(self, config: Config = None):
        super().__init__(config, "IndustryAgent")
        
        # 初始化工具
        self.llm_client = LLMClient(self.config)
        self.web_search_tool = WebSearchTool(self.config)
        self.url_content_tool = URLContentTool(self.config)
        self.rag_system = RAGSystem(self.config)
        self.docx_generator = EnterpriseDocxGenerator(self.config)
        self.professional_docx_generator = ProfessionalFinancialReportGenerator(self.config)
        self.data_processor = DataProcessor(self.config)
        
        # 行业分析相关配置 - 扩展搜索查询以获取更丰富的信息
        self.industry_search_queries = [
            # 基础行业信息
            "{industry_name} 行业概况 基本情况 发展历程",
            "{industry_name} 行业分类 细分领域 产业结构",
            "{industry_name} 监管政策 主管部门 准入门槛",

            # 市场规模分析
            "{industry_name} 市场规模 2023 市场容量",
            "{industry_name} 增长率 历史数据 发展速度",
            "{industry_name} 区域分布 地域特征 市场集中度",
            "{industry_name} 需求分析 消费结构 用户画像",

            # 竞争格局分析
            "{industry_name} 竞争格局 主要企业 市场份额",
            "{industry_name} 龙头企业 行业领导者 竞争优势",
            "{industry_name} 新进入者 创新企业 独角兽",
            "{industry_name} 竞争壁垒 进入门槛 护城河",

            # 技术发展趋势
            "{industry_name} 技术创新 前沿技术 研发投入",
            "{industry_name} 数字化转型 智能化升级 新技术应用",
            "{industry_name} 专利分析 技术壁垒 创新能力",
            "{industry_name} 技术标准 行业规范 标准制定",

            # 政策环境分析
            "{industry_name} 政策支持 国家政策 地方政策",
            "{industry_name} 监管变化 合规要求 政策风险",
            "{industry_name} 税收政策 财政支持 优惠措施",
            "{industry_name} 环保政策 可持续发展 绿色转型",

            # 产业链分析
            "{industry_name} 产业链分析 上游供应商 下游客户",
            "{industry_name} 供应链管理 原材料 成本结构",
            "{industry_name} 渠道分析 销售模式 分销网络",
            "{industry_name} 价值链分析 利润分配 价值创造",

            # 投资机会分析
            "{industry_name} 投资机会 投资热点 资本流向",
            "{industry_name} 并购重组 产业整合 投资案例",
            "{industry_name} 估值水平 投资回报 风险收益",
            "{industry_name} 投资策略 配置建议 投资逻辑",

            # 风险评估
            "{industry_name} 行业风险 经营风险 市场风险",
            "{industry_name} 政策风险 技术风险 竞争风险",
            "{industry_name} 周期性风险 宏观风险 系统性风险",

            # 发展前景
            "{industry_name} 发展前景 未来趋势 增长预测",
            "{industry_name} 国际对比 全球市场 海外拓展",
            "{industry_name} 新兴机会 蓝海市场 增长点"
        ]
        
        # 初始化工具函数映射
        self.tool_functions = {
            "web_search": self._tool_web_search,
            "get_url_content": self._tool_get_url_content,
            "search_knowledge_base": self._tool_search_knowledge_base,
            "add_to_knowledge_base": self._tool_add_to_knowledge_base,
            "analyze_market_data": self._tool_analyze_market_data
        }

        # 工具定义
        self.tools = [
            {
                "type": "function",
                "function": {
                    "name": "web_search",
                    "description": "搜索网络信息，获取行业相关资料",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "搜索查询词"},
                            "num_results": {"type": "integer", "description": "返回结果数量，默认5", "default": 5}
                        },
                        "required": ["query"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "get_url_content",
                    "description": "获取指定URL的详细内容",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "url": {"type": "string", "description": "要获取内容的URL"}
                        },
                        "required": ["url"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "search_knowledge_base",
                    "description": "从知识库中搜索相关信息",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "搜索查询词"}
                        },
                        "required": ["query"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "analyze_market_data",
                    "description": "分析市场数据并生成洞察",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "data": {"type": "string", "description": "市场数据内容"},
                            "analysis_type": {"type": "string", "description": "分析类型", "enum": ["market_size", "competition", "trends", "comprehensive"]}
                        },
                        "required": ["data"]
                    }
                }
            }
        ]

        self.logger.info("IndustryAgent 初始化完成")
    
    async def run(self, industry_name: str, region: str = "中国") -> str:
        """
        生成行业研究报告

        Args:
            industry_name: 行业名称
            region: 区域范围（默认为中国）

        Returns:
            生成的报告文件路径
        """
        self.start_task(f"生成行业研报 - {industry_name}")

        # 创建进度条
        total_steps = 5
        progress_bar = tqdm(total=total_steps, desc=f"🏭 生成{industry_name}行业研报",
                           bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}]')

        try:
            # 验证输入
            if not self.validate_input(industry_name):
                raise ValueError("行业名称不能为空")

            # 第一步：行业信息收集
            progress_bar.set_description(f"🔍 收集{industry_name}行业信息")
            self.logger.info(f"🔍 开始收集 {industry_name} 行业信息")
            search_results = await self._collect_industry_information(industry_name, region)
            progress_bar.update(1)

            # 第二步：内容处理
            progress_bar.set_description(f"📄 处理{industry_name}行业内容")
            self.logger.info(f"📄 处理 {industry_name} 行业内容")
            processed_data = await self._process_industry_content(search_results, industry_name)
            progress_bar.update(1)

            # 第三步：构建行业知识库
            progress_bar.set_description(f"🧠 构建{industry_name}知识库")
            self.logger.info(f"🧠 构建 {industry_name} 行业知识库")
            await self._build_industry_knowledge_base(processed_data, industry_name)
            progress_bar.update(1)

            # 第四步：行业深度分析
            progress_bar.set_description(f"🔬 深度分析{industry_name}行业")
            self.logger.info(f"🔬 深度分析 {industry_name} 行业")
            analysis_result = await self._analyze_industry_data_with_stream(industry_name, region)
            progress_bar.update(1)

            # 第五步：生成行业报告
            progress_bar.set_description(f"📝 生成{industry_name}报告")
            self.logger.info(f"📝 生成 {industry_name} 行业报告")
            report_path = await self._generate_industry_report(analysis_result)
            progress_bar.update(1)

            progress_bar.set_description(f"✅ {industry_name}行业研报完成")
            progress_bar.close()

            self.end_task(f"生成行业研报 - {industry_name}")
            return report_path

        except Exception as e:
            progress_bar.close()
            self.logger.error(f"❌ 生成行业研报失败: {str(e)}")
            raise e
    
    async def _collect_industry_information(self, industry_name: str, region: str) -> List[Dict]:
        """
        收集行业相关信息
        """
        search_queries = []
        
        # 构建搜索查询
        for query_template in self.industry_search_queries:
            query = query_template.format(industry_name=industry_name)
            if region and region != "全球":
                query += f" {region}"
            search_queries.append(query)
        
        # 添加一些通用的行业查询
        additional_queries = [
            f"{industry_name} 行业白皮书",
            f"{industry_name} 产业报告 研究",
            f"{industry_name} 市场前景 预测",
            f"{industry_name} 龙头企业 上市公司"
        ]
        search_queries.extend(additional_queries)
        
        # 改为串行搜索，避免速率限制问题
        all_results = []
        seen_urls = set()

        # 限制搜索查询数量，避免过多请求
        limited_queries = search_queries[:12]  # 最多12个查询

        for i, query in enumerate(limited_queries):
            try:
                self.logger.info(f"🔍 执行行业搜索 {i+1}/{len(limited_queries)}: {query}")
                results = self.web_search_tool.search(query, num_results=4)  # 减少每次搜索的结果数

                if results and isinstance(results, list):
                    for result in results:
                        url = result.get("url", "")
                        if url and url not in seen_urls:
                            seen_urls.add(url)
                            all_results.append(result)
                    self.logger.info(f"✅ 行业搜索 {i+1} 成功，获得 {len(results)} 条结果")
                else:
                    self.logger.warning(f"⚠️ 行业搜索 {i+1} 无结果")

                # 添加延迟，避免速率限制
                if i < len(limited_queries) - 1:  # 最后一次不需要延迟
                    await asyncio.sleep(0.5)  # 500ms延迟

            except Exception as e:
                self.logger.error(f"❌ 行业搜索 {i+1} 失败: {str(e)}")
                continue
        
        self.logger.info(f"收集到 {len(all_results)} 条行业搜索结果")
        return all_results
    
    async def _process_industry_content(self, search_results: List[Dict], industry_name: str) -> List[Dict]:
        """
        处理行业搜索结果
        """
        # 筛选高质量URL
        quality_urls = []
        for result in search_results:
            url = result.get("url", "")
            title = result.get("title", "")
            snippet = result.get("snippet", "")
            
            # 质量评分
            quality_score = self._calculate_content_quality_score(title, snippet, url)
            
            if quality_score > 0.5:  # 质量阈值
                quality_urls.append({
                    "url": url,
                    "title": title,
                    "snippet": snippet,
                    "quality_score": quality_score
                })
        
        # 按质量排序并限制数量
        quality_urls.sort(key=lambda x: x["quality_score"], reverse=True)
        quality_urls = quality_urls[:25]  # 最多处理25个高质量URL
        
        # 简化处理：直接使用搜索结果，放宽条件避免0结果
        processed_data = []
        for result in search_results[:20]:  # 限制处理数量
            title = result.get("title", "")
            snippet = result.get("snippet", "")
            url = result.get("url", "")

            # 放宽条件：只要有标题就可以使用，snippet可以为空
            if title and len(title) > 10:  # 标题至少10个字符
                # 如果snippet为空，使用标题作为内容
                content_text = snippet if snippet and len(snippet) > 20 else title

                processed_data.append({
                    "url": url,
                    "title": title,
                    "content": f"{title}\n\n{content_text}",
                    "industry": industry_name,
                    "content_category": self._classify_industry_content(title, content_text)
                })

                self.logger.debug(f"处理行业搜索结果: {title[:50]}... (snippet长度: {len(snippet)})")
        
        self.logger.info(f"成功处理 {len(processed_data)} 条行业内容")
        return processed_data
    
    def _calculate_content_quality_score(self, title: str, snippet: str, url: str) -> float:
        """
        计算内容质量评分
        """
        score = 0.0
        
        # 标题质量评分
        title_keywords = ["分析", "报告", "研究", "市场", "行业", "趋势", "前景", "发展"]
        title_score = sum(1 for keyword in title_keywords if keyword in title) / len(title_keywords)
        score += title_score * 0.4
        
        # 描述质量评分
        snippet_keywords = ["数据", "增长", "规模", "竞争", "技术", "政策", "投资", "机会"]
        snippet_score = sum(1 for keyword in snippet_keywords if keyword in snippet) / len(snippet_keywords)
        score += snippet_score * 0.3
        
        # URL质量评分
        authoritative_domains = [
            "gov.cn", "edu.cn", "org.cn", "xinhuanet.com", "people.com.cn",
            "eastmoney.com", "cnstock.com", "21jingji.com", "caixin.com",
            "ftchinese.com", "bloomberg.com", "reuters.com"
        ]
        url_score = 0.5 if any(domain in url for domain in authoritative_domains) else 0.2
        score += url_score * 0.3
        
        return min(score, 1.0)
    
    def _classify_industry_content(self, title: str, content: str) -> str:
        """
        分类行业内容
        """
        title_lower = title.lower()
        content_lower = content.lower()
        
        # 市场规模相关
        if any(keyword in title_lower or keyword in content_lower 
               for keyword in ["市场规模", "产值", "收入", "营收", "销售额"]):
            return "market_size"
        
        # 竞争格局相关
        elif any(keyword in title_lower or keyword in content_lower 
                 for keyword in ["竞争格局", "市场份额", "龙头企业", "排名", "竞争者"]):
            return "competition"
        
        # 技术发展相关
        elif any(keyword in title_lower or keyword in content_lower 
                 for keyword in ["技术", "创新", "研发", "专利", "算法", "平台"]):
            return "technology"
        
        # 政策监管相关
        elif any(keyword in title_lower or keyword in content_lower 
                 for keyword in ["政策", "法规", "监管", "标准", "规范", "指导意见"]):
            return "policy"
        
        # 投资机会相关
        elif any(keyword in title_lower or keyword in content_lower 
                 for keyword in ["投资", "机会", "前景", "趋势", "发展", "增长"]):
            return "investment"
        
        # 风险挑战相关
        elif any(keyword in title_lower or keyword in content_lower 
                 for keyword in ["风险", "挑战", "问题", "困难", "障碍"]):
            return "risk"
        
        else:
            return "general"
    
    async def _build_industry_knowledge_base(self, processed_data: List[Dict], industry_name: str):
        """
        构建行业知识库
        """
        for data in processed_data:
            metadata = {
                "source": data["url"],
                "industry": industry_name,
                "content_category": data["content_category"],
                "title": data["title"],
                "quality_score": data["quality_score"]
            }
            
            # 添加到RAG系统
            self.rag_system.add_document(data["content"], metadata)
        
        self.logger.info(f"行业知识库构建完成，共添加 {len(processed_data)} 个文档")
    
    async def _analyze_industry_data_with_stream(self, industry_name: str, region: str) -> Dict[str, Any]:
        """
        深度分析行业数据 - 支持多轮对话和工具调用
        """
        self.logger.info(f"🤖 开始智能分析对话 - {industry_name}行业")

        # 初始分析提示 - 企业级标准
        initial_prompt = f"""
        You are a senior industry research analyst conducting a comprehensive analysis of the {region} {industry_name} industry.
        This analysis must meet institutional-grade standards and professional research guidelines.

        ANALYSIS FRAMEWORK (follow this structure):
        1. **Industry Overview** - Market definition, size, and structural analysis
        2. **Market Dynamics** - Growth trends, drivers, and market segmentation
        3. **Competitive Landscape** - Key players, market share, competitive positioning
        4. **Technology & Innovation** - Technological trends, disruption factors, R&D landscape
        5. **Regulatory Environment** - Policy framework, regulatory changes, compliance requirements
        6. **Investment Analysis** - Capital flows, valuation trends, investment opportunities
        7. **Risk Assessment** - Industry risks, cyclical factors, external threats

        TOOLS AVAILABLE:
        - web_search: Search for latest industry data, market reports, company information
        - get_url_content: Extract detailed content from industry reports and sources
        - search_knowledge_base: Access existing research and historical data
        - analyze_market_data: Perform quantitative market analysis

        PROFESSIONAL STANDARDS:
        - Use specific market metrics (TAM, SAM, SOM, CAGR, market share percentages)
        - Provide quantitative data to support all conclusions
        - Include competitive benchmarking and industry comparisons
        - Follow professional industry research report format
        - Ensure all statements are data-driven and objective
        - Extract concrete market size figures, growth rates, and key performance indicators

        Begin your analysis by searching for {industry_name} industry's latest market size data,
        growth statistics, and key market dynamics. Focus on obtaining concrete quantitative metrics
        including market value, growth rates, and competitive market shares.
        """

        # 进行多轮对话分析
        messages = [
            {"role": "system", "content": "你是一位专业的行业分析师，具有丰富的行业研究经验。你可以使用各种工具来收集和分析信息。请进行深入、全面的分析，确保内容专业、详细，每个分析维度都要充分展开。"},
            {"role": "user", "content": initial_prompt}
        ]

        # 执行多轮对话，增加到8轮以获取更丰富的内容
        conversation_log = []
        for round_num in range(1, 9):
            self.logger.info(f"💬 执行第 {round_num} 轮分析对话")
            print(f"\n{'='*60}")
            print(f"🏭 第 {round_num} 轮行业分析 - {industry_name}")
            print(f"{'='*60}")

            try:
                # 使用流式输出进行对话
                full_response = ""
                async for chunk in self.llm_client.async_chat_completion_with_tools_stream(
                    messages=messages,
                    tools=self.tools,
                    tool_functions=self.tool_functions,
                    max_iterations=6,
                    temperature=0.1,
                    show_thinking=False
                ):
                    print(chunk, end='', flush=True)
                    full_response += chunk

                conversation_log.append(f"第{round_num}轮: {full_response}")

                # 检查是否完成分析
                if self._is_analysis_complete(full_response):
                    self.logger.info(f"✅ 分析完成于第 {round_num} 轮")
                    print(f"\n\n✅ 行业分析完成！")
                    break

                # 继续下一轮分析
                if round_num < 8:
                    follow_up_prompt = self._generate_follow_up_prompt(full_response, round_num)
                    messages.append({"role": "assistant", "content": full_response})
                    messages.append({"role": "user", "content": follow_up_prompt})

                    print(f"\n\n🔄 准备进入第 {round_num + 1} 轮分析...")

            except Exception as e:
                self.logger.error(f"第 {round_num} 轮对话异常: {str(e)}")
                print(f"\n❌ 第 {round_num} 轮对话异常: {str(e)}")
                break

        # 生成最终分析报告
        final_analysis = await self._generate_final_industry_analysis(industry_name, region, conversation_log)

        return final_analysis

    def _is_analysis_complete(self, content: str) -> bool:
        """判断分析是否完成"""
        completion_indicators = [
            "综合分析结论",
            "投资建议",
            "分析完成",
            "总结",
            "最终评估",
            "行业前景"
        ]

        content_lower = content.lower()
        return any(indicator in content_lower for indicator in completion_indicators)

    def _generate_follow_up_prompt(self, previous_content: str, round_num: int) -> str:
        """生成后续分析提示"""
        follow_up_prompts = {
            1: "很好！现在请继续深入搜索行业的详细基本信息，包括行业分类、监管环境、准入门槛等。请确保获取准确的数据。",
            2: "请详细分析行业的市场规模、增长趋势和区域分布情况。重点关注市场容量和发展速度的具体数据。",
            3: "请深入分析行业的竞争格局，包括主要企业、市场份额、竞争优势和行业壁垒。请搜索最新的竞争信息。",
            4: "请分析行业的技术发展趋势、创新能力和数字化转型情况。关注前沿技术和技术标准。",
            5: "请评估政策环境对行业的影响，包括国家政策、监管变化和税收政策等。分析政策对行业发展的推动作用。",
            6: "请进行投资机会分析，包括投资热点、资本流向和估值水平。提供专业的投资观点和策略建议。",
            7: "请全面评估行业面临的各类风险因素，包括市场风险、政策风险、技术风险等，并提供风险控制建议。"
        }

        return follow_up_prompts.get(round_num, "请基于以上所有收集的信息，提供综合的深度行业分析结论和专业投资建议。")

    async def _generate_final_industry_analysis(self, industry_name: str, region: str, conversation_log: List[str]) -> Dict[str, Any]:
        """生成最终行业分析报告 - 基于对话内容生成专业报告所需的所有章节"""
        self.logger.info(f"📋 生成最终行业分析报告 - {industry_name}")

        # 合并所有对话内容
        full_conversation = "\n\n".join(conversation_log)

        # 生成专业报告所需的所有章节
        sections_to_generate = [
            "投资要点",
            "行业概况",
            "市场规模分析",
            "竞争格局分析",
            "技术发展趋势",
            "政策影响分析",
            "投资机会分析",
            "风险提示"
        ]

        report_data = {
            "industry_name": industry_name,
            "region": region,
            "conversation_log": conversation_log,
            "data_sources": self._get_data_sources()
        }

        # 为每个章节生成详细内容
        for section in sections_to_generate:
            self.logger.info(f"📝 生成章节: {section}")
            section_content = await self._generate_section_content_from_conversation(
                industry_name, section, full_conversation
            )

            # 使用标准化的键名映射
            if section == "投资要点":
                report_data["executive_summary"] = section_content
                report_data["投资要点"] = section_content
            elif section == "行业概况":
                report_data["industry_overview"] = section_content
                report_data["行业概况"] = section_content
            elif section == "市场规模分析":
                report_data["market_size_analysis"] = section_content
                report_data["市场规模分析"] = section_content
            elif section == "竞争格局分析":
                report_data["competition_analysis"] = section_content
                report_data["竞争格局分析"] = section_content
            elif section == "技术发展趋势":
                report_data["trend_analysis"] = section_content
                report_data["技术发展趋势"] = section_content
            elif section == "政策影响分析":
                report_data["policy_analysis"] = section_content
                report_data["政策影响分析"] = section_content
            elif section == "投资机会分析":
                report_data["investment_opportunities"] = section_content
                report_data["投资机会分析"] = section_content
            elif section == "风险提示":
                report_data["risk_warning"] = section_content
                report_data["风险提示"] = section_content

        # 生成完整分析内容
        full_analysis = await self._generate_comprehensive_industry_analysis_from_conversation(
            industry_name, region, full_conversation
        )
        report_data["full_analysis"] = full_analysis

        return report_data

    async def _generate_section_content_from_conversation(self, industry_name: str, section_name: str, conversation_context: str) -> str:
        """基于对话内容为特定章节生成详细内容"""
        prompt = f"""
        基于以下分析对话内容，为{industry_name}行业撰写{section_name}：

        分析对话记录：
        {conversation_context}

        请基于对话中收集的信息，撰写专业的{section_name}内容。
        要求：内容专业、客观，严格基于对话中的真实数据。
        """

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位资深的行业分析师，具有丰富的行业研究经验。请严格基于提供的对话内容生成分析报告。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    async def _generate_comprehensive_industry_analysis_from_conversation(self, industry_name: str, region: str, conversation_context: str) -> str:
        """基于对话内容生成综合行业分析"""
        prompt = f"""
        基于以下详细的分析对话内容，为{region}{industry_name}行业生成一份综合的行业分析报告：

        分析对话记录：
        {conversation_context}

        请提供完整的分析内容，包括：
        1. 投资要点
        2. 行业概况
        3. 市场规模分析
        4. 竞争格局分析
        5. 技术发展趋势
        6. 政策影响分析
        7. 投资机会分析
        8. 风险提示

        要求：
        - 严格基于对话中收集的真实数据和信息
        - 分析深入、专业、客观
        - 结论明确、有依据
        - 格式规范、逻辑清晰
        """

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位资深的行业分析师，擅长撰写专业的行业研究报告。请严格基于提供的对话内容进行分析。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    async def _analyze_industry_data(self, industry_name: str, region: str) -> Dict[str, Any]:
        """
        深度分析行业数据
        """
        # 使用RAG检索不同类别的信息
        market_context = self.rag_system.get_relevant_context(f"{industry_name} 市场规模 增长")
        competition_context = self.rag_system.get_relevant_context(f"{industry_name} 竞争格局 企业")
        technology_context = self.rag_system.get_relevant_context(f"{industry_name} 技术发展 创新")
        policy_context = self.rag_system.get_relevant_context(f"{industry_name} 政策 监管")
        investment_context = self.rag_system.get_relevant_context(f"{industry_name} 投资机会 前景")
        risk_context = self.rag_system.get_relevant_context(f"{industry_name} 风险 挑战")
        
        # 构建深度分析提示词
        analysis_prompt = f"""
        作为资深的行业分析师，请基于以下信息对{industry_name}行业进行全面深入的分析：

        市场规模信息：
        {market_context}

        竞争格局信息：
        {competition_context}

        技术发展信息：
        {technology_context}

        政策环境信息：
        {policy_context}

        投资机会信息：
        {investment_context}

        风险因素信息：
        {risk_context}

        请提供以下完整的分析内容：

        1. 执行摘要（概括行业现状和主要观点，300字以内）
        
        2. 行业概况
           - 行业定义与分类
           - 发展历程
           - 行业特征
           - 在国民经济中的地位
        
        3. 市场规模分析
           - 当前市场规模
           - 历史增长趋势
           - 市场结构分析
           - 未来增长预测
        
        4. 竞争格局分析
           - 市场集中度
           - 主要参与者分析
           - 竞争优势对比
           - 进入壁垒分析
        
        5. 技术发展趋势
           - 核心技术分析
           - 技术发展路径
           - 创新驱动因素
           - 技术壁垒与突破点
        
        6. 政策环境分析
           - 相关政策梳理
           - 政策影响评估
           - 监管环境变化
           - 政策趋势预测
        
        7. 投资机会分析
           - 细分领域机会
           - 价值链投资点
           - 新兴应用场景
           - 投资建议与策略
        
        8. 风险因素分析
           - 行业面临的主要风险
           - 风险影响程度评估
           - 风险应对策略
        
        请确保分析深入、客观、专业，基于实际数据和趋势，避免空泛的描述。分析应该具有前瞻性和指导意义。
        """
        
        # 调用LLM进行深度分析
        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位资深的行业研究专家，擅长撰写深入的行业分析报告。"},
            {"role": "user", "content": analysis_prompt}
        ], temperature=0.2)  # 降低温度以获得更稳定的输出
        
        # 解析分析结果
        analysis_content = response.get("content", "")
        
        # 构造报告数据
        report_data = {
            "industry_name": industry_name,
            "region": region,
            "executive_summary": self._extract_section(analysis_content, "执行摘要"),
            "industry_overview": self._extract_section(analysis_content, "行业概况"),
            "market_size_analysis": self._extract_section(analysis_content, "市场规模分析"),
            "competition_analysis": self._extract_section(analysis_content, "竞争格局分析"),
            "technology_trends": self._extract_section(analysis_content, "技术发展趋势"),
            "policy_analysis": self._extract_section(analysis_content, "政策环境分析"),
            "investment_opportunities": self._extract_section(analysis_content, "投资机会分析"),
            "risk_analysis": self._extract_section(analysis_content, "风险因素分析"),
            "full_analysis": analysis_content,
            "data_sources": self._get_industry_data_sources(),
            "key_players": await self._identify_key_players(industry_name),
            "market_metrics": await self._extract_market_metrics(market_context)
        }
        
        return report_data
    
    def _extract_section(self, content: str, section_name: str) -> str:
        """
        从分析内容中提取特定章节
        """
        lines = content.split('\n')
        section_content = []
        in_section = False
        
        for line in lines:
            line = line.strip()
            if section_name in line and any(char in line for char in ['1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.']):
                in_section = True
                continue
            elif in_section and any(num in line for num in ['1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.']):
                if section_content and not line.startswith('   '):  # 不是子项目
                    break
            
            if in_section and line:
                section_content.append(line)
        
        return '\n'.join(section_content).strip()
    
    def _get_industry_data_sources(self) -> List[str]:
        """
        获取行业数据来源
        """
        return [
            "国家统计局行业统计数据",
            "行业协会发布的白皮书和年报",
            "上市公司年报和财务数据",
            "第三方研究机构报告",
            "政府部门政策文件",
            "专业财经媒体报道",
            "国际研究机构数据"
        ]
    
    async def _identify_key_players(self, industry_name: str) -> List[Dict[str, str]]:
        """
        识别行业关键参与者
        """
        try:
            # 搜索行业龙头企业
            query = f"{industry_name} 龙头企业 上市公司 排名"
            search_results = await self.execute_with_retry(
                self.web_search_tool.search, query, num_results=5
            )
            
            key_players = []
            for result in search_results[:3]:  # 取前3个结果
                key_players.append({
                    "name": result.get("title", "").split()[0] if result.get("title") else "未知",
                    "description": result.get("snippet", "")[:100]
                })
            
            return key_players
        except Exception as e:
            self.logger.error(f"识别关键参与者失败: {str(e)}")
            return []
    
    async def _extract_market_metrics(self, market_context: str) -> Dict[str, str]:
        """
        提取市场指标
        """
        metrics = {}
        
        # 使用简单的文本处理提取数字信息
        import re
        
        # 匹配市场规模相关数字
        size_patterns = [
            r'(\d+(?:\.\d+)?)\s*[万亿千百]*\s*[元|美元|USD]',
            r'市场规模.*?(\d+(?:\.\d+)?)',
            r'产值.*?(\d+(?:\.\d+)?)'
        ]
        
        for pattern in size_patterns:
            matches = re.findall(pattern, market_context)
            if matches:
                metrics["market_size"] = f"{matches[0]}亿元（估算）"
                break
        
        # 匹配增长率
        growth_patterns = [
            r'增长.*?(\d+(?:\.\d+)?%)',
            r'CAGR.*?(\d+(?:\.\d+)?%)',
            r'复合增长率.*?(\d+(?:\.\d+)?%)'
        ]
        
        for pattern in growth_patterns:
            matches = re.findall(pattern, market_context)
            if matches:
                metrics["growth_rate"] = matches[0]
                break
        
        return metrics
    
    async def _generate_industry_report(self, analysis_result: Dict[str, Any]) -> str:
        """
        生成最终的专业行业研究报告 - 直接使用已生成的章节内容
        """
        print(f"\n📝 开始生成行业DOCX报告...")
        print(f"📋 使用已生成的章节内容...")

        # 直接使用已生成的章节内容，避免重复生成
        required_sections = ["行业概况", "市场规模分析", "竞争格局分析", "技术发展趋势", "投资机会分析", "风险提示"]
        missing_sections = [section for section in required_sections if section not in analysis_result]

        if missing_sections:
            print(f"⚠️ 缺少章节: {missing_sections}，使用默认内容")
            for section in missing_sections:
                analysis_result[section] = f"{section}内容正在完善中。"

        # 直接使用analysis_result作为professional_data
        professional_data = analysis_result.copy()

        # 确保有基本的报告信息
        if "industry_name" not in professional_data:
            professional_data["industry_name"] = "研究行业"

        print(f"📊 报告数据准备完成，开始生成文档...")
        print(f"⏱️  预计需要30-60秒，请耐心等待...")

        # 直接同步生成DOCX报告 - 更简单更可靠
        try:
            print(f"📝 开始生成DOCX报告...")
            print(f"⚡ 正在优化报告数据...")

            # 直接调用同步方法，避免异步复杂性
            report_path = self.professional_docx_generator.create_industry_report(professional_data)

            print(f"✅ 行业DOCX报告生成完成！")
            self.logger.info(f"专业行业研报生成完成: {report_path}")
            return report_path

        except Exception as e:
            print(f"⚠️  专业DOCX生成失败: {e}")
            print(f"🔄 尝试生成简化版本...")

            try:
                # 生成简化版本作为备选
                simplified_data = self._create_simplified_report_data(analysis_result)
                report_path = self.docx_generator.create_industry_report(simplified_data)
                print(f"✅ 简化版行业DOCX报告生成完成！")
                self.logger.info(f"简化行业研报生成完成: {report_path}")
                return report_path
            except Exception as fallback_error:
                print(f"❌ 简化版DOCX生成也失败: {fallback_error}")
                raise Exception(f"DOCX报告生成失败: {e}, 备选方案也失败: {fallback_error}")

    def _create_simplified_report_data(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """创建简化的报告数据"""
        return {
            "industry_name": analysis_result.get("industry_name", "行业"),
            "region": analysis_result.get("region", "全球"),
            "executive_summary": analysis_result.get("executive_summary", "执行摘要"),
            "industry_overview": analysis_result.get("industry_overview", "行业概况"),
            "market_size_analysis": analysis_result.get("market_size_analysis", "市场规模分析"),
            "competition_analysis": analysis_result.get("competition_analysis", "竞争格局分析"),
            "investment_opportunities": analysis_result.get("investment_opportunities", "投资机会"),
            "risks": analysis_result.get("risks", ["行业风险", "市场风险"])
        }

    async def _build_professional_report_data(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """构建专业报告数据 - 基于LLM深度生成"""
        industry_name = analysis_result.get('industry_name', '智能风控行业')
        region = analysis_result.get('region', '中国')

        # 获取知识库上下文
        industry_context = self.rag_system.get_relevant_context(f"{industry_name} 行业分析 市场")
        policy_context = self.rag_system.get_relevant_context(f"{industry_name} 政策 监管")

        # 使用LLM生成专业内容
        professional_data = await self._generate_professional_industry_content(
            industry_name, region, industry_context, policy_context
        )

        return professional_data

    async def _generate_professional_industry_content(self, industry_name: str, region: str,
                                                    industry_context: str, policy_context: str) -> Dict[str, Any]:
        """使用LLM生成专业行业报告内容"""

        # 基础信息
        base_data = {
            "industry_name": industry_name,
            "region": region,
            "analyst_name": "AI智能分析师",
            "analyst_license": "S1234567890123456",
            "analyst_phone": "010-12345678",
            "analyst_email": "<EMAIL>"
        }

        # 1. 生成投资要点
        print(f"📝 生成章节: 投资要点")
        investment_highlights = await self._generate_industry_investment_highlights(
            industry_name, region, industry_context
        )
        base_data["投资要点"] = investment_highlights

        # 2. 生成行业概况
        print(f"📝 生成章节: 行业概况")
        industry_overview = await self._generate_industry_overview_detailed(
            industry_name, region, industry_context
        )
        base_data["行业概况"] = industry_overview

        # 3. 生成市场规模分析
        print(f"📝 生成章节: 市场规模分析")
        market_analysis = await self._generate_market_size_analysis_detailed(
            industry_name, region, industry_context
        )
        base_data["市场规模分析"] = market_analysis

        # 4. 生成竞争格局分析
        print(f"📝 生成章节: 竞争格局分析")
        competition_analysis = await self._generate_competition_analysis_detailed(
            industry_name, industry_context
        )
        base_data["竞争格局分析"] = competition_analysis

        # 5. 生成技术发展趋势
        print(f"📝 生成章节: 技术发展趋势")
        tech_trends = await self._generate_technology_trends_detailed(
            industry_name, industry_context
        )
        base_data["技术发展趋势"] = tech_trends

        # 6. 生成政策影响分析
        print(f"📝 生成章节: 政策影响分析")
        policy_analysis = await self._generate_policy_impact_analysis(
            industry_name, policy_context
        )
        base_data["政策影响分析"] = policy_analysis

        # 7. 生成投资机会分析
        print(f"📝 生成章节: 投资机会分析")
        investment_opportunities = await self._generate_investment_opportunities_detailed(
            industry_name, industry_context
        )
        base_data["投资机会分析"] = investment_opportunities

        # 8. 生成风险提示
        print(f"📝 生成章节: 风险提示")
        risk_warning = await self._generate_industry_risk_warning(
            industry_name, industry_context
        )
        base_data["风险提示"] = risk_warning

        return base_data

    async def _generate_industry_investment_highlights(self, industry_name: str, region: str, industry_context: str) -> str:
        """生成行业投资要点"""
        prompt = f"""
作为资深行业分析师，请为{region}{industry_name}撰写专业的投资要点：

行业信息：
{industry_context}

请按照以下格式生成投资要点：

## 行业投资评级：看好/中性/谨慎

## 核心投资逻辑：
1. **市场前景**：[行业市场规模和增长前景]
2. **政策支持**：[政府政策支持力度和方向]
3. **技术驱动**：[技术创新对行业发展的推动作用]
4. **投资机会**：[具体的投资机会和标的]

## 关键数据：
- 市场规模：[当前市场规模和预期]
- 增长率：[历史和预期增长率]
- 渗透率：[当前渗透率和提升空间]
- 政策支持：[相关政策和资金支持]

## 投资主线：
- [列出3-4个主要投资主线]

## 重点关注领域：
- [列出重点关注的细分领域]

要求：
1. 内容专业、数据详实
2. 突出行业投资价值
3. 基于真实信息分析
4. 逻辑清晰、结论明确
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位资深的行业投资分析师，具有丰富的行业研究经验。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    async def _generate_industry_overview_detailed(self, industry_name: str, region: str, industry_context: str) -> str:
        """生成详细行业概况"""
        prompt = f"""
作为专业行业分析师，请为{region}{industry_name}撰写详细的行业概况：

参考信息：
{industry_context}

请按照以下结构生成内容：

## 行业定义与分类
[明确行业定义、范围和分类标准]

## 行业发展历程
### 发展阶段
- **萌芽期（时间）**：[发展特征和关键事件]
- **成长期（时间）**：[发展特征和关键事件]
- **成熟期（时间）**：[发展特征和关键事件]

### 重要里程碑
[按时间顺序列出行业发展的重要节点]

## 产业链分析
### 上游
- **主要环节**：[上游主要业务环节]
- **关键企业**：[上游代表性企业]
- **供给特征**：[供给能力和特点]

### 中游
- **核心环节**：[中游核心业务]
- **商业模式**：[主要商业模式]
- **价值创造**：[价值创造方式]

### 下游
- **应用领域**：[主要应用场景]
- **客户群体**：[目标客户分析]
- **需求特征**：[需求规模和特点]

## 行业基本数据
请提供行业基础数据表（JSON格式）：
```json
{{
  "指标": ["市场规模(亿元)", "企业数量(家)", "从业人员(万人)", "产值增长率", "利润率"],
  "2023年": ["", "", "", "", ""],
  "2022年": ["", "", "", "", ""],
  "2021年": ["", "", "", "", ""]
}}
```

## 行业特征
- **周期性特征**：[行业周期性分析]
- **季节性特征**：[季节性波动情况]
- **区域性特征**：[地域分布特点]
- **技术密集度**：[技术要求和门槛]

## 监管环境
- **主管部门**：[行业主管部门]
- **监管政策**：[主要监管政策]
- **准入门槛**：[行业准入要求]

要求：
1. 信息全面、准确
2. 分析深入、专业
3. 数据具体、可信
4. 结构清晰、逻辑性强
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位专业的行业研究分析师。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    async def _generate_market_size_analysis_detailed(self, industry_name: str, region: str, industry_context: str) -> str:
        """生成详细市场规模分析"""
        prompt = f"""
请为{region}{industry_name}撰写详细的市场规模分析：

行业信息：
{industry_context}

请按照以下结构生成：

## 市场规模现状
### 总体规模
- **市场规模**：[当前市场规模，具体数字]
- **增长趋势**：[近年来增长趋势]
- **全球地位**：[在全球市场中的地位]

### 细分市场
请提供细分市场数据（JSON格式）：
```json
{{
  "细分市场": ["细分1", "细分2", "细分3", "其他"],
  "市场规模(亿元)": ["", "", "", ""],
  "市场份额": ["", "", "", ""],
  "增长率": ["", "", "", ""]
}}
```

## 市场增长驱动因素
### 需求侧驱动
1. **政策驱动**：[政策支持对需求的推动]
2. **技术驱动**：[技术进步带来的新需求]
3. **消费升级**：[消费升级对需求的影响]
4. **产业升级**：[产业转型升级需求]

### 供给侧驱动
1. **技术创新**：[供给侧技术创新]
2. **成本下降**：[成本优化和规模效应]
3. **产能扩张**：[产能建设和扩张]

## 市场规模预测
请提供市场预测数据（JSON格式）：
```json
{{
  "年份": ["2024E", "2025E", "2026E", "2027E", "2028E"],
  "市场规模(亿元)": ["", "", "", "", ""],
  "增长率": ["", "", "", "", ""],
  "渗透率": ["", "", "", "", ""]
}}
```

## 区域市场分析
### 主要区域市场
- **华东地区**：[市场规模、特点、增长情况]
- **华南地区**：[市场规模、特点、增长情况]
- **华北地区**：[市场规模、特点、增长情况]
- **其他地区**：[市场规模、特点、增长情况]

## 国际市场对比
请提供国际对比数据（JSON格式）：
```json
{{
  "国家/地区": ["中国", "美国", "欧盟", "日本", "其他"],
  "市场规模(亿美元)": ["", "", "", "", ""],
  "全球占比": ["", "", "", "", ""],
  "增长率": ["", "", "", "", ""]
}}
```

## 市场集中度分析
- **CR5集中度**：[前5名企业市场份额]
- **HHI指数**：[赫芬达尔指数]
- **竞争格局**：[市场竞争激烈程度]

## 市场发展瓶颈
[分析制约市场发展的主要因素]

要求：
1. 数据准确、来源可靠
2. 分析全面、深入
3. 预测合理、有依据
4. 突出关键增长点
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位专业的市场规模分析师，具有丰富的市场研究经验。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    async def _generate_competition_analysis_detailed(self, industry_name: str, industry_context: str) -> str:
        """生成详细竞争格局分析"""
        prompt = f"""
请为{industry_name}撰写详细的竞争格局分析：

行业信息：
{industry_context}

请按照以下结构生成：

## 竞争格局概述
[总体竞争格局描述，包括集中度、竞争激烈程度等]

## 主要竞争者分析
请提供主要竞争者对比表（JSON格式）：
```json
{{
  "公司名称": ["龙头企业1", "龙头企业2", "龙头企业3", "新兴企业1", "新兴企业2"],
  "市场份额": ["", "", "", "", ""],
  "营收规模(亿元)": ["", "", "", "", ""],
  "核心优势": ["", "", "", "", ""],
  "主要产品": ["", "", "", "", ""],
  "发展策略": ["", "", "", "", ""]
}}
```

## 竞争层次分析
### 第一梯队（龙头企业）
- **企业特征**：[龙头企业的共同特征]
- **竞争优势**：[技术、资金、渠道等优势]
- **市场地位**：[在行业中的地位和影响力]

### 第二梯队（追赶者）
- **企业特征**：[追赶者的特征]
- **发展策略**：[追赶策略和差异化定位]
- **成长潜力**：[未来发展潜力]

### 第三梯队（新进入者）
- **进入壁垒**：[新进入者面临的壁垒]
- **竞争策略**：[新进入者的竞争策略]
- **发展前景**：[新进入者的发展前景]

## 竞争要素分析
### 技术竞争
- **技术门槛**：[行业技术要求和门槛]
- **研发投入**：[主要企业研发投入对比]
- **技术路线**：[主流技术路线和发展方向]

### 成本竞争
- **成本结构**：[行业成本构成分析]
- **规模效应**：[规模经济对成本的影响]
- **成本控制**：[成本控制能力对比]

### 渠道竞争
- **销售模式**：[主要销售渠道和模式]
- **渠道优势**：[渠道建设和管理能力]
- **客户关系**：[客户粘性和关系维护]

## 竞争态势变化
### 近期变化
- **市场份额变化**：[主要企业市场份额变化]
- **竞争策略调整**：[企业战略调整情况]
- **新进入者影响**：[新进入者对格局的影响]

### 未来趋势
- **整合趋势**：[行业整合的可能性]
- **技术变革影响**：[技术变革对竞争格局的影响]
- **政策影响**：[政策变化对竞争的影响]

## 竞争强度评估
请提供竞争强度评估（JSON格式）：
```json
{{
  "竞争维度": ["价格竞争", "技术竞争", "服务竞争", "品牌竞争", "渠道竞争"],
  "竞争强度": ["高/中/低", "高/中/低", "高/中/低", "高/中/低", "高/中/低"],
  "变化趋势": ["上升/稳定/下降", "上升/稳定/下降", "上升/稳定/下降", "上升/稳定/下降", "上升/稳定/下降"]
}}
```

## 投资机会识别
- **龙头企业**：[龙头企业投资价值]
- **成长企业**：[高成长企业投资机会]
- **细分龙头**：[细分领域龙头机会]

要求：
1. 分析客观、全面
2. 数据准确、可信
3. 突出竞争关键要素
4. 关注格局变化趋势
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位专业的竞争分析师，具有丰富的行业竞争研究经验。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    async def _generate_technology_trends_detailed(self, industry_name: str, industry_context: str) -> str:
        """生成详细技术发展趋势"""
        prompt = f"""
请为{industry_name}撰写详细的技术发展趋势分析：

行业信息：
{industry_context}

请按照以下结构生成：

## 技术发展现状
### 核心技术
- **主流技术**：[当前主流技术路线]
- **技术成熟度**：[各技术的成熟度评估]
- **技术标准**：[行业技术标准情况]

### 技术能力对比
请提供技术能力对比（JSON格式）：
```json
{{
  "技术领域": ["核心技术1", "核心技术2", "核心技术3", "新兴技术1"],
  "国际先进水平": ["", "", "", ""],
  "国内领先水平": ["", "", "", ""],
  "技术差距": ["", "", "", ""],
  "追赶时间": ["", "", "", ""]
}}
```

## 技术发展趋势
### 短期趋势（1-2年）
- **技术优化**：[现有技术的优化方向]
- **应用拓展**：[技术应用领域扩展]
- **标准化进程**：[技术标准化发展]

### 中期趋势（3-5年）
- **技术突破**：[预期的技术突破点]
- **融合创新**：[技术融合发展趋势]
- **产业化应用**：[技术产业化进程]

### 长期趋势（5-10年）
- **颠覆性技术**：[可能的颠覆性技术]
- **技术革命**：[技术革命的可能性]
- **未来愿景**：[技术发展的未来图景]

## 技术发展路线图
请提供技术路线图（JSON格式）：
```json
{{
  "时间节点": ["2024", "2025", "2026", "2027", "2030"],
  "关键技术": ["", "", "", "", ""],
  "技术指标": ["", "", "", "", ""],
  "应用场景": ["", "", "", "", ""],
  "市场影响": ["", "", "", "", ""]
}}
```

## 技术创新驱动因素
### 需求驱动
- **市场需求**：[市场需求对技术创新的推动]
- **用户体验**：[用户体验提升需求]
- **成本压力**：[降本增效需求]

### 技术驱动
- **基础研究**：[基础科学研究进展]
- **交叉融合**：[跨领域技术融合]
- **工程化能力**：[技术工程化水平提升]

### 政策驱动
- **科技政策**：[科技创新政策支持]
- **标准制定**：[技术标准制定推动]
- **资金支持**：[研发资金投入]

## 技术投资机会
### 技术领先企业
- **研发实力**：[技术研发能力强的企业]
- **专利布局**：[知识产权优势企业]
- **产业化能力**：[技术产业化能力强的企业]

### 新兴技术方向
- **前沿技术**：[值得关注的前沿技术]
- **投资价值**：[技术投资价值评估]
- **风险评估**：[技术投资风险]

## 技术发展风险
- **技术路线风险**：[技术路线选择风险]
- **标准化风险**：[技术标准变化风险]
- **人才短缺风险**：[技术人才供给风险]
- **国际竞争风险**：[国际技术竞争风险]

要求：
1. 技术分析专业、深入
2. 趋势预测合理、有依据
3. 关注技术商业化前景
4. 突出投资相关性
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位专业的技术趋势分析师，具有丰富的技术发展研究经验。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    async def _generate_policy_impact_analysis(self, industry_name: str, policy_context: str) -> str:
        """生成政策影响分析"""
        prompt = f"""
请为{industry_name}撰写详细的政策影响分析：

政策信息：
{policy_context}

请按照以下结构生成：

## 政策环境概述
[当前政策环境总体评价]

## 重要政策梳理
请提供重要政策清单（JSON格式）：
```json
{{
  "政策名称": ["政策1", "政策2", "政策3", "政策4"],
  "发布时间": ["", "", "", ""],
  "发布部门": ["", "", "", ""],
  "政策类型": ["支持/规范/限制", "支持/规范/限制", "支持/规范/限制", "支持/规范/限制"],
  "主要内容": ["", "", "", ""],
  "影响程度": ["重大/中等/轻微", "重大/中等/轻微", "重大/中等/轻微", "重大/中等/轻微"]
}}
```

## 政策影响分析
### 支持性政策
- **财税政策**：[税收优惠、财政补贴等]
- **产业政策**：[产业发展规划、指导意见等]
- **科技政策**：[研发支持、创新激励等]
- **金融政策**：[融资支持、资本市场政策等]

### 规范性政策
- **监管政策**：[行业监管要求和标准]
- **准入政策**：[市场准入门槛和条件]
- **质量标准**：[产品质量和服务标准]
- **环保要求**：[环境保护相关要求]

### 限制性政策
- **市场限制**：[市场准入限制]
- **技术限制**：[技术使用限制]
- **投资限制**：[外资投资限制等]

## 政策传导机制
### 直接影响
- **市场准入**：[政策对市场准入的直接影响]
- **成本结构**：[政策对企业成本的影响]
- **竞争格局**：[政策对竞争格局的影响]

### 间接影响
- **投资环境**：[政策对投资环境的影响]
- **技术创新**：[政策对技术创新的促进作用]
- **产业链协同**：[政策对产业链的影响]

## 政策效果评估
请提供政策效果评估（JSON格式）：
```json
{{
  "政策领域": ["财税支持", "产业引导", "监管规范", "创新激励"],
  "政策力度": ["强/中/弱", "强/中/弱", "强/中/弱", "强/中/弱"],
  "执行效果": ["好/中/差", "好/中/差", "好/中/差", "好/中/差"],
  "行业影响": ["正面/中性/负面", "正面/中性/负面", "正面/中性/负面", "正面/中性/负面"]
}}
```

## 未来政策趋势
### 短期政策预期（1年内）
- **政策重点**：[短期政策关注重点]
- **可能调整**：[可能的政策调整方向]
- **影响预期**：[对行业的预期影响]

### 中长期政策方向（3-5年）
- **发展目标**：[政策设定的发展目标]
- **支持重点**：[政策支持的重点领域]
- **监管趋势**：[监管政策发展趋势]

## 政策风险评估
- **政策变化风险**：[政策调整的可能性和影响]
- **执行风险**：[政策执行不到位的风险]
- **地方差异风险**：[地方政策差异的影响]

## 政策机会识别
- **政策红利**：[可以享受的政策红利]
- **先发优势**：[政策带来的先发机会]
- **投资机会**：[政策驱动的投资机会]

要求：
1. 政策解读准确、深入
2. 影响分析全面、客观
3. 趋势预测合理、有依据
4. 突出投资相关性
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位专业的政策分析师，具有丰富的政策研究和影响评估经验。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    async def _generate_investment_opportunities_detailed(self, industry_name: str, industry_context: str) -> str:
        """生成详细投资机会分析"""
        prompt = f"""
请为{industry_name}撰写详细的投资机会分析：

行业信息：
{industry_context}

请按照以下结构生成：

## 投资机会概述
[总体投资机会评价和投资逻辑]

## 主要投资主线
### 投资主线1：[主线名称]
- **投资逻辑**：[该主线的投资逻辑]
- **市场空间**：[市场规模和增长潜力]
- **投资标的**：[相关投资标的]
- **投资时机**：[最佳投资时机]
- **预期收益**：[预期投资收益]

### 投资主线2：[主线名称]
[同上结构]

### 投资主线3：[主线名称]
[同上结构]

## 细分领域机会
请提供细分投资机会（JSON格式）：
```json
{{
  "细分领域": ["细分1", "细分2", "细分3", "细分4"],
  "市场规模(亿元)": ["", "", "", ""],
  "增长率": ["", "", "", ""],
  "投资热度": ["高/中/低", "高/中/低", "高/中/低", "高/中/低"],
  "投资价值": ["高/中/低", "高/中/低", "高/中/低", "高/中/低"],
  "代表企业": ["", "", "", ""]
}}
```

## 投资标的分析
### 龙头企业投资机会
- **投资价值**：[龙头企业投资价值]
- **成长空间**：[未来成长空间]
- **投资风险**：[主要投资风险]
- **估值水平**：[当前估值水平]

### 成长型企业机会
- **成长驱动**：[成长驱动因素]
- **竞争优势**：[核心竞争优势]
- **发展前景**：[未来发展前景]
- **投资风险**：[投资风险评估]

### 新兴企业机会
- **创新能力**：[技术创新能力]
- **商业模式**：[商业模式创新]
- **市场潜力**：[市场开拓潜力]
- **投资风险**：[早期投资风险]

## 投资策略建议
### 配置策略
- **核心配置**：[核心持仓建议]
- **卫星配置**：[弹性配置建议]
- **风险控制**：[风险控制措施]

### 时机选择
- **买入时机**：[最佳买入时机]
- **持有期限**：[建议持有期限]
- **退出策略**：[退出时机和策略]

## 投资风险提示
### 系统性风险
- **政策风险**：[政策变化风险]
- **经济周期风险**：[经济周期影响]
- **技术变革风险**：[技术变革冲击]

### 行业风险
- **竞争加剧风险**：[行业竞争风险]
- **技术路线风险**：[技术发展风险]
- **市场饱和风险**：[市场增长放缓风险]

## 投资收益预期
请提供收益预期分析（JSON格式）：
```json
{{
  "投资期限": ["1年", "3年", "5年"],
  "预期收益率": ["", "", ""],
  "风险等级": ["高/中/低", "高/中/低", "高/中/低"],
  "收益来源": ["", "", ""]
}}
```

要求：
1. 机会识别准确、全面
2. 投资逻辑清晰、有说服力
3. 风险评估客观、充分
4. 策略建议具体、可操作
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位专业的投资机会分析师，具有丰富的行业投资研究经验。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")

    async def _generate_industry_risk_warning(self, industry_name: str, industry_context: str) -> str:
        """生成行业风险提示"""
        prompt = f"""
作为专业风险分析师，请为{industry_name}撰写全面的风险提示：

行业信息：
{industry_context}

请按照以下结构生成：

## 风险评级
**整体风险等级：高/中高/中等/中低/低**

## 主要风险因素
### 1. 政策风险
- **监管政策变化**：[监管政策调整风险]
- **产业政策调整**：[产业政策变化影响]
- **国际政策影响**：[国际政策对行业的影响]

### 2. 市场风险
- **需求波动风险**：[市场需求变化风险]
- **竞争加剧风险**：[行业竞争激化风险]
- **价格波动风险**：[产品价格波动风险]
- **客户集中风险**：[客户过度集中风险]

### 3. 技术风险
- **技术迭代风险**：[技术更新换代风险]
- **技术路线风险**：[技术路线选择风险]
- **研发失败风险**：[技术研发失败风险]
- **技术泄露风险**：[核心技术泄露风险]

### 4. 运营风险
- **供应链风险**：[供应链中断风险]
- **人才流失风险**：[关键人才流失风险]
- **质量控制风险**：[产品质量风险]
- **信息安全风险**：[数据安全风险]

### 5. 财务风险
- **资金链风险**：[现金流紧张风险]
- **汇率风险**：[汇率波动影响]
- **信用风险**：[客户信用风险]
- **投资风险**：[投资项目风险]

## 风险量化评估
请提供风险评估矩阵（JSON格式）：
```json
{{
  "风险类别": ["政策风险", "市场风险", "技术风险", "运营风险", "财务风险"],
  "发生概率": ["高/中/低", "高/中/低", "高/中/低", "高/中/低", "高/中/低"],
  "影响程度": ["重大/中等/轻微", "重大/中等/轻微", "重大/中等/轻微", "重大/中等/轻微", "重大/中等/轻微"],
  "风险等级": ["高/中/低", "高/中/低", "高/中/低", "高/中/低", "高/中/低"],
  "应对措施": ["", "", "", "", ""]
}}
```

## 风险传导机制
[分析各类风险如何相互影响和传导]

## 风险预警指标
### 政策风险指标
- [列出政策风险的预警指标]

### 市场风险指标
- [列出市场风险的预警指标]

### 技术风险指标
- [列出技术风险的预警指标]

### 财务风险指标
- [列出财务风险的预警指标]

## 风险应对策略
### 风险规避
- [可以规避的风险和方法]

### 风险缓解
- [风险缓解措施和方法]

### 风险转移
- [风险转移的途径和方法]

### 风险承受
- [必须承受的风险和管理方法]

## 投资者风险提示
**重要提醒：**
1. 行业投资存在较大不确定性，投资者应充分了解风险
2. 政策变化可能对行业发展产生重大影响
3. 技术进步可能导致现有商业模式被颠覆
4. 市场竞争加剧可能影响企业盈利能力
5. 建议投资者分散投资，控制行业配置比例
6. 密切关注行业发展动态和风险变化

要求：
1. 风险识别全面、准确
2. 风险评估客观、量化
3. 应对措施具体、可行
4. 语言严谨、负责任
"""

        response = await self.llm_client.async_chat_completion([
            {"role": "system", "content": "你是一位专业的行业风险分析师，具有丰富的风险识别和评估经验。"},
            {"role": "user", "content": prompt}
        ], temperature=0.1)

        return response.get("content", "")
    
    async def generate_industry_charts(self, industry_name: str) -> List[str]:
        """
        生成行业分析图表
        """
        try:
            import matplotlib.pyplot as plt
            import numpy as np
            
            chart_paths = []
            
            # 1. 市场规模趋势图
            plt.figure(figsize=(12, 8))
            years = np.arange(2019, 2024)
            market_size = [100, 120, 135, 155, 180]  # 示例数据
            
            plt.subplot(2, 2, 1)
            plt.plot(years, market_size, 'bo-', linewidth=2, markersize=8)
            plt.title(f'{industry_name}市场规模趋势')
            plt.xlabel('年份')
            plt.ylabel('市场规模（亿元）')
            plt.grid(True, alpha=0.3)
            
            # 2. 竞争格局饼图
            plt.subplot(2, 2, 2)
            companies = ['龙头企业A', '龙头企业B', '龙头企业C', '其他']
            market_share = [30, 25, 20, 25]
            colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99']
            
            plt.pie(market_share, labels=companies, colors=colors, autopct='%1.1f%%', startangle=90)
            plt.title(f'{industry_name}竞争格局')
            
            # 3. 技术发展雷达图
            plt.subplot(2, 2, 3)
            categories = ['技术成熟度', '市场应用', '投资热度', '政策支持', '人才储备']
            values = [0.8, 0.6, 0.9, 0.7, 0.5]  # 示例数据
            
            angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
            values += values[:1]  # 闭合图形
            angles += angles[:1]
            
            ax = plt.subplot(2, 2, 3, projection='polar')
            ax.plot(angles, values, 'o-', linewidth=2)
            ax.fill(angles, values, alpha=0.25)
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(categories)
            ax.set_ylim(0, 1)
            plt.title(f'{industry_name}技术发展雷达图')
            
            # 4. 投资趋势图
            plt.subplot(2, 2, 4)
            investment_years = np.arange(2020, 2024)
            investment_amount = [50, 80, 120, 160]  # 示例数据
            
            plt.bar(investment_years, investment_amount, color='skyblue', alpha=0.7)
            plt.title(f'{industry_name}投资趋势')
            plt.xlabel('年份')
            plt.ylabel('投资金额（亿元）')
            plt.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            chart_path = f"data/outputs/{industry_name}_industry_analysis.png"
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            chart_paths.append(chart_path)
            
            self.logger.info(f"生成行业图表: {chart_paths}")
            return chart_paths
            
        except Exception as e:
            self.logger.error(f"生成行业图表失败: {str(e)}")
            return []

    # 工具函数实现
    async def _tool_web_search(self, query: str, num_results: int = 5) -> str:
        """工具：网络搜索"""
        try:
            self.logger.info(f"🔍 执行网络搜索: {query}")

            # 验证参数
            if not query or not isinstance(query, str):
                return "❌ 搜索查询不能为空"

            if not isinstance(num_results, int) or num_results <= 0:
                num_results = 5

            results = await self.execute_with_retry(
                self.web_search_tool.search, query, num_results=num_results
            )

            if results and isinstance(results, list):
                formatted_results = []
                for i, result in enumerate(results[:num_results], 1):
                    if isinstance(result, dict):
                        title = result.get('title', 'N/A')
                        url = result.get('url', 'N/A')
                        snippet = result.get('snippet', 'N/A')
                        formatted_results.append(f"{i}. {title}\n   URL: {url}\n   摘要: {snippet}")

                if formatted_results:
                    return f"✅ 搜索到 {len(formatted_results)} 条结果：\n\n" + "\n\n".join(formatted_results)
                else:
                    return "❌ 搜索结果格式异常"
            else:
                return "❌ 未找到相关搜索结果"

        except Exception as e:
            self.logger.error(f"网络搜索失败: {str(e)}")
            return f"❌ 搜索失败: {str(e)}"

    async def _tool_get_url_content(self, url: str) -> str:
        """工具：获取URL内容"""
        try:
            self.logger.info(f"📄 获取URL内容: {url}")

            # 验证URL参数
            if not url or not isinstance(url, str):
                return "❌ URL不能为空"

            if not url.startswith(('http://', 'https://')):
                return "❌ URL格式无效，必须以http://或https://开头"

            content = await self.execute_with_retry(
                self.url_content_tool.get_content, url
            )

            if content and isinstance(content, str):
                # 限制内容长度
                if len(content) > 3000:
                    content = content[:3000] + "...[内容已截断]"
                return f"✅ 成功获取URL内容：\n\n{content}"
            else:
                return "❌ 无法获取URL内容或内容为空"

        except Exception as e:
            self.logger.error(f"获取URL内容失败: {str(e)}")
            return f"❌ 获取内容失败: {str(e)}"

    async def _tool_search_knowledge_base(self, query: str) -> str:
        """工具：搜索知识库"""
        try:
            self.logger.info(f"🧠 搜索知识库: {query}")

            # 验证查询参数
            if not query or not isinstance(query, str):
                return "❌ 搜索查询不能为空"

            context = await self.execute_with_retry(
                self.rag_system.get_relevant_context, query
            )

            if context and isinstance(context, str) and context.strip():
                # 限制内容长度
                if len(context) > 2000:
                    context = context[:2000] + "...[内容已截断]"
                return f"✅ 知识库搜索结果：\n\n{context}"
            else:
                return "❌ 知识库中未找到相关信息"

        except Exception as e:
            self.logger.error(f"知识库搜索失败: {str(e)}")
            return f"❌ 知识库搜索失败: {str(e)}"

    async def _tool_add_to_knowledge_base(self, content: str, metadata: dict = None) -> str:
        """工具：添加到知识库"""
        try:
            self.logger.info(f"📚 添加内容到知识库")

            # 验证内容参数
            if not content or not isinstance(content, str):
                return "❌ 内容不能为空"

            if len(content.strip()) < 10:
                return "❌ 内容太短，无法添加到知识库"

            if metadata is None:
                metadata = {"source": "manual_input", "type": "analysis"}
            elif not isinstance(metadata, dict):
                metadata = {"source": "manual_input", "type": "analysis"}

            await self.execute_with_retry(
                self.rag_system.add_document, content, metadata
            )
            return f"✅ 成功添加 {len(content)} 字符的内容到知识库"

        except Exception as e:
            self.logger.error(f"添加到知识库失败: {str(e)}")
            return f"❌ 添加到知识库失败: {str(e)}"

    async def _tool_analyze_market_data(self, data: str, analysis_type: str = "comprehensive") -> str:
        """工具：分析市场数据"""
        try:
            self.logger.info(f"📊 分析市场数据: {analysis_type}")

            # 验证参数
            if not data or not isinstance(data, str):
                return "❌ 市场数据不能为空"

            if len(data.strip()) < 20:
                return "❌ 市场数据太少，无法进行有效分析"

            # 验证分析类型
            valid_types = ["market_size", "competition", "trends", "comprehensive"]
            if analysis_type not in valid_types:
                analysis_type = "comprehensive"

            analysis_prompt = f"""
            作为专业市场分析师，请对以下市场数据进行{analysis_type}分析：

            市场数据：
            {data}

            分析类型：{analysis_type}

            请提供：
            1. 市场规模和增长趋势分析
            2. 竞争格局和市场份额分析
            3. 技术发展和创新趋势
            4. 投资机会和风险评估
            5. 未来发展预测

            请确保分析专业、客观、基于数据。
            """

            response = await self.llm_client.async_chat_completion([
                {"role": "system", "content": "你是一位资深的市场分析师。"},
                {"role": "user", "content": analysis_prompt}
            ], temperature=0.1)

            content = response.get("content", "")
            if content:
                return f"✅ 市场数据分析结果：\n\n{content}"
            else:
                return "❌ 市场数据分析失败，未获得有效结果"

        except Exception as e:
            self.logger.error(f"市场数据分析失败: {str(e)}")
            return f"❌ 市场数据分析失败: {str(e)}"
