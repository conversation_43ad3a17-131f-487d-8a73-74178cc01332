import asyncio
import json
import sys
from typing import Dict, List, Any, Optional, AsyncGenerator
from openai import OpenAI, AsyncOpenAI
from config.settings import Config

class LLMClient:
    """OpenAI v1.0+ 兼容的LLM客户端"""
    
    def __init__(self, config: Config = None):
        self.config = config or Config()
        
        # 初始化同步客户端
        self.client = OpenAI(
            api_key=self.config.OPENAI_API_KEY,
            base_url=self.config.OPENAI_BASE_URL
        )
        
        # 初始化异步客户端
        self.async_client = AsyncOpenAI(
            api_key=self.config.OPENAI_API_KEY,
            base_url=self.config.OPENAI_BASE_URL
        )
    
    def chat_completion(self, messages: List[Dict[str, str]],
                       model: str = None, tools: List[Dict] = None, **kwargs) -> Dict[str, Any]:
        """同步聊天完成，支持function calling"""
        try:
            # 构建请求参数
            request_params = {
                "model": model or self.config.OPENAI_MODEL,
                "messages": messages,
                "temperature": kwargs.get('temperature', 0.5),
                "max_tokens": min(kwargs.get('max_tokens', 4096), 8192),
            }

            # 添加tools参数（如果提供）
            if tools:
                request_params["tools"] = tools
                # 设置tool_choice为auto（让模型自主决定是否调用工具）
                request_params["tool_choice"] = kwargs.get('tool_choice', 'auto')

            # 对于qwen模型，添加特殊配置
            if "qwen" in (model or self.config.OPENAI_MODEL).lower():
                request_params["extra_body"] = {"enable_thinking": False}

            response = self.client.chat.completions.create(**request_params)

            # 解析响应
            message = response.choices[0].message
            result = {
                "content": message.content,
                "usage": response.usage.model_dump() if response.usage else None,
                "model": response.model,
                "success": True,
                "finish_reason": response.choices[0].finish_reason
            }

            # 如果有工具调用，添加到结果中
            if hasattr(message, 'tool_calls') and message.tool_calls:
                result["tool_calls"] = []
                for tool_call in message.tool_calls:
                    result["tool_calls"].append({
                        "id": tool_call.id,
                        "type": tool_call.type,
                        "function": {
                            "name": tool_call.function.name,
                            "arguments": tool_call.function.arguments
                        }
                    })

            return result

        except Exception as e:
            return {
                "content": f"LLM调用失败: {str(e)}",
                "error": str(e),
                "success": False
            }
    
    async def async_chat_completion(self, messages: List[Dict[str, str]],
                                   model: str = None, tools: List[Dict] = None, **kwargs) -> Dict[str, Any]:
        """异步聊天完成，支持function calling"""
        try:
            # 构建请求参数
            request_params = {
                "model": model or self.config.OPENAI_MODEL,
                "messages": messages,
                "temperature": kwargs.get('temperature', 0.5),
                "max_tokens": min(kwargs.get('max_tokens', 4096), 8192),
            }

            # 添加tools参数（如果提供）
            if tools:
                request_params["tools"] = tools
                # 设置tool_choice为auto（让模型自主决定是否调用工具）
                request_params["tool_choice"] = kwargs.get('tool_choice', 'auto')

            # 对于qwen模型，添加特殊配置
            if "qwen" in (model or self.config.OPENAI_MODEL).lower():
                request_params["extra_body"] = {"enable_thinking": False}

            response = await self.async_client.chat.completions.create(**request_params)

            # 解析响应
            message = response.choices[0].message
            result = {
                "content": message.content,
                "usage": response.usage.model_dump() if response.usage else None,
                "model": response.model,
                "success": True,
                "finish_reason": response.choices[0].finish_reason
            }

            # 如果有工具调用，添加到结果中
            if hasattr(message, 'tool_calls') and message.tool_calls:
                result["tool_calls"] = []
                for tool_call in message.tool_calls:
                    result["tool_calls"].append({
                        "id": tool_call.id,
                        "type": tool_call.type,
                        "function": {
                            "name": tool_call.function.name,
                            "arguments": tool_call.function.arguments
                        }
                    })

            return result

        except Exception as e:
            return {
                "content": f"LLM调用失败: {str(e)}",
                "error": str(e),
                "success": False
            }

    async def async_chat_completion_with_tools(self, messages: List[Dict[str, str]],
                                             tools: List[Dict], tool_functions: Dict[str, callable],
                                             model: str = None, max_iterations: int = 5, **kwargs) -> Dict[str, Any]:
        """异步聊天完成，自动处理工具调用循环"""
        current_messages = messages.copy()
        iteration = 0

        while iteration < max_iterations:
            # 调用LLM
            response = await self.async_chat_completion(
                messages=current_messages,
                model=model,
                tools=tools,
                **kwargs
            )

            if not response.get("success"):
                return response

            # 检查是否有工具调用
            if not response.get("tool_calls"):
                # 没有工具调用，返回最终结果
                return response

            # 添加assistant消息到对话历史
            assistant_message = {
                "role": "assistant",
                "content": response.get("content", ""),
            }

            # 如果有工具调用，添加tool_calls
            if response.get("tool_calls"):
                assistant_message["tool_calls"] = response["tool_calls"]

            current_messages.append(assistant_message)

            # 执行工具调用
            for tool_call in response["tool_calls"]:
                function_name = tool_call["function"]["name"]
                function_args = tool_call["function"]["arguments"]

                if function_name in tool_functions:
                    try:
                        # 解析参数
                        import json
                        args = json.loads(function_args)

                        # 调用工具函数
                        if asyncio.iscoroutinefunction(tool_functions[function_name]):
                            tool_result = await tool_functions[function_name](**args)
                        else:
                            tool_result = tool_functions[function_name](**args)

                        # 添加工具结果到对话历史
                        tool_message = {
                            "role": "tool",
                            "content": str(tool_result),
                            "tool_call_id": tool_call["id"]
                        }
                        current_messages.append(tool_message)

                    except Exception as e:
                        # 工具调用失败
                        error_message = {
                            "role": "tool",
                            "content": f"工具调用失败: {str(e)}",
                            "tool_call_id": tool_call["id"]
                        }
                        current_messages.append(error_message)
                else:
                    # 未知工具
                    error_message = {
                        "role": "tool",
                        "content": f"未知工具: {function_name}",
                        "tool_call_id": tool_call["id"]
                    }
                    current_messages.append(error_message)

            iteration += 1

        # 达到最大迭代次数
        return {
            "content": "达到最大工具调用迭代次数",
            "success": False,
            "error": "max_iterations_reached",
            "messages": current_messages
        }

    async def async_chat_completion_stream(self, messages: List[Dict[str, str]],
                                         model: str = None, tools: List[Dict] = None,
                                         show_thinking: bool = False, **kwargs) -> AsyncGenerator[str, None]:
        """异步流式聊天完成"""
        try:
            # 构建请求参数
            request_params = {
                "model": model or self.config.OPENAI_MODEL,
                "messages": messages,
                "temperature": kwargs.get('temperature', 0.5),
                "max_tokens": min(kwargs.get('max_tokens', 4096), 8192),
                "stream": True,
            }

            # 添加tools参数（如果提供）
            if tools:
                request_params["tools"] = tools
                request_params["tool_choice"] = kwargs.get('tool_choice', 'auto')

            # 对于qwen模型，添加特殊配置
            if "qwen" in (model or self.config.OPENAI_MODEL).lower():
                request_params["extra_body"] = {"enable_thinking": show_thinking}

            # 创建流式响应
            stream = await self.async_client.chat.completions.create(**request_params)

            # 处理流式响应
            full_content = ""
            async for chunk in stream:
                if chunk.choices and len(chunk.choices) > 0:
                    delta = chunk.choices[0].delta

                    if hasattr(delta, 'content') and delta.content:
                        content_chunk = delta.content
                        full_content += content_chunk
                        yield content_chunk

                    # 处理工具调用（如果有）
                    if hasattr(delta, 'tool_calls') and delta.tool_calls:
                        for tool_call in delta.tool_calls:
                            if tool_call.function and tool_call.function.name:
                                yield f"\n🔧 调用工具: {tool_call.function.name}\n"
                            if tool_call.function and tool_call.function.arguments:
                                yield f"参数: {tool_call.function.arguments}\n"

        except Exception as e:
            yield f"\n❌ 流式输出错误: {str(e)}\n"

    async def async_chat_completion_with_tools_stream(self, messages: List[Dict[str, str]],
                                                    tools: List[Dict], tool_functions: Dict[str, callable],
                                                    model: str = None, max_iterations: int = 5,
                                                    show_thinking: bool = False, **kwargs) -> AsyncGenerator[str, None]:
        """异步流式聊天完成，自动处理工具调用循环"""
        current_messages = messages.copy()
        iteration = 0

        while iteration < max_iterations:
            yield f"\n💭 第 {iteration + 1} 轮对话开始...\n"

            # 使用非流式调用来避免工具调用解析问题
            try:
                response = await self.async_chat_completion(
                    messages=current_messages,
                    model=model,
                    tools=tools,
                    **kwargs
                )

                if not response.get("success"):
                    yield f"\n❌ LLM调用失败: {response.get('error', 'Unknown error')}\n"
                    return

                # 输出响应内容
                content = response.get("content", "")
                if content:
                    yield content

                # 检查是否有工具调用
                tool_calls = response.get("tool_calls", [])
                if not tool_calls:
                    yield f"\n\n✅ 对话完成！\n"
                    return

                # 添加assistant消息到对话历史
                assistant_message = {
                    "role": "assistant",
                    "content": content,
                }

                if tool_calls:
                    assistant_message["tool_calls"] = tool_calls

                current_messages.append(assistant_message)

                # 执行工具调用
                for tool_call in tool_calls:
                    function_name = tool_call["function"]["name"]
                    function_args = tool_call["function"]["arguments"]

                    yield f"\n\n🔧 执行工具: {function_name}\n"

                    if function_name in tool_functions:
                        try:
                            # 解析参数
                            if isinstance(function_args, str):
                                args = json.loads(function_args)
                            else:
                                args = function_args

                            yield f"📝 参数: {args}\n"

                            # 调用工具函数
                            if asyncio.iscoroutinefunction(tool_functions[function_name]):
                                tool_result = await tool_functions[function_name](**args)
                            else:
                                tool_result = tool_functions[function_name](**args)

                            yield f"✅ 工具执行结果:\n{tool_result}\n"

                            # 添加工具结果到对话历史
                            tool_message = {
                                "role": "tool",
                                "content": str(tool_result),
                                "tool_call_id": tool_call["id"]
                            }
                            current_messages.append(tool_message)

                        except json.JSONDecodeError as e:
                            error_msg = f"❌ 参数解析失败: {str(e)}"
                            yield f"{error_msg}\n"

                            error_message = {
                                "role": "tool",
                                "content": error_msg,
                                "tool_call_id": tool_call["id"]
                            }
                            current_messages.append(error_message)

                        except Exception as e:
                            error_msg = f"❌ 工具调用失败: {str(e)}"
                            yield f"{error_msg}\n"

                            error_message = {
                                "role": "tool",
                                "content": error_msg,
                                "tool_call_id": tool_call["id"]
                            }
                            current_messages.append(error_message)
                    else:
                        error_msg = f"❌ 未知工具: {function_name}"
                        yield f"{error_msg}\n"

                        error_message = {
                            "role": "tool",
                            "content": error_msg,
                            "tool_call_id": tool_call["id"]
                        }
                        current_messages.append(error_message)

            except Exception as e:
                yield f"\n❌ LLM调用失败: {str(e)}\n"
                return

            iteration += 1

        yield f"\n⚠️ 达到最大迭代次数 ({max_iterations})，对话结束\n"
